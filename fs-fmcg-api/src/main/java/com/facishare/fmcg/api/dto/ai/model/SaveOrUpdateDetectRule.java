package com.facishare.fmcg.api.dto.ai.model;

import com.facishare.fmcg.api.dto.abstraction.ArgBase;
import com.facishare.fmcg.api.dto.abstraction.ResultBase;
import com.facishare.fmcg.api.error.FmcgException;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

public interface SaveOrUpdateDetectRule {


    @Data
    @ToString
    @EqualsAndHashCode(callSuper = true)
    class Arg extends ArgBase {

        private AIDetectRuleDTO aiDetectRuleDTO;

        @Override
        public void insure() {

        }

        @Override
        public void validate() throws FmcgException {

        }
    }


    @Data
    @ToString
    @EqualsAndHashCode(callSuper = true)
    class Result extends ResultBase {

        private AIDetectRuleDTO rule;
    }
}
