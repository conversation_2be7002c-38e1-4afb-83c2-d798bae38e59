package com.facishare.fmcg.api.dto.ai.model;


import com.facishare.fmcg.api.dto.abstraction.ArgBase;
import com.facishare.fmcg.api.dto.abstraction.ResultBase;
import com.facishare.fmcg.api.error.FmcgException;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;


public interface BatchQueryModel {


    @Data
    @ToString
    @EqualsAndHashCode(callSuper = true)
    class Arg extends ArgBase {

        private List<QueryParam> queryParams;

        @Override
        public void insure() {

        }

        @Override
        public void validate() throws FmcgException {

        }
    }


    @Data
    @ToString
    @EqualsAndHashCode(callSuper = true)
    class Result extends ResultBase {

        private List<QueryResult> queryResults;
    }

    @Data
    @ToString
    class QueryResult implements Serializable {

        private ModelDTO model;

        private List<AIDetectRuleDTO> rules;

        private List<ObjectMapDTO> objectMaps;

    }

    @Data
    @ToString
    class QueryParam implements Serializable {

        private String modelId;

        private List<String> ruleIds;

        private boolean needObjectMap;

    }
}
