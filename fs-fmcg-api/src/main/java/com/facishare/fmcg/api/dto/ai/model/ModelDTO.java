package com.facishare.fmcg.api.dto.ai.model;

import com.alibaba.fastjson.JSONObject;
import com.facishare.fmcg.api.dto.common.UserInfo;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@ToString
public class ModelDTO implements Serializable {

    private String id;

    private Integer tenantId;

    private String name;

    /**
     * 模型所属场景 display 商品陈列
     */
    private String scene;

    /**
     * 业务模型 商品检测模型：OBJECT_RECOGNITION
     */
    private String type;

    /**
     * 模型类型  LLM  Tradition
     */
    private String parentType;

    /**
     * 模型状态
     * 1 启用 0 禁用
     */
    private int status;

    /**
     * 处理平台 元气森林需要填写此”平台“,其他厂商的后台填写默认值
     */
    private String platform;

    /**
     * 模型产商 baidu,huawei,tu_jiang,sense_time,rio,yqsl
     */
    private String modelManufacturer;

    /**
     * 模型唯一键 百度模型需要
     */
    private String key;

    /**
     * 置信度
     */
    private Double confidence;

    /**
     * 自定义参数
     */
    private JSONObject params;

    /**
     * token 相关 每个厂商不一样 需要从这里取
     *
     * SENSE_TIME: ak, sk, host
     * YQSL: appId,key
     * TU_JIANG: account,password,compCode,secretKey,url
     * RIO: token
     * HUA_WEI:userName,password,domainName,projectName
     * BAI_DU:appKey,secretKey
     */
    private TokenInfoDTO tokenInfo;

    /**
     * 业务能力
     * isOpenProductRowNumber 商品sku/排面数识别
     * isOpenGroupNumber 商品陈列组数
     * isOpenLayerNumber 货架层数识别
     * openSkuUnit 商品单位识别
     * isOpenPrices 价格识别
     * isOpenSceneDetect 商品陈列场景
     * isOpenDisplayForm 商品陈列形式
     */
    private List<String> aiRuleCapabilities;

    /**
     * isShowUnionType
     */
    private Map<String,Integer> objectMapCapabilityMap;

    private UserInfo creator;

    private UserInfo lastModifier;

    private String description;

    private long createTime;

    private long lastModifyTime;

    private boolean isDefault;

    private String apiName;

    /**
     * token 信息拍平
     */

    private String token_identityKey;

    private String token_type;

    private String token_ak;

    private String token_sk;

    private String token_host;

    private String token_appKey;

    private String token_secretKey;

    private String token_userName;
    private String token_password;
    private String token_domainName;
    private String token_projectName;

    private String token_token;

    private String token_secretId;

    private String token_account;

    private String token_compCode;

    private String token_url;

    private String token_appId;

    private String token_key;

    private String token_grantType;

    private String token_secret;

}
