package com.facishare.fmcg.api.service.ai.model;

import com.facishare.fmcg.api.dto.abstraction.ApiArg;
import com.facishare.fmcg.api.dto.abstraction.ApiResult;
import com.facishare.fmcg.api.dto.ai.model.AddModel;
import com.facishare.fmcg.api.dto.ai.model.AddToken;
import com.facishare.fmcg.api.dto.ai.model.BatchDeleteObjectMap;
import com.facishare.fmcg.api.dto.ai.model.BatchQueryAIRuleByIds;
import com.facishare.fmcg.api.dto.ai.model.BatchQueryModel;
import com.facishare.fmcg.api.dto.ai.model.BatchSaveOrUpdateObjectMap;
import com.facishare.fmcg.api.dto.ai.model.DeleteDetectRule;
import com.facishare.fmcg.api.dto.ai.model.GetAIModelScenes;
import com.facishare.fmcg.api.dto.ai.model.GetAIModelsByScene;
import com.facishare.fmcg.api.dto.ai.model.GetAIRuleById;
import com.facishare.fmcg.api.dto.ai.model.GetAIRuleDescribe;
import com.facishare.fmcg.api.dto.ai.model.GetDisplayScenesByModelId;
import com.facishare.fmcg.api.dto.ai.model.GetModelById;
import com.facishare.fmcg.api.dto.ai.model.GetModelDescribe;
import com.facishare.fmcg.api.dto.ai.model.ModelSwitch;
import com.facishare.fmcg.api.dto.ai.model.QueryObjectList;
import com.facishare.fmcg.api.dto.ai.model.QueryRuleList;
import com.facishare.fmcg.api.dto.ai.model.SaveOrUpdateDetectRule;
import com.facishare.fmcg.api.dto.ai.model.UpdateModel;

public interface AIModelService {

    ApiResult<GetAIModelScenes.Result> getAIModelScenes(ApiArg<GetAIModelScenes.Arg> arg);

    ApiResult<GetAIModelsByScene.Result> getAIModelsByScene(ApiArg<GetAIModelsByScene.Arg> arg);

    ApiResult<GetModelById.Result> getModelById(ApiArg<GetModelById.Arg> arg);

    ApiResult<ModelSwitch.Result> modelSwitch(ApiArg<ModelSwitch.Arg> arg);

    ApiResult<AddModel.Result> addModel(ApiArg<AddModel.Arg> arg);

    ApiResult<UpdateModel.Result> updateModel(ApiArg<UpdateModel.Arg> arg);

    ApiResult<GetModelDescribe.Result> getModelDescribe(ApiArg<GetModelDescribe.Arg> arg);

    ApiResult<QueryObjectList.Result> queryObjectList(ApiArg<QueryObjectList.Arg> arg);

    ApiResult<BatchDeleteObjectMap.Result> batchDeleteObjectMap(ApiArg<BatchDeleteObjectMap.Arg> arg);

    ApiResult<BatchSaveOrUpdateObjectMap.Result> batchSaveOrUpdateObjectMap(ApiArg<BatchSaveOrUpdateObjectMap.Arg> arg);

    ApiResult<QueryRuleList.Result> queryRuleList(ApiArg<QueryRuleList.Arg> arg);

    ApiResult<SaveOrUpdateDetectRule.Result> saveOrUpdateDetectRule(ApiArg<SaveOrUpdateDetectRule.Arg> arg);

    ApiResult<DeleteDetectRule.Result> deleteDetectRule(ApiArg<DeleteDetectRule.Arg> arg);

    ApiResult<BatchQueryModel.Result> batchQueryModel(ApiArg<BatchQueryModel.Arg> arg);

    ApiResult<BatchQueryAIRuleByIds.Result> batchQueryAIRuleByIds(ApiArg<BatchQueryAIRuleByIds.Arg> arg);

    /**
     * 根据ID查询单个AI规则
     * @param arg 包含规则ID的请求参数
     * @return 返回单个AI规则的详细信息
     */
    ApiResult<GetAIRuleById.Result> getAIRuleById(ApiArg<GetAIRuleById.Arg> arg);

    /**
     * 获取AI规则描述
     * @param arg 包含租户ID和模型ID的请求参数
     * @return 返回AI规则描述，包括分组、检测能力、字段映射等配置信息
     */
    ApiResult<GetAIRuleDescribe.Result> getAIRuleDescribe(ApiArg<GetAIRuleDescribe.Arg> arg);
    
    /**
     * 根据模型ID获取场景列表
     * @param arg 包含模型ID的请求参数
     * @return 返回场景列表
     */
    ApiResult<GetDisplayScenesByModelId.Result> getDisplayScenesByModelId(ApiArg<GetDisplayScenesByModelId.Arg> arg);

    /**
     * 添加Token信息
     * @param arg 包含Token信息的请求参数
     * @return 返回添加的Token信息
     */
    ApiResult<AddToken.Result> addToken(ApiArg<AddToken.Arg> arg);
}
