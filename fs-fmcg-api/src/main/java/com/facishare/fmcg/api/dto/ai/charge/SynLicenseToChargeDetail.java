package com.facishare.fmcg.api.dto.ai.charge;

import com.facishare.fmcg.api.dto.abstraction.ArgBase;
import com.facishare.fmcg.api.dto.abstraction.ResultBase;
import com.facishare.fmcg.api.error.FmcgException;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2021/9/27 下午2:26
 */
public interface SynLicenseToChargeDetail {

    @Data
    @ToString
    @EqualsAndHashCode(callSuper = true)
    class Arg extends ArgBase {
        private String type = "OBJECT_DETECT";

        @Override
        public void insure() {

        }

        @Override
        public void validate() throws FmcgException {

        }
    }

    @Data
    @ToString
    @EqualsAndHashCode(callSuper = true)
    class Result extends ResultBase {

    }

}
