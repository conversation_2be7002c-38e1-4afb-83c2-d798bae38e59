package com.facishare.fmcg.api.dto.ai.model;

import com.facishare.fmcg.api.dto.abstraction.ArgBase;
import com.facishare.fmcg.api.dto.abstraction.ResultBase;
import com.facishare.fmcg.api.error.ErrorCode;
import com.facishare.fmcg.api.error.FmcgException;

import lombok.Data;

import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

/**
 * 根据ID查询AI规则的DTO
 */
public interface GetAIRuleById {

  @Data
  @EqualsAndHashCode(callSuper = true)
  class Arg extends ArgBase {
    /**
     * AI规则ID
     */
    private String ruleId;

    @Override
    public void insure() {
      // 无需特殊处理
    }

    @Override
    public void validate() throws FmcgException {
      if (StringUtils.isBlank(ruleId)) {
        throw new FmcgException(ErrorCode.PARAM_EMPTY);
      }
    }
  }

  @Data
  @EqualsAndHashCode(callSuper = true)
  class Result extends ResultBase {
    /**
     * AI规则详情
     */
    private AIDetectRuleDTO rule;
  }
}