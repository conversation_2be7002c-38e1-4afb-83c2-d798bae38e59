package com.facishare.fmcg.api.dto.ai.model;

import com.facishare.fmcg.api.dto.abstraction.ArgBase;
import com.facishare.fmcg.api.dto.abstraction.ResultBase;
import com.facishare.fmcg.api.error.ErrorCode;
import com.facishare.fmcg.api.error.FmcgException;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 获取AI规则描述的DTO
 */
public interface GetAIRuleDescribe {

  @Data
  @EqualsAndHashCode(callSuper = true)
  class Arg extends ArgBase {

    /**
     * 模型ID
     */
    private String modelId;

    @Override
    public void insure() {
      // 无需特殊处理
    }

    @Override
    public void validate() throws FmcgException {

      if (StringUtils.isBlank(modelId)) {
        throw new FmcgException(ErrorCode.PARAM_EMPTY);
      }
    }
  }

  @Data
  @ToString
  @EqualsAndHashCode(callSuper = true)
  class Result extends ResultBase {
    /**
     * 分组列表
     */
    private List<GroupItem> groupList;
    
    /**
     * 检测能力列表
     */
    private List<DetectCapabilityItem> detectCapabilityList;
    
    /**
     * 字段映射
     */
    private Map<String, FieldMapItem> fieldsMap;
    
    /**
     * 分组字段关系列表
     */
    private List<GroupFieldRelation> groupFieldRelationList;
    
    /**
     * 检测能力字段关系映射
     */
    private Map<String, List<String>> detectCapabilityFieldRelationMap;
    
    /**
     * 计算类型映射
     */
    private Map<String, CalculateTypeItem> calculateTypesMap;
    
    /**
     * 字段类型映射
     */
    private Map<String, FieldTypeItem> fieldTypeMap;
  }
  
  /**
   * 分组项
   */
  @Data
  class GroupItem implements Serializable {
    /**
     * 标签
     */
    private String label;
    
    /**
     * 值
     */
    private String value;
    
    /**
     * 国际化键
     */
    private String i18nKey;
  }
  
  /**
   * 检测能力项
   */
  @Data
  class DetectCapabilityItem implements Serializable {
    /**
     * 标签
     */
    private String label;
    
    /**
     * 值
     */
    private String value;
    
    /**
     * 国际化键
     */
    private String i18nKey;
  }
  
  /**
   * 字段映射项
   */
  @Data
  class FieldMapItem implements Serializable {
    /**
     * 类型
     */
    private String type;
    
    /**
     * 字段API名称
     */
    private String fieldApiName;
    
    /**
     * 标签
     */
    private String label;
    
    /**
     * 支持的计算类型
     */
    private List<String> supportCalculateTypes;
    
    /**
     * 支持的字段类型
     */
    private List<String> supportFieldType;
    
    /**
     * 是否启用手动输入
     */
    private boolean enableManualInput;
    
    /**
     * 是否启用计算类型
     */
    private boolean enableCalculateType;
    
    /**
     * 国际化键
     */
    private String i18nKey;

    /**
     * 选项值
     */
    private List<Option> options;
  }

  @Data
  @ToString
  class Option implements Serializable{

    private String label;

    private String value;

    private List<String> supportFields;
  }
  
  /**
   * 分组字段关系
   */
  @Data
  class GroupFieldRelation implements Serializable {
    /**
     * 分组
     */
    private String group;
    
    /**
     * 字段列表
     */
    private List<String> fields;
    
    /**
     * 排序
     */
    private int order;
  }
  
  /**
   * 计算类型项
   */
  @Data
  class CalculateTypeItem implements Serializable {
    /**
     * 标签
     */
    private String label;
    
    /**
     * 值
     */
    private String value;
    
    /**
     * 国际化键
     */
    private String i18nKey;
  }
  
  /**
   * 字段类型项
   */
  @Data
  class FieldTypeItem implements Serializable {
    /**
     * 标签
     */
    private String label;
    
    /**
     * 值
     */
    private String value;
    
    /**
     * 国际化键
     */
    private String i18nKey;
  }
} 