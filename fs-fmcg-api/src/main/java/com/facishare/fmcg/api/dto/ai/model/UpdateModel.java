package com.facishare.fmcg.api.dto.ai.model;

import com.facishare.fmcg.api.dto.abstraction.ArgBase;
import com.facishare.fmcg.api.dto.abstraction.ResultBase;
import com.facishare.fmcg.api.error.FmcgException;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

public interface UpdateModel {

  @Data
  @ToString
  @EqualsAndHashCode(callSuper = true)
  class Arg extends ArgBase {

    private ModelDTO model;

    private boolean isUpdateToken;

    @Override
    public void insure() {
      // TODO Auto-generated method stub

    }

    @Override
    public void validate() throws FmcgException {
      // TODO Auto-generated method stub

    }
  }

  @Data
  @ToString
  @EqualsAndHashCode(callSuper = true)
  class Result extends ResultBase {

    private ModelDTO model;

  }
}