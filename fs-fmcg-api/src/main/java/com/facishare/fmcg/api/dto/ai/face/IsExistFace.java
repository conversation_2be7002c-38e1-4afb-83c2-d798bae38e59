package com.facishare.fmcg.api.dto.ai.face;

import com.facishare.fmcg.api.dto.abstraction.ArgBase;
import com.facishare.fmcg.api.dto.abstraction.ResultBase;
import com.facishare.fmcg.api.error.FmcgException;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 19-12-20  上午11:10
 */
public interface IsExistFace {

    @Data
    @ToString
    @EqualsAndHashCode(callSuper = true)
    class Arg extends ArgBase {

        private Integer userId;

        @Override
        public void insure() {

        }

        @Override
        public void validate() throws FmcgException {

        }
    }

    @Data
    @ToString
    @EqualsAndHashCode(callSuper = true)
    class Result extends ResultBase {

        private Integer errorCode = 0;

        private String msg = "SUCCESS";

        private Boolean isExist;
    }
}
