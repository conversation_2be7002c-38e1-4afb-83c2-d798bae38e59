package com.facishare.fmcg.api.dto.ai.model;

import com.facishare.fmcg.api.dto.abstraction.ArgBase;
import com.facishare.fmcg.api.dto.abstraction.ResultBase;
import com.facishare.fmcg.api.error.FmcgException;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

public interface QueryRuleList {

  @Data
  @ToString
  @EqualsAndHashCode(callSuper = true)
  class Arg extends ArgBase {

    private String modelId;

    @Override
    public void insure() {
      // 这里可以添加对 ruleId 的检查逻辑
    }

    @Override
    public void validate() throws FmcgException {
      // 这里可以添加对 ruleId 的验证逻辑
    }
  }
  
  @Data
  @ToString
  @EqualsAndHashCode(callSuper = true)
  class Result extends ResultBase {

    private List<AIDetectRuleDTO> rules;
  }
} 