package com.facishare.fmcg.api.dto.ai.model;


import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
public class ObjectMapDTO implements Serializable {

    private String id;

    private Integer tenantId;

    //唯一键
    private String key;

    //对象apiName
    private String apiName;

    //数据id
    private String objectId;

    private String objectName;

    //模型id
    private String modelId;


    /**
     * 单位类型
     * small 小单位   large 大单位
     */
    private String unitType;

}
