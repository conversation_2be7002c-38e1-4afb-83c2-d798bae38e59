package com.facishare.fmcg.api.dto.ai.model;


import com.facishare.fmcg.api.dto.common.UserInfo;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
@ToString
public class AIDetectRuleDTO implements Serializable {

    private String id;

    private Integer tenantId;

    private String apiName;

    private String promptTemplate;

    private String modelId;

    private String name;

    private String ruleDescribe;

    private String masterDescribeApiName;

    /**
     * 业务能力
     * isOpenProductRowNumber 商品sku/排面数识别
     * isOpenGroupNumber 商品陈列组数
     * isOpenLayerNumber 货架层数识别
     * openSkuUnit 商品单位识别
     * isOpenPrices 价格识别
     * isOpenSceneDetect 商品陈列场景
     * isOpenDisplayForm 商品陈列形式
     * isPOSMDetect 物料识别
     */
    private Map<String, Integer> detectCapabilityMap;

    /**
     * aiPath ai图片
     * productName 产品名称
     * aiRowNumber 商品排面数  需要calculateType属性
     * aiGroupNumber 商品组数  需要calculateType属性
     * aiLayerNumber 层数
     * aiSceneField 场景字段
     * aiUnitField 单位存储字段
     * aiPrices 产品的价格字段
     * productScene 商品的陈列形式
     *
     * displaySceneField 陈列形式字段
     * displayTotalLayerNumber 陈列形式的层数
     * displayCutBoxNumber 陈列形式的割箱数
     * displayMaxVisibleNumber 陈列形式的最大可视数
     * displayAiPath 陈列形式的图片
     *
     * posmDisplayScene posm的陈列形式
     * posmName 物料名称
     * posmNumber 物料数量
     * posmPrices POSM价格
     * posmRelatedSku 附近的sku
     *
     */
    private Map<String, FieldDTO> fieldMap;

    private boolean isDefault;

    private UserInfo creator;

    private UserInfo lastModifier;

    private long createTime;

    private long lastModifyTime;

    private List<String> associatedBizNames = new ArrayList<>();

}
