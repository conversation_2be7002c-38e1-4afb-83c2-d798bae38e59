package com.facishare.fmcg.api.dto.ai.model;

import com.facishare.fmcg.api.dto.abstraction.ArgBase;
import com.facishare.fmcg.api.dto.abstraction.ResultBase;
import com.facishare.fmcg.api.error.FmcgException;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 添加Token DTO
 * <AUTHOR>
 * @date 2024/12/19
 */
public interface AddToken {

    @Data
    @ToString
    @EqualsAndHashCode(callSuper = true)
    class Arg extends ArgBase {

        /**
         * Token信息
         */
        private TokenInfoDTO tokenInfo;

        @Override
        public void insure() {
            // 参数校验逻辑
        }

        @Override
        public void validate() throws FmcgException {
            if (tokenInfo == null) {
                throw new FmcgException("Token信息不能为空", 600101);
            }

        }
    }

    @Data
    @ToString
    @EqualsAndHashCode(callSuper = true)
    class Result extends ResultBase {
        
        /**
         * Token信息
         */
        private TokenInfoDTO tokenInfo;
        
    }
} 