package com.facishare.fmcg.api.dto.ai.model;

import com.facishare.fmcg.api.dto.abstraction.ArgBase;
import com.facishare.fmcg.api.dto.abstraction.ResultBase;
import com.facishare.fmcg.api.error.FmcgException;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public interface GetModelDescribe {


    @Data
    @ToString
    @EqualsAndHashCode(callSuper = true)
    class Arg extends ArgBase {

        private String scene;

        @Override
        public void insure() {
            // TODO Auto-generated method stub
        }

        @Override
        public void validate() throws FmcgException {
            // TODO Auto-generated method stub
        }
    }

    @Data
    @ToString
    @EqualsAndHashCode(callSuper = true)
    class Result extends ResultBase {
        /**
         * 模型厂商列表
         */
        private List<ModelManufacturer> modelManufacturer;

        /**
         * 模型类型列表
         */
        private List<ModelType> type;

        /**
         * 模型信息映射
         */
        private Map<String, List<ModelInfo>> modelInfoMap;

    }

    @Data
    class ModelManufacturer implements Serializable {
        private String name;
        private String value;
        private String i18nKey;
        private List<String> types;
    }

    @Data
    class ModelType implements Serializable {
        private String name;
        private String value;
        private String tip;
        private String i18nKey;
        private String tipI18nKey;
    }

    @Data
    class ModelInfo implements Serializable {
        private String type;
        private List<ModelField> field;
    }

    @Data
    class ModelField implements Serializable {
        private String apiName;
        private String label;
        private String i18nKey;
        private boolean required;
        private String tip;
        private String tipI18nKey;
        private String type;
    }
}
