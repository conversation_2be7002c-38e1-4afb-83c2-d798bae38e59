package com.facishare.fmcg.api.dto.ai.model;

import com.facishare.fmcg.api.dto.abstraction.ArgBase;
import com.facishare.fmcg.api.dto.abstraction.ResultBase;
import com.facishare.fmcg.api.error.FmcgException;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;




public interface BatchSaveOrUpdateObjectMap {

  @Data
  @ToString
  @EqualsAndHashCode(callSuper = true)
  class Arg extends ArgBase {

    private List<ObjectMapDTO> objectMaps;
    
    @Override
    public void insure() {

    }

    @Override
    public void validate() throws FmcgException {

    }

  }
  
  @Data
  @ToString
  @EqualsAndHashCode(callSuper = true)
  class Result extends ResultBase {

    private List<ObjectMapDTO> objectMaps;
  }
}
