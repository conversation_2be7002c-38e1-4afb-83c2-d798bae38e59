package com.facishare.fmcg.api.dto.ai.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class FieldDTO implements Serializable {

    /**
     *  类型 目前仅有 mapping
     */
    private String type;

    /**
     * 字段apiName
     */
    private String fieldKey;

    /**
     * 0 求和
     * 1 最大值
     */
    private Integer  calculateType;

    /**
     * 对象apiName
     */
    private String objectApiName;

    /**
     * 字段类型
     * image
     * select_one
     * object_reference
     * number
     * text
     */
    private String fieldType;

    /**
     * ai存储字段
     */
    private String aiStoreFieldApiName;

    /**
     * 手动存储字段
     */
    private String manuallyStoreFieldApiName;
}
