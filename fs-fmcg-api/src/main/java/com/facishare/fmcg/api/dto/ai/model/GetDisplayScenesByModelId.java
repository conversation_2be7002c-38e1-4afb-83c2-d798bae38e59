package com.facishare.fmcg.api.dto.ai.model;

import com.facishare.fmcg.api.dto.abstraction.ArgBase;
import com.facishare.fmcg.api.dto.abstraction.ResultBase;
import com.facishare.fmcg.api.error.ErrorCode;
import com.facishare.fmcg.api.error.FmcgException;

import java.util.List;

import lombok.*;

import org.springframework.util.StringUtils;

/**
 * 根据模型ID获取场景列表
 */
public interface GetDisplayScenesByModelId {

    @Data
    @ToString
    @EqualsAndHashCode(callSuper = true)
    class Arg extends ArgBase {
        /**
         * 模型ID
         */
        private String modelId;

        @Override
        public void insure() {
            if (modelId == null) {
                modelId = "";
            }
        }

        @Override
        public void validate() throws FmcgException {
            if (StringUtils.isEmpty(modelId)) {
                throw new FmcgException(ErrorCode.PARAM_EMPTY);
            }
        }
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @ToString
    @EqualsAndHashCode(callSuper = true)
    class Result extends ResultBase {
        /**
         * 场景列表
         */
        private List<DisplaySceneDTO> displayScenes;
    }
} 