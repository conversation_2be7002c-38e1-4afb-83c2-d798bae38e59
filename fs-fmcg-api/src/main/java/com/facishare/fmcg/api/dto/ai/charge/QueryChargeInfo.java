package com.facishare.fmcg.api.dto.ai.charge;

import com.facishare.fmcg.api.dto.abstraction.ArgBase;
import com.facishare.fmcg.api.dto.abstraction.ResultBase;
import com.facishare.fmcg.api.error.FmcgException;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/27 下午2:27
 */
public interface QueryChargeInfo {

    @Data
    @ToString
    @EqualsAndHashCode(callSuper = true)
    class Arg extends ArgBase {
        private String type = "OBJECT_DETECT";

        @Override
        public void insure() {

        }

        @Override
        public void validate() throws FmcgException {

        }
    }

    @Data
    @ToString
    @EqualsAndHashCode(callSuper = true)
    class Result extends ResultBase {
        private Long totalChargeCount;
        private Long totalUsedCount;
        private Long residualQuantity;
        private List<ChargeDetail> chargeDetails;
    }

    @Data
    @ToString
    class ChargeDetail implements Serializable{
        private Long chargeTime;
        private Long quantity;
    }
}
