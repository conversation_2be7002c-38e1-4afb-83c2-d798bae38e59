package com.facishare.fmcg.api.dto.ai.model;

import com.facishare.fmcg.api.dto.abstraction.ArgBase;
import com.facishare.fmcg.api.dto.abstraction.ResultBase;
import com.facishare.fmcg.api.error.FmcgException;

import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;


public interface BatchDeleteObjectMap {

  @Data
  @ToString
  @EqualsAndHashCode(callSuper = true)
  class Arg extends ArgBase {
    
    private List<String> ids;

    @Override
    public void insure() {
    
    }

    @Override
    public void validate() throws FmcgException {
      
    }

  }

  @Data
  @ToString
  @EqualsAndHashCode(callSuper = true)
  class Result extends ResultBase {

  }
}
