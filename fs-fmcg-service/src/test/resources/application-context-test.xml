<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd">

    <context:component-scan base-package="com.facishare.fmcg,com.facishare.paas.pod">
        <context:exclude-filter type="annotation" expression="org.springframework.web.bind.annotation.RestController"/>
    </context:component-scan>
    <context:annotation-config/>

    <!--cep-->
    <import resource="classpath:META-INF/fs-cep-plugin.xml"/>
    <import resource="classpath:spring/cms.xml"/>

    <import resource="classpath:spring/ei-ea-converter.xml"/>
    <import resource="classpath:spring/service.xml"/>
    <import resource="classpath:fs-social-api.xml"/>

    <import resource="classpath:fmcg-vision-client.xml"/>

    <!--local-->
    <import resource="classpath:META-INF/provider.xml"/>
    <import resource="classpath:spring/schedule.xml"/>
</beans>
