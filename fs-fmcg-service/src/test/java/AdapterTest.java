import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.fmcg.adapter.ai.AIServiceAdapter;
import com.facishare.fmcg.adapter.config.ConfigFileName;
import com.facishare.fmcg.adapter.config.ConfigService;
import com.facishare.fmcg.adapter.file.FileAdapter;
import com.facishare.fmcg.adapter.interconnection.EnterpriseRelationAdapter;
import com.facishare.fmcg.adapter.organization.OrganizationAdapter;
import com.facishare.fmcg.adapter.redisCache.TPMLocalCache;
import com.facishare.fmcg.api.dto.abstraction.ApiArg;
import com.facishare.fmcg.api.dto.abstraction.ApiResult;
import com.facishare.fmcg.api.dto.ai.model.GetModelById;
import com.facishare.fmcg.api.dto.ai.model.ModelDTO;
import com.facishare.fmcg.api.dto.ai.model.TokenInfoDTO;
import com.facishare.fmcg.api.dto.common.organization.GetSubordinate;
import com.facishare.fmcg.api.dto.common.organization.RoleInit;
import com.facishare.fmcg.api.service.common.OrganizationService;
import com.facishare.fmcg.api.service.common.RoleInitService;
import com.facishare.fmcg.provider.business.abstraction.DescribeBusiness;
import com.facishare.fmcg.provider.dao.abstraction.LicenseDAO;
import com.facishare.fmcg.provider.impl.dev.TenantDevService;
import com.facishare.fmcg.provider.license.handler.TPMLicenseHandler2;
import com.facishare.fmcg.provider.mq.model.LicenseMqObj;
import com.facishare.fmcg.service.schedule.CorrectTPMActivityStatusWorker;
import com.facishare.fmcg.service.schedule.PresetTPMBudgetWorker;
import com.facishare.open.app.center.api.service.OpenAppAdminService;
import com.facishare.open.app.center.api.service.QueryAppAdminService;
import com.facishare.open.common.model.FsUserVO;
import com.facishare.open.common.result.BaseResult;
import com.facishare.restful.client.exception.FRestClientException;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fs.fmcg.sdk.ai.plat.TokenPlatFormEnum;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2020/10/26 下午3:15
 */
public class AdapterTest extends TestBase {

    @Resource
    private FileAdapter fileAdapter;


    @Resource
    private OpenAppAdminService openAppAdminService;

    @Resource
    private QueryAppAdminService queryAppAdminService;

    @Resource
    private ConfigService configService;

    @Resource
    private OrganizationAdapter organizationAdapter;

    @Resource
    private EIEAConverter eieaConverter;

    @Autowired
    private OrganizationService organizationService;

    @Resource
    private EnterpriseEditionService enterpriseEditionService;

    @Resource
    private LicenseDAO licenseDao;

    @Autowired
    private CorrectTPMActivityStatusWorker correctTPMActivityStatusWorker;
    @Autowired
    private PresetTPMBudgetWorker presetTPMBudgetWorker;
    @Resource
    private RoleInitService roleInitService;
    @Resource
    private TPMLicenseHandler2 tpmLicenseHandler2;
    @Resource
    private DescribeBusiness describeBusiness;

    @Resource
    private EnterpriseRelationAdapter enterpriseRelationAdapter;

    @Resource
    private TenantDevService tenantDevService;
    @Resource
    private TPMLocalCache tpmLocalCache;

    @Resource
    private AIServiceAdapter aiServiceAdapter;

    @Test
    public void testWaterMark() throws FRestClientException {
        String[] waterMarks = new String[]{"baifang11", "234234", "2020年10月21日  星期三 15:34", "北京市海淀区知春路甲63号卫星大厦"};
        System.out.println(fileAdapter.waterMark("80063", 1000, waterMarks, "TN_453a0adc66394dfd9afd1b4a21958e8f_tmb"));
        System.out.println("test");
    }

    @Test
    public void licenseDo() throws FRestClientException {
        tpmLicenseHandler2.active(85494, "85494", -10000);
        //describeBusiness.initCashingFieldFor840("[\"85494\"]","false");
    }

    @Test
    public void testInitRole() {
        ApiArg<RoleInit.Arg> arg = new ApiArg<>();
        arg.setTenantId(84966);
        RoleInit.Arg arg2 = new RoleInit.Arg();
        arg2.setTargetTenantId(84966);
        arg.setData(arg2);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("marketingActivitiesManager", "TPMDealerActivityCostObj, TPMDealerActivityCostObj||EditTeamMember, TPMDealerActivityCostObj||Delete, TPMActivityAgreementDetailObj||View, TPMActivityAgreementObj||Abolish, TPMActivityItemCostStandardObj||View, TPMActivityItemCostStandardObj||ChangeBPMApprover, TPMActivityProofDetailObj||IntelligentForm, TPMActivityAgreementObj||Edit, TPMActivityProofAuditDetailObj||StageMoveTo, TPMDealerActivityCostObj||ModifyLog_Recover, TPMActivityObj||StartBPM, TPMActivityObj||ViewApprovalConfig, TPMActivityProofObj, TPMDealerActivityCostObj||ViewApprovalInstanceLog, TPMActivityItemObj||ViewEntireBPM, TPMActivityProofAuditObj||Edit, TPMActivityProofAuditDetailObj||Import, TPMActivityAgreementObj||Relate, TPMActivityObj||PictureAnnexDownload, TPMActivityProofObj||ViewApprovalInstanceLog, TPMActivityProofObj||ViewBPMInstanceLog, TPMActivityItemObj||View, TPMActivityProofObj||Clone, TPMActivityProofAuditDetailObj||View, TPMActivityAgreementObj||EditTeamMember, TPMActivityProofObj||ModifyLog_Recover, TPMActivityProofAuditObj||Export, TPMActivityAgreementObj||PictureAnnexDownload, TPMActivityDetailObj||ViewApprovalConfig, TPMActivityAgreementObj||Delete, TPMActivityStoreObj||ViewEntireBPM, TPMActivityObj||ViewEntireBPM, TPMActivityStoreObj||ViewBPMInstanceLog, TPMActivityObj||Edit, TPMActivityObj||Import, TPMActivityAgreementObj||ChangeBPMApprover, TPMActivityItemCostStandardObj||ViewEntireBPM, TPMActivityProofAuditObj||Print, TPMActivityItemCostStandardObj||ViewApprovalInstanceLog, TPMActivityItemObj||StartBPM, TPMActivityAgreementObj||StartBPM, TPMActivityAgreementObj||StageBackTo, TPMActivityItemObj||StageMoveTo, TPMActivityItemCostStandardObj||Add, TPMActivityAgreementObj||IntelligentForm, TPMActivityProofDetailObj||ViewApprovalInstanceLog, TPMDealerActivityCostObj||Relate, TPMActivityProofAuditObj||IntelligentForm, TPMActivityStoreObj||StageMoveTo, TPMActivityAgreementObj||ModifyLog_Recover, TPMActivityObj||ChangeOwner, TPMDealerActivityCostObj||Clone, TPMDealerActivityCostObj||ChangeBPMApprover, TPMActivityProofObj||Unlock, TPMActivityItemObj||ChangeBPMApprover, TPMActivityItemObj||Unlock, TPMActivityProofDetailObj||PictureAnnexDownload, TPMActivityDetailObj||StopBPM, TPMActivityProofDetailObj||Add, TPMActivityProofAuditObj||ViewApprovalInstanceLog, TPMActivityDetailObj||ViewApprovalInstanceLog, TPMActivityItemObj||ViewApprovalInstanceLog, TPMActivityAgreementDetailObj||IntelligentForm, TPMActivityAgreementDetailObj||ViewApprovalConfig, TPMActivityItemObj||ModifyLog_Recover, TPMActivityProofObj||EditTeamMember, TPMActivityDetailObj||StageMoveTo, TPMActivityProofDetailObj||Relate, TPMActivityAgreementDetailObj||StageBackTo, TPMActivityProofAuditDetailObj||Export, TPMActivityProofAuditObj||StageBackTo, TPMActivityProofAuditObj||StopBPM, TPMActivityProofAuditDetailObj||StartBPM, TPMActivityObj||ViewApprovalInstanceLog, TPMActivityItemObj||Delete, TPMActivityStoreObj||PictureAnnexDownload, TPMActivityProofObj||ChangeOwner, TPMActivityItemCostStandardObj||Print, TPMActivityItemObj||ViewBPMInstanceLog, TPMActivityProofAuditObj||Import, TPMActivityItemObj||Clone, TPMActivityDetailObj||ViewBPMInstanceLog, TPMActivityProofAuditObj||Recover, TPMActivityStoreObj||StopBPM, TPMActivityProofAuditObj||StartBPM, TPMActivityProofAuditObj, TPMActivityAgreementDetailObj||Add, TPMActivityAgreementDetailObj||PictureAnnexDownload, TPMActivityProofObj||StartBPM, TPMActivityAgreementObj||View, TPMActivityDetailObj||Abolish, TPMActivityStoreObj||IntelligentForm, TPMActivityProofAuditDetailObj||ViewBPMInstanceLog, TPMActivityStoreObj||ModifyLog_Recover, TPMActivityStoreObj||ViewApprovalInstanceLog, TPMActivityStoreObj||ViewApprovalConfig, TPMActivityProofAuditObj||Relate, TPMDealerActivityCostObj||StartBPM, TPMActivityAgreementDetailObj||Edit, TPMActivityProofDetailObj||View, TPMActivityItemObj, TPMActivityDetailObj||Relate, TPMActivityObj||Add, TPMActivityProofAuditDetailObj||ChangeBPMApprover, TPMActivityAgreementObj||ViewApprovalConfig, TPMActivityProofObj||Lock, TPMDealerActivityCostObj||Edit, TPMActivityAgreementObj||Recover, TPMActivityProofAuditDetailObj||Abolish, TPMDealerActivityCostObj||StopBPM, TPMActivityDetailObj||View, TPMActivityItemCostStandardObj||ViewBPMInstanceLog, TPMActivityProofAuditObj||Add, TPMActivityAgreementDetailObj||StartBPM, TPMActivityProofAuditObj||ViewApprovalConfig, TPMDealerActivityCostObj||ViewEntireBPM, TPMActivityStoreObj||Add, TPMActivityAgreementDetailObj||Print, TPMActivityItemCostStandardObj||Relate, TPMActivityItemCostStandardObj||ModifyLog_Recover, TPMActivityProofObj||Import, TPMDealerActivityCostObj||Export, TPMActivityProofObj||Print, TPMActivityProofAuditDetailObj||ChangeStageCandidateIds, TPMActivityProofDetailObj||StageBackTo, TPMActivityAgreementDetailObj||Abolish, TPMActivityItemObj||ViewApprovalConfig, TPMActivityProofObj||ViewApprovalConfig, TPMActivityStoreObj||StartBPM, TPMActivityItemCostStandardObj||StartBPM, TPMActivityItemCostStandardObj||EditTeamMember, TPMActivityProofDetailObj||ViewApprovalConfig, TPMActivityProofObj||Add, TPMActivityObj||Export, TPMActivityAgreementDetailObj||Relate, TPMDealerActivityCostObj||StageBackTo, TPMActivityItemObj||Recover, TPMActivityProofDetailObj||StageMoveTo, TPMActivityDetailObj||Add, TPMActivityItemCostStandardObj||Abolish, TPMActivityStoreObj||ChangeBPMApprover, TPMActivityItemObj||Relate, TPMActivityProofAuditObj||EditTeamMember, TPMActivityAgreementObj||StopBPM, TPMDealerActivityCostObj||Unlock, TPMActivityItemObj||ChangeStageCandidateIds, TPMActivityObj||Print, TPMActivityObj||CloseTPMActivity, TPMActivityProofObj||ViewEntireBPM, TPMDealerActivityCostObj||Recover, TPMActivityProofAuditDetailObj||StageBackTo, TPMActivityItemObj||ChangeOwner, TPMActivityProofAuditDetailObj||Print, TPMActivityObj||Clone, TPMActivityStoreObj||ChangeStageCandidateIds, TPMActivityItemCostStandardObj||ViewApprovalConfig, TPMActivityProofAuditDetailObj||Add, TPMDealerActivityCostObj||ViewBPMInstanceLog, TPMActivityProofObj||Abolish, TPMActivityObj||StopBPM, TPMActivityStoreObj||Export, TPMActivityProofDetailObj||Abolish, TPMActivityObj||Delete, TPMActivityProofObj||Export, TPMActivityAgreementDetailObj||ViewEntireBPM, TPMDealerActivityCostObj||Import, TPMActivityProofAuditObj||ViewEntireBPM, TPMActivityObj||EditTeamMember, TPMActivityObj||Unlock, TPMActivityProofDetailObj||ModifyLog_Recover, TPMActivityObj||Lock, TPMActivityAgreementObj||ChangeStageCandidateIds, TPMActivityProofDetailObj||Import, TPMActivityProofObj||View, TPMActivityDetailObj||Export, TPMActivityProofAuditObj||Unlock, TPMActivityProofAuditObj||ChangeStageCandidateIds, TPMDealerActivityCostObj||StageMoveTo, TPMActivityProofAuditObj||TPMProofRandomAudit, TPMActivityItemObj||StopBPM, TPMActivityStoreObj||View, TPMActivityItemObj||Export, TPMActivityAgreementDetailObj, TPMActivityProofAuditObj||Abolish, TPMDealerActivityCostObj||ChangeStageCandidateIds, TPMActivityProofDetailObj||ViewBPMInstanceLog, TPMActivityAgreementObj||Import, TPMActivityItemCostStandardObj||Delete, TPMActivityAgreementObj||Lock, TPMActivityAgreementObj||ViewEntireBPM, TPMActivityProofAuditObj||ChangeBPMApprover, TPMActivityProofObj||ChangeStageCandidateIds, TPMActivityItemCostStandardObj||IntelligentForm, TPMActivityItemCostStandardObj||StageBackTo, TPMActivityObj||ChangeBPMApprover, TPMActivityDetailObj||ModifyLog_Recover, TPMActivityProofAuditDetailObj||Relate, TPMActivityProofDetailObj||Export, TPMActivityAgreementDetailObj||ModifyLog_Recover, TPMActivityDetailObj||Import, TPMActivityProofAuditDetailObj||ModifyLog_Recover, TPMActivityProofAuditObj||Lock, TPMActivityItemCostStandardObj||Unlock, TPMActivityProofDetailObj||Edit, TPMActivityProofAuditDetailObj, TPMActivityAgreementDetailObj||ViewApprovalInstanceLog, TPMDealerActivityCostObj||Add, TPMActivityItemCostStandardObj, TPMDealerActivityCostObj||Print, TPMActivityProofAuditDetailObj||ViewApprovalConfig, TPMActivityItemObj||Add, TPMActivityItemObj||Edit, TPMActivityProofDetailObj||Print, TPMActivityProofAuditDetailObj||Edit, TPMActivityAgreementDetailObj||ChangeBPMApprover, TPMActivityDetailObj||IntelligentForm, TPMActivityObj||ModifyLog_Recover, TPMDealerActivityCostObj||ChangeOwner, TPMActivityProofAuditObj||StageMoveTo, TPMActivityAgreementDetailObj||StageMoveTo, TPMActivityStoreObj||Abolish, TPMActivityItemCostStandardObj||Lock, TPMDealerActivityCostObj||IntelligentForm, TPMActivityItemObj||StageBackTo, TPMActivityDetailObj||Print, TPMActivityObj||Recover, TPMActivityProofAuditObj||Delete, TPMDealerActivityCostObj||View, TPMActivityAgreementObj||ViewBPMInstanceLog, TPMActivityItemCostStandardObj||Clone, TPMActivityProofDetailObj||ViewEntireBPM, TPMActivityItemObj||Import, TPMActivityProofAuditDetailObj||ViewApprovalInstanceLog, TPMActivityDetailObj||StartBPM, TPMActivityItemObj||Print, TPMActivityProofObj||StopBPM, TPMActivityDetailObj||StageBackTo, TPMActivityObj||ViewBPMInstanceLog, TPMActivityStoreObj||Relate, TPMActivityAgreementDetailObj||ViewBPMInstanceLog, TPMActivityItemObj||Abolish, TPMActivityProofObj||Edit, TPMActivityItemObj||IntelligentForm, TPMActivityProofAuditObj||Clone, TPMActivityProofObj||Relate, TPMDealerActivityCostObj||Lock, TPMActivityAgreementObj||Print, TPMActivityItemCostStandardObj||Recover, TPMActivityProofAuditObj||ModifyLog_Recover, TPMActivityItemCostStandardObj||StopBPM, TPMActivityObj||Abolish, TPMActivityStoreObj||Print, TPMActivityItemCostStandardObj||Edit, TPMActivityProofDetailObj||ChangeBPMApprover, TPMActivityProofDetailObj||StartBPM, TPMActivityAgreementObj||Export, TPMActivityItemObj||PictureAnnexDownload, TPMActivityObj||StageMoveTo, TPMActivityAgreementObj||StageMoveTo, TPMActivityProofDetailObj, TPMActivityObj||View, TPMActivityStoreObj||StageBackTo, TPMActivityItemCostStandardObj||ChangeOwner, TPMActivityProofDetailObj||StopBPM, TPMActivityItemCostStandardObj||PictureAnnexDownload, TPMActivityDetailObj||ChangeBPMApprover, TPMActivityItemObj||Lock, TPMActivityItemCostStandardObj||StageMoveTo, TPMActivityAgreementDetailObj||StopBPM, TPMActivityProofAuditObj||View, TPMActivityObj, TPMActivityProofAuditDetailObj||ViewEntireBPM, TPMActivityProofObj||Delete, TPMActivityDetailObj||ViewEntireBPM, TPMActivityStoreObj||Import, TPMActivityAgreementDetailObj||Import, TPMActivityDetailObj||PictureAnnexDownload, TPMActivityProofObj||Recover, TPMActivityStoreObj||Edit, TPMActivityProofObj||PictureAnnexDownload, TPMActivityObj||Relate, TPMActivityProofObj||ChangeBPMApprover, TPMActivityAgreementObj||Unlock, TPMActivityObj||IntelligentForm, TPMActivityStoreObj, TPMActivityAgreementObj||Add, TPMDealerActivityCostObj||Abolish, TPMActivityProofAuditObj||ViewBPMInstanceLog, TPMActivityProofAuditDetailObj||StopBPM, TPMActivityAgreementObj||ViewApprovalInstanceLog, TPMActivityObj||StageBackTo, TPMActivityObj||ChangeStageCandidateIds, TPMActivityAgreementDetailObj||Export, TPMDealerActivityCostObj||ViewApprovalConfig, TPMActivityProofAuditObj||PictureAnnexDownload, TPMActivityAgreementObj||ChangeOwner, TPMActivityDetailObj, TPMActivityItemCostStandardObj||Import, TPMActivityProofAuditDetailObj||IntelligentForm, TPMDealerActivityCostObj||PictureAnnexDownload, TPMActivityAgreementObj||Clone, TPMActivityProofObj||StageBackTo, TPMActivityAgreementObj, TPMActivityItemCostStandardObj||Export, TPMActivityDetailObj||ChangeStageCandidateIds, TPMActivityDetailObj||Edit, TPMActivityItemObj||EditTeamMember, TPMActivityProofObj||IntelligentForm, TPMActivityItemCostStandardObj||ChangeStageCandidateIds, TPMActivityProofAuditObj||ChangeOwner, TPMActivityProofAuditDetailObj||PictureAnnexDownload, TPMActivityProofObj||StageMoveTo, TPMActivityAgreementDetailObj||ChangeStageCandidateIds, TPMActivityProofDetailObj||ChangeStageCandidateIds");

        jsonObject.put("inspector", "TPMActivityProofAuditDetailObj||Add, TPMActivityProofAuditObj||Clone, TPMActivityProofAuditObj||Recover, TPMActivityProofAuditObj||ModifyLog_Recover, TPMActivityProofAuditDetailObj||StageMoveTo, TPMActivityProofAuditObj||StartBPM, TPMActivityProofAuditObj, TPMActivityProofAuditObj||ViewEntireBPM, TPMActivityProofAuditDetailObj||ViewBPMInstanceLog, TPMActivityProofAuditObj||Relate, TPMActivityProofAuditObj||Edit, TPMActivityProofAuditObj||Unlock, TPMActivityProofAuditDetailObj||Import, TPMActivityProofAuditObj||ChangeStageCandidateIds, TPMActivityProofAuditObj||TPMProofRandomAudit, TPMActivityProofAuditDetailObj||ChangeBPMApprover, TPMActivityProofAuditObj||Abolish, TPMActivityProofAuditDetailObj||Abolish, TPMActivityProofAuditDetailObj||ChangeBPMApprover, TPMActivityProofAuditDetailObj||View, TPMActivityProofAuditObj||Add, TPMActivityProofAuditObj||Export, TPMActivityProofAuditObj||ViewApprovalConfig, TPMActivityProofAuditDetailObj||Relate, TPMActivityProofAuditDetailObj||View, TPMActivityProofAuditDetailObj||ViewEntireBPM, TPMActivityProofAuditDetailObj||ModifyLog_Recover, TPMActivityProofAuditObj||Lock, TPMActivityProofAuditObj||Print, TPMActivityProofAuditDetailObj, TPMActivityProofAuditDetailObj||ChangeStageCandidateIds, TPMActivityProofAuditDetailObj||ViewApprovalConfig, TPMActivityProofAuditObj||IntelligentForm, TPMActivityProofAuditObj||ViewBPMInstanceLog, TPMActivityProofAuditDetailObj||Edit, TPMActivityProofAuditDetailObj||StopBPM, TPMActivityProofAuditDetailObj||PictureAnnexDownload, TPMActivityProofAuditDetailObj||IntelligentForm, TPMActivityProofAuditObj||ViewApprovalInstanceLog, TPMActivityProofAuditDetailObj||StageMoveTo, TPMActivityProofAuditDetailObj||EditTeamMember, TPMActivityProofAuditDetailObj||ChangeOwner, TPMActivityProofAuditDetailObj||StageBackTo, TPMActivityProofAuditDetailObj||Export, TPMActivityProofAuditDetailObj||PictureAnnexDownload, TPMActivityProofAuditDetailObj||Print, TPMActivityProofAuditDetailObj||StageBackTo, TPMActivityProofAuditDetailObj||Delete, TPMActivityProofAuditDetailObj||StartBPM, TPMActivityProofAuditDetailObj||ViewApprovalInstanceLog, TPMActivityProofAuditObj||Import,TPMActivityObj,TPMActivityObj||View");
        jsonObject.put("cityManager", "TPMDealerActivityCostObj, TPMDealerActivityCostObj||EditTeamMember, TPMDealerActivityCostObj||Delete, TPMActivityAgreementDetailObj||View, TPMDealerActivityCostObj||ViewBPMInstanceLog, TPMActivityObj||StopBPM, TPMActivityStoreObj||Export, TPMActivityObj||Delete, TPMDealerActivityCostObj||Import, TPMDealerActivityCostObj||ModifyLog_Recover, TPMActivityObj||EditTeamMember, TPMActivityObj||StartBPM, TPMActivityObj||ViewApprovalConfig, TPMActivityProofObj, TPMActivityObj||Unlock, TPMDealerActivityCostObj||ViewApprovalInstanceLog, TPMActivityObj||Lock, TPMActivityProofObj||View, TPMActivityDetailObj||Export, TPMActivityObj||PictureAnnexDownload, TPMDealerActivityCostObj||StageMoveTo, TPMActivityStoreObj||View, TPMActivityAgreementDetailObj, TPMDealerActivityCostObj||ChangeStageCandidateIds, TPMActivityProofAuditDetailObj||View, TPMActivityObj||ChangeBPMApprover, TPMActivityDetailObj||ViewApprovalConfig, TPMActivityDetailObj||ModifyLog_Recover, TPMActivityStoreObj||ViewEntireBPM, TPMActivityObj||ViewEntireBPM, TPMActivityDetailObj||Import, TPMActivityStoreObj||ViewBPMInstanceLog, TPMActivityObj||Edit, TPMActivityObj||Import, TPMActivityProofAuditDetailObj, TPMDealerActivityCostObj||Add, TPMDealerActivityCostObj||Print, TPMDealerActivityCostObj||Relate, TPMActivityStoreObj||StageMoveTo, TPMActivityObj||ChangeOwner, TPMDealerActivityCostObj||Clone, TPMActivityDetailObj||IntelligentForm, TPMActivityObj||ModifyLog_Recover, TPMDealerActivityCostObj||ChangeBPMApprover, TPMDealerActivityCostObj||ChangeOwner, TPMActivityDetailObj||StopBPM, TPMActivityDetailObj||ViewApprovalInstanceLog, TPMActivityStoreObj||Abolish, TPMDealerActivityCostObj||IntelligentForm, TPMActivityDetailObj||StageMoveTo, TPMActivityDetailObj||Print, TPMActivityObj||Recover, TPMDealerActivityCostObj||View, TPMActivityObj||ViewApprovalInstanceLog, TPMActivityStoreObj||PictureAnnexDownload, TPMActivityDetailObj||StartBPM, TPMActivityDetailObj||StageBackTo, TPMActivityObj||ViewBPMInstanceLog, TPMActivityStoreObj||Relate, TPMActivityDetailObj||ViewBPMInstanceLog, TPMDealerActivityCostObj||Lock, TPMActivityStoreObj||StopBPM, TPMActivityProofAuditObj, TPMActivityAgreementObj||View, TPMActivityObj||Abolish, TPMActivityDetailObj||Abolish, TPMActivityStoreObj||IntelligentForm, TPMActivityStoreObj||Print, TPMActivityStoreObj||ModifyLog_Recover, TPMActivityStoreObj||ViewApprovalInstanceLog, TPMActivityStoreObj||ViewApprovalConfig, TPMDealerActivityCostObj||StartBPM, TPMActivityProofDetailObj||View, TPMActivityObj||StageMoveTo, TPMActivityDetailObj||Relate, TPMActivityObj||Add, TPMActivityProofDetailObj, TPMActivityObj||View, TPMActivityStoreObj||StageBackTo, TPMDealerActivityCostObj||Edit, TPMDealerActivityCostObj||StopBPM, TPMActivityDetailObj||View, TPMActivityDetailObj||ChangeBPMApprover, TPMDealerActivityCostObj||ViewEntireBPM, TPMActivityProofAuditObj||View, TPMActivityStoreObj||Add, TPMActivityObj, TPMDealerActivityCostObj||Export, TPMActivityDetailObj||ViewEntireBPM, TPMActivityStoreObj||Import, TPMActivityDetailObj||PictureAnnexDownload, TPMActivityStoreObj||Edit, TPMActivityObj||Relate, TPMActivityObj||IntelligentForm, TPMActivityStoreObj, TPMActivityStoreObj||StartBPM, TPMDealerActivityCostObj||Abolish, AccountAddrObj, TPMActivityObj||StageBackTo, TPMActivityObj||ChangeStageCandidateIds, TPMDealerActivityCostObj||ViewApprovalConfig, TPMActivityObj||Export, TPMDealerActivityCostObj||StageBackTo, TPMActivityDetailObj, TPMDealerActivityCostObj||PictureAnnexDownload, TPMActivityDetailObj||Add, TPMActivityStoreObj||ChangeBPMApprover, TPMActivityAgreementObj, TPMActivityDetailObj||ChangeStageCandidateIds, TPMActivityDetailObj||Edit, TPMDealerActivityCostObj||Unlock, TPMActivityObj||Print, TPMActivityObj||CloseTPMActivity, TPMDealerActivityCostObj||Recover, TPMActivityObj||Clone, TPMActivityStoreObj||ChangeStageCandidateIds");
        roleInitService.fixInitRolePermission(84966, JSON.toJSONString(jsonObject));
    }

    @Test
    public void testAdminList() {
        com.facishare.open.common.result.BaseResult<List<Integer>> appAdminIds = openAppAdminService.getAppAdminIds("80063", "FSAID_989801");
        System.out.println(appAdminIds);
    }

    @Test
    public void testIsAdminList() {
        FsUserVO fsUser = new FsUserVO();
        fsUser.setEnterpriseAccount("80063");
        fsUser.setUserId(1000);
        fsUser.setUserAccount("E." + "80063" + "." + 1000);
        com.facishare.open.common.result.BaseResult<Boolean> admin = queryAppAdminService.isAppAdmin(fsUser, "FSAID_989801");
        System.out.println(admin);
    }

    @Test
    public void testUpdateAdminList() {
        FsUserVO fsUser = new FsUserVO();
        fsUser.setEnterpriseAccount("80063");
        fsUser.setUserId(1000);
        fsUser.setUserAccount("E." + "80063" + "." + 1000);
        BaseResult<Void> updateAppAdminIds = openAppAdminService.updateAppAdminIds(fsUser, "FSAID_989801", Lists.newArrayList("E.80063.1000", "E.80063.1001"));
        System.out.println(updateAppAdminIds);
    }

    @Test
    public void testGetConfig() {
        Map<String, String> map = configService.get(ConfigFileName.VARIABLES_FMCG_GRAY.getFileName());
        System.out.println(JSON.toJSONString(map));
    }

    @Test
    public void querySubordinateEmployees() {
        ApiArg<GetSubordinate.Arg> apiArg = new ApiArg<>();
        apiArg.setUserId(1055);
        apiArg.setTenantId(78612);

        ApiResult<GetSubordinate.Result> resultApiResult = organizationService.queryMySubordinates(apiArg);
        System.out.println(JSON.toJSONString(resultApiResult));
    }

    @Test
    public void testEA() {

        GetEnterpriseDataArg arg = new GetEnterpriseDataArg();
        arg.setEnterpriseId(78582);
        System.out.println(JSON.toJSON(enterpriseEditionService.getEnterpriseData(arg)));
    }

    @Test
    public void testGetEASimple() {
        correctTPMActivityStatusWorker.run();
    }

    @Test
    public void testPresetTPMBudgetWorker() {
        presetTPMBudgetWorker.run();
    }

    @Test
    public void testIsTemplateTenant() {

        System.out.println(enterpriseRelationAdapter.isConnectTemplateCopyTenant(89789));
    }

    @Test
    public void testIsCurrentCloudTenant() {

        Integer tenantId = 40160001;
        System.out.println("tenantDevService isCurrentCloudTenant:" + tenantDevService.isCurrentCloudTenant(tenantId));
        System.out.println("tenantDevService isMengNiuCloudTenant:" + tenantDevService.isMengNiuCloudTenant(tenantId));
        System.out.println("tenantDevService isCloudTenant:" + tenantDevService.isCloudTenant(tenantId));
    }

    @Test
    public void testTpmRedisCache() {
        TraceContext.get().setTraceId(UUID.randomUUID().toString());
        ArrayList<String> traceId = Lists.newArrayList(TraceContext.get().getTraceId());
        String key = tpmLocalCache.getKey(String.valueOf(84931), TPMLocalCache.KEY_TPM_LICENSE, traceId.toArray(new String[0]));
        System.out.println(key);
        LicenseMqObj licenseMqObj = new LicenseMqObj();
        licenseMqObj.setTenantId("84931");
        licenseMqObj.setLicenseVersion("multi_language_app");
        licenseMqObj.setOrderNumber("90032150uZ5I0hz231Ny158D");
        licenseMqObj.setCreateTime(1726043361576L);
        licenseMqObj.setStartTime(1725984000000L);
        licenseMqObj.setExpiredTime(1793084400000L);
        licenseMqObj.setModuleCodes(Sets.newHashSet("multi_language_app"));
        boolean save = tpmLocalCache.save(key, JSON.toJSONString(licenseMqObj));
        System.out.println(save);
        System.out.println(tpmLocalCache.exists(key));
        System.out.println(tpmLocalCache.get(key));
        System.out.println("json:" + tpmLocalCache.findCacheToJson(key));
        System.out.println("obj:" + tpmLocalCache.findCacheToObj(key, LicenseMqObj.class));
        tpmLocalCache.del(key);
        System.out.println(tpmLocalCache.exists(key));
    }

    @Test
    public void testGetModelList() {
        // 测试获取模型列表
        String scene = "display"; // 测试陈列场景
        List<ModelDTO> models = aiServiceAdapter.getModelList(84931, scene);

        System.out.println("模型列表：" + JSON.toJSONString(models));
        Assert.assertNotNull(models);

    }

    @Test
    public void testAddModel() {
        // 测试添加模型
        ModelDTO modelDTO = new ModelDTO();
        modelDTO.setName("测试模型");
        modelDTO.setScene("display");
        modelDTO.setType("OBJECT_RECOGNITION");
        modelDTO.setPlatform("baidu");
        modelDTO.setTenantId(84931);
        modelDTO.setKey("test_key");
        modelDTO.setConfidence(0.85);
        JSONObject params = new JSONObject();
        params.put("12", "dfdf");
        modelDTO.setParams(params);
        modelDTO.setStatus(1);

        ModelDTO result = aiServiceAdapter.addModel(84931, -10000, modelDTO);

        System.out.println("添加模型结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertEquals("测试模型", result.getName());
    }

    @Test
    public void testOverloadUpdateModel() {
        // 测试更新模型
        ModelDTO modelDTO = JSON.parseObject("{\n" +
                "        \"tenantId\": 78612,\n" +
                "        \"status\": true,\n" +
                "        \"platform\": \"huawei_sku_poc\",\n" +
                "        \"key\": \"niupi\",\n" +
                "        \"creator\": {\n" +
                "            \"id\": -10000,\n" +
                "            \"name\": \"系统\"\n" +
                "        },\n" +
                "        \"lastModifier\": {\n" +
                "            \"id\": -10000,\n" +
                "            \"name\": \"系统\"\n" +
                "        },\n" +
                "        \"createTime\": 1739433070229,\n" +
                "        \"lastModifyTime\": 1739433070229,\n" +
                "        \"token_identityKey\": \"HUAWEI_FSHUAWEICLOUD_EAST_STANDARD\",\n" +
                "        \"token_type\": \"HUAWEI_FSHUAWEICLOUD_EAST_STANDARD\",\n" +
                "        \"token_userName\": \"h******j\",\n" +
                "        \"token_password\": \"j******6\",\n" +
                "        \"token_domainName\": \"f******2\",\n" +
                "        \"token_projectName\": \"c******3\",\n" +
                "        \"token_key\": \"H******D\",\n" +
                "        \"labels\": [\n" +
                "            \"商品名称\",\n" +
                "            \"货架层数\",\n" +
                "            \"排面数\",\n" +
                "            \"陈列位置\"\n" +
                "        ],\n" +
                "        \"id\": \"6088d264cb69647054f21f53\",\n" +
                "        \"description\": \"test\",\n" +
                "        \"modelManufacturer\": \"huawei\",\n" +
                "        \"name\": \"华为SKU模型\",\n" +
                "        \"scene\": \"display\",\n" +
                "        \"type\": \"OBJECT_RECOGNITION\",\n" +
                "        \"params\": {\n" +
                "            \"url\": \"https://fdd1e570f187404fa894e30ce82651e1.apig.cn-east-3.huaweicloudapis.com/v1/infers/5f462257-8029-4df7-8f34-20df4cffad2d/v1/image/shelf-sku-recognition\"\n" +
                "        }\n" +
                "    }", ModelDTO.class);

        ModelDTO result = aiServiceAdapter.overloadUpdateModel(84293,1000, modelDTO);

        System.out.println("更新模型结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertEquals("更新后的模型", result.getName());
    }

    @Test
    public void testModelSwitch() {
        // 测试模型开关
        String modelId = "67af0d9f20e9130001ffb093";

        // 测试启用模型
        //aiServiceAdapter.modelSwitch(84293, modelId, 1);

        // 测试禁用模型
        aiServiceAdapter.modelSwitch(84931, modelId, 0);
    }

    @Test
    public void testGetModelById() {
        // 测试获取模型详情
        String modelId = "6788d5b3b6ab2619671a9a70";
        GetModelById.Result result = aiServiceAdapter.getModelById(84931, modelId, true, true, true);

        System.out.println("模型详情：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertNotNull(result.getModel());
        Assert.assertEquals(modelId, result.getModel().getId());

        // 验证对象映射列表
        if (result.getObjectMaps() != null) {
            Assert.assertFalse(result.getObjectMaps().isEmpty());
        }

    }

    @Test
    public void testGetTokenInfo() {
        // 测试获取百度平台的token信息
        String baiduIdentityKey = "ccfb4f858a5aec2b7a25e982146d2482b40071014ef6473dd001ec875225606d";
        TokenInfoDTO baiduToken = aiServiceAdapter.getTokenInfo(84931, baiduIdentityKey);
        System.out.println("百度平台token信息：" + JSON.toJSONString(baiduToken));

    }

    @Test
    public void testAddTokenInfo() {
        // 测试添加百度平台token信息
        TokenInfoDTO baiduToken = new TokenInfoDTO();
        baiduToken.setType(TokenPlatFormEnum.SENSE_TIME.code());
        baiduToken.setAk("674d20dbd8456122c6048079");
        baiduToken.setSk("dad3354ebd45238f6e94b43d3f6713db");
        baiduToken.setHost("https://sensegalaxy-product.sensetime.com");

        TokenInfoDTO baiduResult = aiServiceAdapter.addTokenInfo(84931, baiduToken);
        System.out.println("添加百度平台token结果：" + JSON.toJSONString(baiduResult));


    }
}

