import com.facishare.fmcg.api.dto.abstraction.ApiArg;
import com.facishare.fmcg.api.dto.abstraction.ApiResult;
import com.facishare.fmcg.api.dto.order.PickingListQuery;
import com.facishare.fmcg.api.service.order.SalesOrderService;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @create 2022 - 03 - 16  11:12 上午
 **/
public class OrderTest extends TestBase{


    @Autowired
    private SalesOrderService salesOrderService;

    @Test
    public void queryPickingListTest(){

        ApiArg<PickingListQuery.Arg> arg = new ApiArg<>();
        PickingListQuery.Arg queryArg = new PickingListQuery.Arg();
        ArrayList<String> list = Lists.newArrayList();
        list.add("621dffc0a5db410001612092");
        list.add("621c7046a5db41000142f432");
        list.add("62188e59f0f6df0001128fb6");
        list.add("6204c74ab78168000145c9ee");
        list.add("6204b615b78168000141799e");
        list.add("6204875eb7816800013df6dc");
        list.add("62039dd0b7816800012d8477");
        list.add("61debeac24bf540001ab5258");
        list.add("61d7ae53f6612c0001728ebf");
        queryArg.setSalesOrderIds(list);
        arg.setUserId(-10000);
        arg.setTenantId(83991);
        arg.setTenantAccount("83991");
        arg.setData(queryArg);
        ApiResult<PickingListQuery.Result> resultApiResult = salesOrderService.queryPickingList(arg);
        System.out.println(resultApiResult);

    }


    @Test
    public void queryPickingListTest2(){

        ApiArg<PickingListQuery.Arg> arg = new ApiArg<>();
        PickingListQuery.Arg queryArg = new PickingListQuery.Arg();
        ArrayList<String> list = Lists.newArrayList();
        list.add("6278d7009928240001553e0c");
        queryArg.setSalesOrderIds(list);
        arg.setUserId(-10000);
        arg.setTenantId(84770);
        arg.setTenantAccount("84770");
        arg.setData(queryArg);
        ApiResult<PickingListQuery.Result> resultApiResult = salesOrderService.queryPickingList(arg);
        System.out.println(resultApiResult);

    }



}
