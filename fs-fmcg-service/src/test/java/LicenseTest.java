import com.alibaba.fastjson.JSON;
import com.facishare.fmcg.adapter.license.LicenseAdapter;
import com.facishare.fmcg.api.dto.abstraction.ApiArg;
import com.facishare.fmcg.api.dto.abstraction.ApiResult;
import com.facishare.fmcg.api.dto.common.license.Active;
import com.facishare.fmcg.api.dto.common.license.Validate;
import com.facishare.fmcg.api.dto.common.organization.StoreWriteOffInit;
import com.facishare.fmcg.api.service.common.LicenseService;
import com.facishare.fmcg.api.service.custom.IBudgetWorkFlowService;
import com.facishare.fmcg.provider.business.abstraction.ActivityUnifiedCaseBusiness;
import com.facishare.fmcg.provider.business.abstraction.DescribeBusiness;
import com.facishare.fmcg.provider.business.abstraction.StoreWriteOffBusiness;
import com.facishare.fmcg.provider.license.AppCodeEnum;
import com.facishare.fmcg.provider.license.handler.*;
import com.facishare.paas.license.Result.ModuleInfoResult;
import com.facishare.paas.license.arg.JudgeModuleArg;
import com.facishare.paas.license.arg.QueryModuleArg;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.common.Result;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.license.pojo.JudgeModulePojo;
import com.facishare.uc.api.model.enterprise.arg.BatchGetSimpleEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.BatchGetSimpleEnterpriseDataResult;
import com.facishare.uc.api.model.fscore.SimpleEnterpriseData;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import org.elasticsearch.common.Strings;
import org.junit.Test;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

public class LicenseTest extends TestBase {

    @Resource
    private BusinessLinkLicenseHandler businessLinkLicenseHandler;


    @Resource
    private TPMCodeLicenseHandler tpmCodeLicenseHandler;
    @Resource
    private TPMLicenseHandler2 tpmLicenseHandler2;

    @Resource
    private TPMBudgetLicenseHandler2 tpmBudgetLicenseHandler2;

    @Resource
    private TPMRandomAuditLicenseHandler2 tpmRandomAuditLicenseHandler2;

    @Resource
    private TPMCostStandardLicenseHandler2 tpmCostStandardLicenseHandler2;

    @Resource
    private LicenseService licenseService;

    @Resource
    private TPMBudgetAccountLicenseHandler tpmBudgetAccountLicenseHandler;


    @Resource
    private TPMLicenseHandler tpmLicenseHandler;

    @Resource
    private LicenseAdapter licenseAdapter;

    @Resource
    private StoreWriteOffBusiness storeWriteOffBusiness;

    @Resource
    private ActivityUnifiedCaseBusiness activityUnifiedCaseBusiness;

    @Resource
    private IBudgetWorkFlowService budgetWorkFlowService;

    @Resource
    private EnterpriseEditionService enterpriseEditionService;

    @Resource
    private DescribeBusiness describeBusiness;

    @Resource(name = "licenseClient")
    private LicenseClient licenseClient;

    @Resource
    private StoreFrontDetectLicenseHandler storeFrontDetectLicenseHandler;

    @Test
    public void businessLinkActiveTest() {
        businessLinkLicenseHandler.active(84931, "84931", 1000);
    }

    @Test
    public void codeTest() {
        tpmCodeLicenseHandler.active(84931, "84931", 1000);
    }

    @Test
    public void tpm2() throws InterruptedException {
        Integer tenantId = 84931;
        String tenantAccount = "84931";
        tpmLicenseHandler2.active(tenantId, tenantAccount, -10000);
        //tpmBudgetLicenseHandler2.active(tenantId, tenantAccount, -10000);
        //tpmRandomAuditLicenseHandler2.active(tenantId, tenantAccount, -10000);
        /* tpmCostStandardLicenseHandler2.active(tenantId, tenantAccount, -10000);*/
        Thread.sleep(5000);
    }

    @Test
    public void tpm1() throws InterruptedException {
        Integer tenantId = 84931;
        String tenantAccount = "84931";
        tpmLicenseHandler.active(tenantId, tenantAccount, -10000);
        //tpmBudgetLicenseHandler2.active(tenantId, tenantAccount, -10000);
        //tpmRandomAuditLicenseHandler2.active(tenantId, tenantAccount, -10000);
        /* tpmCostStandardLicenseHandler2.active(tenantId, tenantAccount, -10000);*/
        Thread.sleep(5000);
    }

    @Test
    public void licenseCenter() {
        TraceContext context = TraceContext.get();
        context.setEa("90910");
        context.setEi("90910");

        ApiArg<Active.Arg> activeApiArg = new ApiArg<>();
        activeApiArg.setTenantAccount("90910");
        activeApiArg.setTenantId(90910);
        activeApiArg.setUserId(-10000);
        Active.Arg arg = new Active.Arg();
        arg.setAppCode(AppCodeEnum.TPM_CODE.code());
        activeApiArg.setData(arg);
        licenseService.active(activeApiArg);
    }

    @Test
    public void validate() {
        ApiArg<Validate.Arg> validateApiArg = new ApiArg<>();
        validateApiArg.setTenantId(84513);
        validateApiArg.setTenantAccount("84513_sandbox");
        validateApiArg.setUserId(-10000);
        Validate.Arg arg = new Validate.Arg();
        arg.setAppCode("FMCG.EFFICIENCY");
        validateApiArg.setData(arg);
        ApiResult<Validate.Result> validate = licenseService.validate(validateApiArg);
        System.out.println(JSON.toJSONString(validate));
    }

    @Test
    public void suatestBudget2() {

        Integer tenantId = 84788;
        String tenantAccount = "84788";
        tpmBudgetAccountLicenseHandler.active(tenantId, tenantAccount, -10000);
    }

    @Test
    public void testLicenseVersion() {

        System.out.println(JSON.toJSONString(licenseAdapter.get(78581, "trade_promotion_management_app")));
    }

    @Test
    public void addStoreWriteOff() {
        StoreWriteOffInit.Arg arg = new StoreWriteOffInit.Arg();
        arg.setTenantIds("84931");
        arg.setSource("");
        storeWriteOffBusiness.addStoreWriteOff(arg);
    }

    @Test
    public void updateField() {
//        StoreWriteOffInit.Arg arg = new StoreWriteOffInit.Arg();
//        arg.setTenantIds("80063");
//        arg.setSource("");
//        arg.setObjectApiName("TPMStoreWriteOffObj");
//        arg.setFieldName("activity_type");
        activityUnifiedCaseBusiness.modifyField(84931, "", new String[]{"1", "2"});
    }

    @Test
    public void addUnifiedCase() {
        StoreWriteOffInit.Arg arg = new StoreWriteOffInit.Arg();
        arg.setTenantIds("84846");
        arg.setSource("other");
        arg.setApiNames("TPMActivityUnifiedCaseObj,TPMActivityDealerScopeObj,TPMActivityCashingProductScopeObj");
        describeBusiness.initUnifiedCaseFor850(arg);

    }

    @Test
    public void updateUnifiedCaseIdFieldLayout() {
//        StoreWriteOffInit.Arg arg = new StoreWriteOffInit.Arg();
//        arg.setTenantIds("84274");
//        arg.setSource("");
//        arg.setApiNames("TPMActivityUnifiedCaseObj,TPMActivityDealerScopeObj,TPMActivityCashingProductScopeObj");
//        describeBusiness.initUnifiedCaseFor850(arg);
//84274.1000
        describeBusiness.modifyFieldNameFor850("88181", "", "");
    }

    @Test
    public void initRoleByMould(){

        describeBusiness.initRoleCopyByMould("84274", "88181", "marketingActivitiesManager");
    }



    @Test
    public void addField() {
//        StoreWriteOffInit.Arg arg = new StoreWriteOffInit.Arg();
//        arg.setTenantIds("80063");
//        arg.setSource("");
//        arg.setObjectApiName("TPMStoreWriteOffObj");
//        arg.setFieldName("activity_type");
        activityUnifiedCaseBusiness.addRefField(84931);
    }

    @Test
    public void initWorkflow() {
        budgetWorkFlowService.initWorkflow(80063, "TPMBudgetTransferDetailObj");
    }

    @Test
    public void getEnv() {
        isFoneShareEnv(84931);
        System.out.println(getProperty());
    }

    private boolean isFoneShareEnv(int tenantId) {
        BatchGetSimpleEnterpriseDataArg arg = new BatchGetSimpleEnterpriseDataArg();
        arg.setEnterpriseIds(Lists.newArrayList(tenantId));

        BatchGetSimpleEnterpriseDataResult batchGetSimpleEnterpriseByRunStatusResult = enterpriseEditionService.batchGetSimpleEnterpriseData(arg);
        List<SimpleEnterpriseData> enterpriseData = batchGetSimpleEnterpriseByRunStatusResult.getSimpleEnterpriseList();

        if (!CollectionUtils.isEmpty(enterpriseData)) {
            Integer env = enterpriseData.stream().filter(simpleEnterpriseData -> tenantId == simpleEnterpriseData.getEnterpriseId()).map(SimpleEnterpriseData::getEnv).findAny().orElse(0);
            return env == 1;
        }
        return false;
    }


    private String getProperty() {
        String candidates = System.getProperty("process.profile.candidates");
        if (!Strings.isNullOrEmpty(candidates)) {
            String[] properties = candidates.split(",");
            return properties[0];
        } else {
            String env = System.getenv("ENVIRONMENT_TYPE");
            if ("firstshare".equals(env)) {
                return "fstest";
            } else if ("foneshare".equals(env)) {
                return "foneshare";
            } else if (!Strings.isNullOrEmpty(env)) {
                return "cloud";
            }
        }
        return System.getProperty("process.profile");
    }


    @Test
    public void testHB(){
        describeBusiness.handlerMNRecordObjAndFields(Lists.newArrayList(84931));
    }

    @Test
    public void testStoreFrontDetect(){
        storeFrontDetectLicenseHandler.active(83921, "83921", -10000);
    }

    @Test
    public void testJudgement(){
        JudgeModuleArg arg = new JudgeModuleArg();
        LicenseContext licenseContext = new LicenseContext();
        licenseContext.setAppId("CRM");
        licenseContext.setTenantId(String.valueOf(83921));
        licenseContext.setUserId("1000");
        arg.setContext(licenseContext);
        arg.setModuleCodes(Lists.newArrayList("fmcg_ai_store_sign_recognition_app"));
        Result<JudgeModulePojo> result = (Result<JudgeModulePojo>) licenseClient.judgeModule(arg);
        System.out.println("------------------------------");
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void queryModule(){
        LicenseContext licenseContext = new LicenseContext();
        licenseContext.setAppId("CRM");
        licenseContext.setTenantId(String.valueOf(83921));
        licenseContext.setUserId("1000");
        QueryModuleArg arg = new QueryModuleArg();
        arg.setLicenseContext(licenseContext);
        ModuleInfoResult moduleInfoResult = licenseClient.queryModule(arg);

        System.out.println(JSON.toJSONString(moduleInfoResult));
    }
}
