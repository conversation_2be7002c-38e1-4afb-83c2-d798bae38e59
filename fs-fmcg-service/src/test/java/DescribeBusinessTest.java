import com.facishare.fmcg.adapter.metadata.dto.field.Delete;
import com.facishare.fmcg.provider.business.abstraction.DescribeBusiness;
import com.facishare.fmcg.provider.business.model.OrderByBO;
import com.facishare.fmcg.provider.business.model.PureDescribeBO;
import com.google.common.collect.Lists;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/31 下午7:20
 */
public class DescribeBusinessTest extends TestBase {

    @Resource
    private DescribeBusiness describeBusiness;

    @Test
    public void testCreateDescribe() {

//        String apiName = "TPMActivityObj";
//        PureDescribeBO bo = describeBusiness.getPureDescribeFromTargetTenant(84364, apiName, Lists.newArrayList("budget_table", "available_amount"));
//
//        describeBusiness.createDescribe(84392, apiName, bo.getDescribe(), bo.getLayouts(), bo.getListLayout());

//        describeBusiness.handlerMNWithdrawObjAndFields(Lists.newArrayList(82958), Lists.newArrayList("WithdrawRecordObj"), Lists.newArrayList());


//        describeBusiness.initObjForPhysicalRewards(Lists.newArrayList(78612), Lists.newArrayList("TPMActivityPrizesObj"));
//
//        describeBusiness.createRecordTypeLayoutForRewards(Lists.newArrayList(78612));
//        describeBusiness.initObjectDescription(Lists.newArrayList(84931), Lists.newArrayList("TPMBudgetProvisionObj"));

        describeBusiness.initObjectDescription(Lists.newArrayList(85494), Lists.newArrayList("TPMBudgetProvisionObj","TPMBudgetProvisionRecordObj"), "", "");

    }

    @Test
    public void testCopyLayoutRule() {
        describeBusiness.copyLayoutRule(84392, 81767, "TPMActivityObj");
    }

    @Test
    public void testReOrderColumn() {
        describeBusiness.reOrderColumnByCopy(84392, 81767, "TPMActivityObj");
    }

    @Test
    public void testDistributeLayout() {
        describeBusiness.distributeLayoutByCopy(81767, 84392, "TPMActivityObj");
    }

    @Test
    public void testReOrder() {
        describeBusiness.rebuildScene(84931, -10000, "TPMBudgetAccountDetailObj", Lists.newArrayList("name", "budget_account_id", "main_type", "business_type", "amount", "operate_time", "related_object_api_name", "related_object_data_id", "created_by", "create_time", "last_modified_by", "last_modified_time"), Lists.newArrayList(new OrderByBO("operate_time", false)));
    }

    @Test
    public void handleUpdateField() {
//        Map<String, Object> paramMap = new HashMap<>();
//        paramMap.put("default_value", "");
//        paramMap.put("default_is_expression", false);
//        Map<String, Map<String, Object>> map = new HashMap<>();
////        map.put("activity_department_range", paramMap);
////        map.put("start_date", paramMap);
//        map.put("start_date", paramMap);
//        Map<String, Map<String, Map<String, Object>>> twoMap = new HashMap<>();
//        twoMap.put("TPMActivityUnifiedCaseObj", map);
//        System.out.println(JSONObject.toJSONString(twoMap));
//        IF(OR($end_date$ >= DATE(2222,12,31), $start_date$ <= DATE(1970,1,2)),null, $end_date$-$start_date$+1)
        List<Integer> tenantIds = Lists.newArrayList();
        tenantIds.add(84931);
        String updateField = "{\"TPMActivityUnifiedCaseObj\":{\"end_date\":{\"is_required\":false},\"activity_department_range\":{\"is_required\":false},\"start_date\":{\"is_required\":false,\"default_is_expression\":false,\"default_value\":\"\"}},\"TPMActivityObj\":{\"end_date\":{\"is_required\":false},\"multi_department_range\":{\"is_required\":false},\"begin_date\":{\"is_required\":false}},\"TPMDealerActivityCostObj\":{\"end_date\":{\"is_required\":false},\"begin_date\":{\"is_required\":false}},\"TPMActivityAgreementObj\":{\"end_date\":{\"default_value\":\"IF($activity_id__r.end_date$ < DATE(2222,12,31) , $activity_id__r.end_date$, null)\"}}}";

        updateField = "{\"TPMActivityUnifiedCaseObj\":{\"activity_days\":{\"expression\":\"IF(OR($end_date$ >= DATE(2222,12,31), $start_date$ <= DATE(1970,1,2)), null, $end_date$-$start_date$+1)\"}}}";

        updateField = "{\"TPMActivityUnifiedCaseObj\":{\"end_date\":{\"is_required\":false},\"activity_department_range\":{\"is_required\":false},\"start_date\":{\"is_required\":false,\"default_is_expression\":false,\"default_value\":\"\"},\"activity_days\":{\"expression\":\"IF(OR($end_date$ >= DATE(2222,12,31), $start_date$ <= DATE(1970,1,2)), null, $end_date$-$start_date$+1)\"}},\"TPMActivityObj\":{\"end_date\":{\"is_required\":false},\"multi_department_range\":{\"is_required\":false},\"begin_date\":{\"is_required\":false}},\"TPMDealerActivityCostObj\":{\"end_date\":{\"is_required\":false},\"begin_date\":{\"is_required\":false}},\"TPMActivityAgreementObj\":{\"end_date\":{\"default_value\":\"IF($activity_id__r.end_date$ < DATE(2222,12,31) , $activity_id__r.end_date$, null)\"}}}";
        describeBusiness.handlerUpdateFieldFor860(tenantIds, "param", null, updateField);
    }


}
