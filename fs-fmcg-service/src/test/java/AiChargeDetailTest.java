import com.alibaba.fastjson.JSON;
import com.facishare.fmcg.api.dto.abstraction.ApiArg;
import com.facishare.fmcg.api.dto.ai.charge.QueryChargeInfo;
import com.facishare.fmcg.api.dto.ai.charge.SynLicenseToChargeDetail;
import com.facishare.fmcg.api.service.ai.charge.AIChargeDetailService;
import com.facishare.fmcg.provider.dao.enumeration.AIChargeDetailType;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2021/9/27 下午3:51
 */
public class AiChargeDetailTest extends TestBase {

    @Resource
    private AIChargeDetailService aiChargeDetailService;

    @Test
    public void testSynDetail(){

        ApiArg<SynLicenseToChargeDetail.Arg> apiArg = new ApiArg<>();
        SynLicenseToChargeDetail.Arg arg = new SynLicenseToChargeDetail.Arg();
        arg.setType(AIChargeDetailType.OBJECT_DETECT.value());
        apiArg.setData(arg);
        apiArg.setTenantId(Integer.parseInt("81959"));
        apiArg.setUserId(-10000);
        apiArg.setTenantAccount("81959");
        aiChargeDetailService.synLicenseToChargeDetail(apiArg);
    }

    @Test
    public void testQueryChargeInfo(){

        ApiArg<QueryChargeInfo.Arg> apiArg = new ApiArg<>();
        QueryChargeInfo.Arg arg = new QueryChargeInfo.Arg();
        arg.setType(AIChargeDetailType.OBJECT_DETECT.value());
        apiArg.setData(arg);
        apiArg.setTenantId(Integer.parseInt("84231"));
        apiArg.setUserId(-10000);
        apiArg.setTenantAccount("84231");
        System.out.println(JSON.toJSONString(aiChargeDetailService.queryChargeInfo(apiArg)));
    }
}
