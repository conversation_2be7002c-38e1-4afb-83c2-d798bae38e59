import com.github.jedis.support.MergeJedisCmd;
import org.junit.Test;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/8/4 下午4:23
 */
public class RedisTest extends TestBase {

    @Resource(name = "redisCmd")
    private   MergeJedisCmd redisCmd ;

    @Resource
    private RedissonClient redissonClient;


    @Test
    public void testHMap() throws InterruptedException {
        String id = "bud_id_1";
        redisCmd.hset(id,"a","b");
        redisCmd.expire(id,10);
        Thread.sleep(1000);
        redisCmd.hset(id,"b","a");
        Thread.sleep(3000);
        System.out.println(redisCmd.ttl(id));
        System.out.println(redisCmd.hgetAll(id));
        Thread.sleep(7000);
        System.out.println(redisCmd.hgetAll(id));
        redisCmd.del(id);
    }

    @Test
    public void testRedis(){

        System.out.println(redisCmd.ttl("REDIS_AI_TOKEN_APPLICATION_KEY:RIO_WECHAT"));
    }


    @Test
    public void testZset(){
        long time = System.currentTimeMillis();
        redisCmd.zadd("test_zset",time/1000*10+1,"aa");
        System.out.println(time/1000*10+1);
        System.out.println(redisCmd.zscore("test_zset","aa").longValue());
    }

    @Test
    public void testRedisson(){

    }

}
