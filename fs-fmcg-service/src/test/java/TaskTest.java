import com.alibaba.fastjson.JSON;
import com.facishare.fmcg.provider.dao.abstraction.TaskDAO;
import com.facishare.fmcg.provider.task.executor.normal.Add860FieldTaskExecutor;
import com.facishare.fmcg.provider.task.executor.normal.UpdateStoreRangeTaskExecutor;
import com.facishare.fmcg.provider.task.po.TaskPO;
import com.facishare.fmcg.provider.task.trigger.BaseExecuteTaskTrigger;
import com.fxiaoke.notifier.support.NotifierClient;
import org.junit.Test;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;

/**
 * Author: linmj
 * Date: 2023/6/2 11:38
 */
public class TaskTest extends TestBase{

    @Resource
    private BaseExecuteTaskTrigger baseExecuteTaskTrigger;

    @Resource
    private TaskDAO taskDAO;


    @Test
    public void testAddRedis(){
        baseExecuteTaskTrigger.addTaskIdToRedis();
    }

    @Test
    public void testExecute(){

    }

    @Test
    public void testRun(){
        TaskPO taskPO = taskDAO.get("6479acf8605a811b047c08f7");
        baseExecuteTaskTrigger.run(taskPO, UpdateStoreRangeTaskExecutor.class);
    }

    @Test
    public void testUpdate(){
        TaskPO taskPO = taskDAO.get("6479b6a030f5c84bc59de38a");
        taskPO.setName("666");
        taskDAO.update(taskPO);
    }

    @Test
    public void  testNotice() throws InterruptedException {
        NotifierClient.register("test_notice", listener -> {
            System.out.println(JSON.toJSONString(listener));
        });

        Thread.sleep(99999000);
    }
}
