import com.facishare.fmcg.adapter.route.DBRouterAdapter;
import com.facishare.fmcg.api.service.tenant.TenantCommonService;
import com.facishare.fmcg.provider.task.annotation.TaskExecutorName;
import org.junit.Test;
import org.reflections.Reflections;

import javax.annotation.Resource;

/**
 * Author: linmj
 * Date: 2023/3/10 16:21
 */
public class testRouter extends TestBase{

    @Resource
    private DBRouterAdapter dbRouterAdapter;

    @Resource
    private TenantCommonService tenantCommonService;

    @Test
    public void testRouter(){

        System.out.println(dbRouterAdapter.existsPGDBRouter("84931"));
    }

    @Test
    public void test(){
        System.out.println(new Reflections("com.facishare.fmcg").getTypesAnnotatedWith(TaskExecutorName.class));
    }

    @Test
    public void testTenantCom(){

        System.out.println(tenantCommonService.judgeIfTenantIsValid(84931));
    }
}
