import com.facishare.fmcg.adapter.metadata.DataAdapter;
import com.facishare.fmcg.adapter.metadata.dto.data.Get;
import com.facishare.fmcg.adapter.metadata.dto.describe.Edit;
import com.fmcg.framework.http.PaasDataProxy;
import com.fmcg.framework.http.contract.paas.data.PaasDataEdit;
import com.google.common.collect.Maps;
import org.junit.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Map;

/**
 * author: wuyx
 * description:
 * createTime: 2023/9/12 15:02
 */
public class MQTest extends TestBase {

    @Resource
    private DataAdapter dataAdapter;

    @Resource
    private PaasDataProxy paasDataProxy;


    @Test
    public void editTest() {
        Get.Result tpmActivityObj = dataAdapter.get(84931, "TPMActivityObj", "650014276655750001b7245f");

        Edit.Arg editArg = new Edit.Arg();

        Map<String, Object> editData = Maps.newHashMap();


        editData.put("_id", "650014276655750001b7245f");
        //editData.put("total_policy_dynamic_amount", BigDecimal.valueOf(1));
        editData.put("remarks", BigDecimal.valueOf(1));
        editArg.setData(editData);
        //editArg.setDetails(Maps.newHashMap());

        PaasDataEdit.Arg updateArg = new PaasDataEdit.Arg();
        updateArg.setData(Maps.newHashMap());

        updateArg.getData().put("_id", "650014276655750001b7245f");
        updateArg.getData().put("total_policy_dynamic_amount", BigDecimal.valueOf(1));
        updateArg.getData().put("remarks", BigDecimal.valueOf(1));

        updateArg.setDetails(Maps.newHashMap());

        PaasDataEdit.Result updateResult = paasDataProxy.edit(84931, -10000, "TPMActivityObj", updateArg);
        System.out.println(updateResult);
    }

}
