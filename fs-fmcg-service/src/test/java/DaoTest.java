import com.alibaba.fastjson.JSON;
import com.facishare.fmcg.provider.dao.abstraction.AccountDetailDAO;
import com.facishare.fmcg.provider.dao.abstraction.MasterDetailDAO;
import com.facishare.fmcg.provider.dao.po.AccountPO;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2020/7/14 下午4:49
 */
public class DaoTest extends TestBase{

    @Resource
    private AccountDetailDAO accountDetailDAO;

    @Resource
    private MasterDetailDAO masterDetailDAO;

    @Test
    public void sumDetail(){
        System.out.println(accountDetailDAO.sumDeductedExpenses("5ed75145d64a530a16d1719f"));
    }

    @Test
    public void add(){
        masterDetailDAO.randomInsert();
    }

    @Test
    public void getList(){
        System.out.println(JSON.toJSONString(masterDetailDAO.getList()));
    }
}
