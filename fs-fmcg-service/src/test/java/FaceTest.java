
import com.alibaba.fastjson.JSON;
import com.facishare.fmcg.api.dto.abstraction.ApiArg;
import com.facishare.fmcg.api.dto.abstraction.ApiResult;
import com.facishare.fmcg.api.dto.ai.config.AdjustEmployee;
import com.facishare.fmcg.api.dto.ai.config.EnableSwitch;
import com.facishare.fmcg.api.dto.ai.config.QueryUsageAndConfig;
import com.facishare.fmcg.api.dto.ai.face.AddFace;
import com.facishare.fmcg.api.dto.ai.face.FaceComparision;
import com.facishare.fmcg.api.dto.ai.face.IsExistFace;
import com.facishare.fmcg.api.dto.common.metadata.AddMetadataFiled;
import com.facishare.fmcg.api.dto.common.organization.AdminInit;
import com.facishare.fmcg.api.dto.common.organization.AppGray;
import com.facishare.fmcg.api.dto.common.organization.CostAssign;
import com.facishare.fmcg.api.dto.custom.ImageDetect;
import com.facishare.fmcg.api.service.ai.config.AISwitchService;
import com.facishare.fmcg.api.service.common.AppGraService;
import com.facishare.fmcg.api.service.common.CostAssignService;
import com.facishare.fmcg.api.service.common.MetadataInnerService;
import com.facishare.fmcg.api.service.common.RoleInitService;
import com.facishare.fmcg.provider.impl.ai.face.FaceServiceImpl;
import com.facishare.fmcg.provider.impl.custom.YuanQiService;
import com.facishare.fmcg.provider.license.handler.BaiduFaceBaseDetectLicenseHandler;
import com.fxiaoke.common.release.GrayRelease;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 19-12-23  上午10:39
 */
public class FaceTest extends TestBase {

    @Resource
    private FaceServiceImpl faceService;

    @Resource
    private MetadataInnerService metadataInnerService;

    @Resource
    private AISwitchService aiSwitchService;

    @Autowired
    protected MergeJedisCmd redisCmd;

    @Resource
    private YuanQiService yuanQiService;

    @Resource
    private RoleInitService roleInitService;

    @Resource
    private AppGraService appGraService;

    @Resource
    private CostAssignService costAssignService;

    @Resource
    private BaiduFaceBaseDetectLicenseHandler baiduFaceBaseDetectLicenseHandler;

    @Test
    public void test() {
      //  faceService.addFaceToAccount(78582, 1051, "N_201912_20_02ac0a65506b48fcb9d05c5ada85fa2a1");
    }

    @Test
    public void detectTest() {
        ApiArg<ImageDetect.Arg> arg = new ApiArg<>();
        arg.setTenantId(80063);
        ImageDetect.Arg data = new ImageDetect.Arg();
        data.setModelId("626a69f7cb69647054a6ccb2");
        data.setPath("N_202204_28_0c0690bb67e34c1898bfe9aaaf21cf82");
        data.setIsPureDetect(true);
        arg.setData(data);

        ApiResult<ImageDetect.Result> result = yuanQiService.imageDetect(arg);
        assert result != null;
    }

    @Test
    public void
    testAddFace() {
        AddFace.Arg arg = new AddFace.Arg();
        arg.setImagePath("TN_2a77b2d390244141a4cebd1e5d5ec43b_tmb");
        ApiArg<AddFace.Arg> argApiArg = new ApiArg<>();
        argApiArg.setData(arg);
        argApiArg.setTenantId(78582);
        argApiArg.setUserId(1011);
        argApiArg.setTenantAccount("78582");
        arg.setUserId(1011);
        System.out.println(JSON.toJSONString(faceService.addFace(argApiArg)));
    }

    @Test
    public void testIsExist() {
        IsExistFace.Arg arg = new IsExistFace.Arg();
        ApiArg<IsExistFace.Arg> argApiArg = new ApiArg<>();
        argApiArg.setData(arg);
        argApiArg.setTenantId(79525);
        argApiArg.setUserId(1000);
        argApiArg.setTenantAccount("79525");
        System.out.println(JSON.toJSONString("---------------------------" + faceService.isExistFace(argApiArg)));
        ;
    }

    @Test
    public void testCompare() {
        FaceComparision.Arg arg = new FaceComparision.Arg();
        ApiArg<FaceComparision.Arg> argApiArg = new ApiArg<>();
        argApiArg.setData(arg);
        argApiArg.setTenantId(78582);
        argApiArg.setUserId(1011);
        argApiArg.setTenantAccount("78582");
        arg.setDetectedFacePath("TN_be69402d2fe845288fbb3ef70b3f59af_tmb");
        String[] waterMarks = new String[] {"jiege666", "----------------", "2020年10月21日  星期三 15:34", "北京市海淀区知春路甲63号卫星大厦"};
        arg.setWords(waterMarks);
        System.out.println(JSON.toJSONString("---------------------------" + faceService.faceComparison(argApiArg)));
    }



    @Test
    public void testA() {
        System.out.println(GrayRelease.isAllow("fmcg", "BDM", 110));
    }


    @Test
    public void testAddField() {
        AddMetadataFiled.Arg arg = new AddMetadataFiled.Arg();
        arg.setApiName("PersonnelObj");
        arg.setFieldKey("PersonnelObj.selfie__c");
        ApiArg<AddMetadataFiled.Arg> argApiArg = new ApiArg<>();
        argApiArg.setData(arg);
        argApiArg.setTenantId(79525);
        argApiArg.setTenantAccount("79525");
        System.out.println(JSON.toJSONString(metadataInnerService.addMetadataField(argApiArg)));
    }

    @Test
    public void testQuery() {
        ApiArg<QueryUsageAndConfig.Arg> argApiArg = new ApiArg<>();
        argApiArg.setTenantAccount("78582");
        argApiArg.setTenantId(78582);
        argApiArg.setUserId(1000);
        QueryUsageAndConfig.Arg arg = new QueryUsageAndConfig.Arg();
        argApiArg.setData(arg);
        System.out.println(JSON.toJSONString(aiSwitchService.queryUsageAndConfig(argApiArg)));
    }

    @Test
    public void testActive() {
        ApiArg<EnableSwitch.Arg> argApiArg = new ApiArg<>();
        argApiArg.setTenantAccount("78582");
        argApiArg.setTenantId(78582);
        argApiArg.setUserId(1000);
        EnableSwitch.Arg arg = new EnableSwitch.Arg();
        arg.setEnable(true);
        arg.setId("5ed60fc1e49907410676fec0");
        argApiArg.setData(arg);
        System.out.println(JSON.toJSONString(aiSwitchService.enableSwitch(argApiArg)));
    }

    @Test
    public void testAddEmp() {
        ApiArg<AdjustEmployee.Arg> argApiArg = new ApiArg<>();
        argApiArg.setTenantAccount("78582");
        argApiArg.setTenantId(78582);
        argApiArg.setUserId(1000);
        AdjustEmployee.Arg arg = new AdjustEmployee.Arg();
        arg.setEmployeeIds(Lists.newArrayList(1000, 1001));
        arg.setDepartmentIds(Lists.newArrayList(999999));
        arg.setId("5ed60fc1e49907410676fec0");
        argApiArg.setData(arg);
        System.out.println(JSON.toJSONString(aiSwitchService.adjustEmployee(argApiArg)));
    }


    @Test
    public void testRedis() {
      /*  redisCmd.transactional((t) -> {
            SetParams setParams = new SetParams();
            setParams.ex(1).nx();
            t.set("test_res", "0", setParams);
            t.incr("test_res");
        });*/
        System.out.println(redisCmd.set("test_res","1"));
    }

    @Test
    public void testPreAdmin() {
        ApiArg<AdminInit.Arg> arg = new ApiArg<>();
        arg.setTenantId(85494);
        arg.setTenantAccount("85494");
        arg.setUserId(1000);
        arg.setData(new AdminInit.Arg());

        ApiResult<AdminInit.Result> resultApiResult = roleInitService.preAdmin(arg);
        System.out.println(resultApiResult);
    }

    @Test
    public void testAddGrayAndInitialization() {
        ApiArg<AppGray.Arg> arg = new ApiArg<>();
        arg.setTenantId(80063);
        arg.setTenantAccount("80063");
        arg.setUserId(1000);
        arg.setData(new AppGray.Arg());

        ApiResult<AppGray.Result> resultApiResult = appGraService.addGrayAndInitialization(arg);
        System.out.println(resultApiResult);
    }

    @Test
    public void testAddGrayAndInitializationTemplate() {
        ApiArg<AppGray.Arg> arg = new ApiArg<>();
        arg.setTenantId(84843);
        arg.setTenantAccount("84843");
        arg.setUserId(1000);
        arg.setData(new AppGray.Arg());

        ApiResult<AppGray.Result> resultApiResult = appGraService.addGrayAndInitializationTemplate(arg);
        System.out.println(resultApiResult);
    }

    @Test
    public void testUpdateTPM2ActivityAuditConfig() {
        ApiArg<AppGray.Arg> arg = new ApiArg<>();
        arg.setTenantId(80063);
        arg.setTenantAccount("80063");
        arg.setUserId(1000);
        arg.setData(new AppGray.Arg());

        ApiResult<AppGray.Result> resultApiResult = appGraService.updateTPM2ActivityAuditConfig(arg);
        System.out.println(resultApiResult);
    }


    @Test
    public void testValidationNeedCostAssign() {
        ApiArg<CostAssign.Arg> arg = new ApiArg<>();
        arg.setTenantId(80063);
        arg.setTenantAccount("80063");
        arg.setUserId(1000);
        arg.setData(new CostAssign.Arg());

        ApiResult<CostAssign.Result> result = costAssignService.validationNeedCostAssign(arg);
        System.out.println(result);
    }

    @Test
    public void testHandler(){
        //baiduFaceBaseDetectLicenseHandler.isExistsField(88417,"PublicEmployee1Obj","owner");
        baiduFaceBaseDetectLicenseHandler.upgrade(84931);
    }

}
