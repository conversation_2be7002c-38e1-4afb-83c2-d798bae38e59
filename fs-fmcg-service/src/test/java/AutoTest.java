import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.fmcg.adapter.metadata.DescribeAdapter;
import com.facishare.fmcg.adapter.metadata.dto.describe.GetDescribe;
import com.facishare.fmcg.provider.impl.auto.AutoInitService;
import com.facishare.fmcg.provider.impl.inner.DataUpdateService;
import com.fmcg.framework.http.PaasDescribeProxy;
import com.fmcg.framework.http.PaasLayoutProxy;
import com.fmcg.framework.http.contract.paas.describe.OptionDependence;
import com.fmcg.framework.http.contract.paas.layout.PaasDisableEditLayout;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/4/12 上午10:30
 */
public class AutoTest extends TestBase {


    @Resource
    private AutoInitService autoInitService;

    @Resource
    private DescribeAdapter describeAdapter;

    @Resource
    private DataUpdateService dataUpdateService;

    @Resource
    private PaasDescribeProxy paasDescribeProxy;

    @Resource
    private PaasLayoutProxy paasLayoutProxy;

    @Resource
    private EIEAConverter eieaConverter;

    @Test
    public void testAutoTPM() {
        autoInitService.autoTPM(88522);
        autoInitService.autoTPM(85494, true);
    }

    @Test
    public void testDescribe() {
        System.out.println(getDescribeObj(80063, "TPMActivityObj"));
    }

    private boolean getDescribeObj(int tenantId, String apiName) {
        GetDescribe.Result tpmActivityObj = describeAdapter.get(tenantId, apiName);
        if (tpmActivityObj.getCode() == 0 && Objects.nonNull(tpmActivityObj.getData())) {
            return true;
        }
        return false;
    }

    @Test
    public void testUpdateDescribe() {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("target_api_name", "WithdrawRecordObj__c");

        Map<String, Map<String, Object>> map = new HashMap<>();
        map.put("withdraw_record_id", paramMap);

        Map<String, Map<String, Map<String, Object>>> twoMap = new HashMap<>();
        twoMap.put("RedPacketRecordObj", map);

        dataUpdateService.updateFieldDescribeWithLayout(89150, twoMap);
    }


    @Test
    public void testExpense() {
        int tenantId = 84931;
        String[] names = {"ExpenseClaimFormObj", "ExpenseClaimFormDetailObj"};
        for (String name : names) {
            PaasDisableEditLayout.Arg arg = new PaasDisableEditLayout.Arg();
            arg.setDescribeApiName(name);
            paasLayoutProxy.disableEditLayout(tenantId, -10000, arg);
        }
        autoInitService.createExpenseClaimObj(tenantId);
    }

    @Test
    public void testOptionDependence() {
        OptionDependence.Arg optionDependenceArg = JSON.parseObject("{\"fieldDependence\":{\"describeApiName\":\"ExpenseClaimFormDetailObj\",\"fieldApiName\":\"record_type\",\"childFieldName\":\"type\",\"dependence\":[{\"value\":\"default__c\",\"childOptions\":[\"5\",\"6\",\"7\",\"other\"]},{\"value\":\"record_P1mpZ__c\",\"childOptions\":[\"1\",\"2\",\"3\",\"4\",\"other\"]},{\"value\":\"record_Ak61I__c\",\"childOptions\":[\"15\",\"16\",\"17\",\"18\",\"other\"]},{\"value\":\"record_0fFNl__c\",\"childOptions\":[\"19\",\"20\",\"21\",\"22\",\"23\",\"other\"]},{\"value\":\"record_Eex1M__c\",\"childOptions\":[\"8\",\"9\",\"10\",\"11\",\"12\",\"13\",\"14\",\"other\"]},{\"value\":\"record_oT4K1__c\",\"childOptions\":[\"24\",\"25\",\"26\",\"27\",\"other\"]}]}}", OptionDependence.Arg.class);
        OptionDependence.Result optionDependenceResult = paasDescribeProxy.createOptionDependence(88417, -10000, optionDependenceArg);
        System.out.println(optionDependenceResult);

    }

    @Test
    public void copyHomePageTPM() {
        String ea = eieaConverter.enterpriseIdToAccount(89405);
        String templateEa = eieaConverter.enterpriseIdToAccount(84274);
        autoInitService.copyCustomPage(89405, ea, 84274, templateEa);
    }


}
