import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.fmcg.adapter.config.ConfigService;
import com.facishare.fmcg.api.dto.common.config.model.TenantConfigKey;
import com.facishare.fmcg.provider.dao.abstraction.TenantConfigDAO;
import com.fxiaoke.common.MapUtils;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.CharMatcher;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.common.Strings;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/3/22 下午6:21
 */
public class ConfigTest extends TestBase {


    @Resource
    private ConfigService configService;

    @Resource
    private EIEAConverter eieaConverter;

    @Resource
    private TenantConfigDAO tenantConfigDAO;

    @Test
    public void testConfig() {
        Integer tenantId = 123456;
        Map<String, String> configMap = configService.get("variables_fmcg_gray");
        String eaGrayList = configMap.getOrDefault("ea_list_fmcg_crm_gray", "");
        String eiGrayList = configMap.getOrDefault("ei_list_fmcg_tpm_gray", "");
        String describeGrayList = configMap.getOrDefault("ei_list_fmcg_tpm_gray_describe", "");
        String tenantAccount = "123456";//eieaConverter.enterpriseIdToAccount(tenantId);

        if (Strings.isNullOrEmpty(eaGrayList) || Strings.isNullOrEmpty(eiGrayList)) {
            throw new RuntimeException("获取路由灰度列表失败");
        }
        Map<String, String> updateMap = new HashMap<>();
        if (!eaGrayList.contains(tenantAccount)) {
            if (eaGrayList.endsWith(",") || Strings.isNullOrEmpty(eaGrayList)) {
                eaGrayList += tenantAccount;
            } else {
                eaGrayList += ',' + tenantAccount;
            }
            updateMap.put("ea_list_fmcg_crm_gray", eaGrayList);
        }
        if (!eiGrayList.contains(String.valueOf(tenantId))) {
            if (eiGrayList.endsWith(",") || Strings.isNullOrEmpty(eiGrayList)) {
                eiGrayList += tenantId;
            } else {
                eiGrayList += "," + tenantId;
            }
            updateMap.put("ei_list_fmcg_tpm_gray", eiGrayList);
        }
        if (!describeGrayList.contains(String.valueOf(tenantId))) {
            if (describeGrayList.endsWith(",") || Strings.isNullOrEmpty(describeGrayList)) {
                describeGrayList += ("\"" + tenantId + "\"");
            } else {
                describeGrayList += "," + ("\"" + tenantId + "\"");
            }
            updateMap.put("ei_list_fmcg_tpm_gray_describe", describeGrayList);
        }
        if (!MapUtils.isNullOrEmpty(updateMap)) {
            configService.updateMulti("variables_fmcg_gray", updateMap);
        }
    }

    @Test
    public void testWebConfig(){
        String ea = "84294";
        String updateString = null;
        String indexKey = "Index4";
        String configName = "fs-web-view-IndexConfig";
        Map<String,String> map =configService.get(configName);
        String list = map.get(indexKey);
        if(!Strings.isNullOrEmpty(list)){
            list = list.trim();
            String preString = ",'";
            if(!list.contains("$")){
                List<String> array = JSON.parseArray(list,String.class);
                if(org.apache.commons.collections4.CollectionUtils.isEmpty(array)){
                    preString="'";
                }
            }
            list = list.substring(0,list.lastIndexOf(']'));
            list += preString+ea+"']";
            updateString = list;
            configService.update(configName,indexKey,updateString);
        }else {
        }

    }

    @Test
    public void testConfig12(){
        tenantConfigDAO.query(78582,JSON.parseArray("[\"STOCK_TICKET_SWITCH\",\"RECOMMEND_SWITCH\",\"RECOMMEND_RATIO\",\"TICKET_SWITCH\",\"FMCG_STORE_AUDIT\",\"KEYBOARD_TYPE\",\"SHOW_NONE_STOCK_PRODUCT\",\"CATEGORY_LEVEL\",\"EXPAND_MULTIPLE_UNIT_INFO\"]",String.class));
    }

    @Test
    public void testConfig13(){
        tenantConfigDAO.set(78582,1000, TenantConfigKey.TICKET_SWITCH.name(),"false");
    }
}
