import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.fmcg.provider.util.QueryUtil;
import com.fmcg.framework.http.contract.paas.data.PaasDataQueryWithFields;
import com.google.common.collect.Lists;
import org.junit.Test;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Author: linmj
 * Date: 2023/6/25 11:54
 */
public class QueryTest extends TestBase{


    @Test
    public void testQuery(){
      /*  PaasDataQueryWithFields.FilterDTO filterDTO = new PaasDataQueryWithFields.FilterDTO("store_range","CONTAINS","CONDITION");

        System.out.println(JSON.toJSONString(QueryUtil.queryAllDataWithFields(84931,-10000, "TPMActivityObj",Lists.newArrayList(filterDTO),Lists.newArrayList("_id","name","store_range"))));
   */
        PaasDataQueryWithFields.FilterDTO filterDTO = new PaasDataQueryWithFields.FilterDTO("store_range", "CONTAINS", "CONDITION");
        List<JSONObject> data = QueryUtil.queryAllDataWithFields(84931, -10000, "TPMActivityObj", Lists.newArrayList(filterDTO), Lists.newArrayList("_id", "name", "store_range"));
        Set<String> set = new HashSet<>();
        data.forEach(activity -> {
            String storeRange = activity.getString("store_range");
            JSONObject object = JSON.parseObject(storeRange);
            if ("CONDITION".equals(object.getString("type"))) {
                JSONArray conditions = JSON.parseArray(object.getString("value"));
                conditions.stream().map(v -> (JSONObject) v).forEach(condition -> {
                    List<JSONObject> filters = condition.getObject("filters", new TypeReference<List<JSONObject>>() {
                    });
                    filters.forEach(filter -> set.add(filter.getString("field_name")));
                });
            }
        });
        System.out.println(JSON.toJSONString(set));
    }
}
