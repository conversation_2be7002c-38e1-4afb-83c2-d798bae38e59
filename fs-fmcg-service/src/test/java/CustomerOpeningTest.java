import com.alibaba.fastjson.JSON;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.fmcg.api.dto.abstraction.ApiArg;
import com.facishare.fmcg.api.dto.common.organization.GetSubordinate;
import com.facishare.fmcg.api.service.common.OrganizationService;
import com.facishare.fmcg.provider.co.excel.ExcelImportFactory;
import com.facishare.fmcg.provider.co.excel.abstraction.ExcelImport;
import com.facishare.fmcg.provider.co.model.ExcelImportArg;
import com.facishare.fmcg.provider.co.service.CustomerOpeningService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/3/4 上午11:21
 */
public class CustomerOpeningTest extends TestBase {

    @Resource
    private CustomerOpeningService  customerOpeningService;

    @Resource
    private ExcelImportFactory excelImportFactory;

    @Autowired
    private OrganizationService organizationService;
    @Test
    public void testIdCardDetect(){
        customerOpeningService.bdmIdCardDetect(78582,"5e6b5323554c5900013d3201");
    }

    @Test
    public void test() throws ParseException {
        Date date =new Date();
        System.out.println(date.getYear());
    }

    @Test
    public void testExcel() throws ParseException, IOException {
        ExcelImport excelImport = excelImportFactory.get("bdmExcelImport");
        ExcelImportArg arg = new ExcelImportArg();
        arg.setTenantAccount("78582");
        arg.setFilePath("N_202003_16_e17b314e9a0243289ff60491358fd9b3");
        Map<String,Object> map = new HashMap<>();
        map.put("masterId","5e6f5566554c590001776ce0");
        arg.setOptions(map);
        arg.setTenantId(78582);
        excelImport.excelImport(arg);
    }

    @Test
    public void testInvoiceDetect() throws ParseException, IOException, AiProviderException {
        customerOpeningService.bdmVatInvoiceDetect(78582,"5e6b5323554c5900013d3201",true);
    }

    @Test
    public void testAI() throws ParseException, IOException, AiProviderException {
        customerOpeningService.pileExpenseReview(78582,"5edb8a4fcf280d00019d30f5");
    }

    @Test
    public void testAI2() throws ParseException, IOException, AiProviderException {
        customerOpeningService.tpmPileAudit(81767,"62738bb2423dbd00011e1e55");
    }
    @Test
    public void testOrg(){
        GetSubordinate.Arg getSubordinate = new GetSubordinate.Arg();
        ApiArg<GetSubordinate.Arg> arg = new ApiArg<>();
        arg.setTenantId(78612);
        arg.setUserId(1000);
        System.out.println(JSON.toJSONString(organizationService.querySubordinates(arg)));;
    }
}
