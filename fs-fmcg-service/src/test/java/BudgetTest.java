import com.facishare.fmcg.provider.license.handler.TPMBudgetAccountLicenseHandler;
import com.fmcg.framework.http.TPMProxy;
import com.fmcg.framework.http.contract.tpm.CommonScript;
import com.google.common.collect.Lists;
import org.junit.Test;

import javax.annotation.Resource;
import java.io.IOException;

public class BudgetTest extends TestBase {

    @Resource
    private TPMProxy tpmProxy;

    @Resource
    private TPMBudgetAccountLicenseHandler budgetAccountLicenseHandler;

    @Test
    public void testInitButton() {

        int tenantId = 90432;
        initButton(tenantId, "init_random_audit_button");
        initButton(tenantId, "init_store_write_off_button");
        initButton(tenantId, "init_close_activity_button");
        initButton(tenantId, "init_close_activity_template_button");
        initButton(tenantId, "init_close_activity_agreement_button");
        initButton(tenantId, "init_budget_statistic_table_refresh_off_button");
        initButton(tenantId, "init_budget_account_button");
        initButton(tenantId, "init_budget_accrual_button");
        initButton(tenantId, "init_carry_forward_retry_button");
        initButton(tenantId, "init_disassembly_retry_button");
    }

    @Test
    public void testInitButtonByTenantId() {
        int tenantId = 84931;
        initButton(tenantId, "init_close_activity_agreement_button");
    }

    private void initButton(Integer tenantId, String actionCode) {
        CommonScript.Arg arg = new CommonScript.Arg();
        arg.setModule(actionCode);
        arg.setTenantIds(Lists.newArrayList(String.valueOf(tenantId)));
        CommonScript.Result result = tpmProxy.commonScript(tenantId, -10000, arg);
    }

    @Test
    public void updateLayOut() {
        try {
            String statisticTableObj = budgetAccountLicenseHandler.upgradeTPMBudgetTransferDetailObjLayout(84931, "TPMBudgetStatisticTableObj");
            System.out.println(statisticTableObj);
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

}
