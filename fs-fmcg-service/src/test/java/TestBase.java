import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {
        "classpath:application-context-test.xml"
})
@WebAppConfiguration
public class TestBase {
    static {
        System.setProperty("process.profile", "fstest");
        System.setProperty("proxy.jump", "yes");
    }
}
