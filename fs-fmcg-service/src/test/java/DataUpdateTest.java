import com.alibaba.fastjson.JSONObject;
import com.facishare.fmcg.provider.impl.inner.DataUpdateService;
import com.fmcg.framework.http.CrmWorkflowProxy;
import com.fmcg.framework.http.contract.workflow.DefinitionInit;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2021/2/4 下午2:30
 */
public class DataUpdateTest extends TestBase{

    @Resource
    private DataUpdateService dataUpdateService;

    @Resource
    private CrmWorkflowProxy crmWorkflowProxy;

    @Test
    public void test(){
        JSONObject arg = new JSONObject();

        arg.put("tenantId",79525);
        arg.put("content","访销");
        arg.put("accountNames", Lists.newArrayList("haocy总店"));
        arg.put("startTime",1610380800000L);
        arg.put("endTime",1611676800000L);
        dataUpdateService.updateCheckinsMonthGoal(arg);
    }

    @Test
    public void testUpdatePersonnel(){
        dataUpdateService.updateSelfieImage("78582");

    }


    @Test
    public void testUpdateDefinition(){

        DefinitionInit.Arg arg = new DefinitionInit.Arg();
        arg.setApiName("TPMDealerActivityCostObj");
        DefinitionInit.Result result = crmWorkflowProxy.definitionInt(88330, -10000, arg);
    }

    @SneakyThrows
    @Test
    public void testAddField(){
        dataUpdateService.agreementVisitFieldAdd();
    }

    @Test
    public void testUpdate(){
        dataUpdateService.updateSubjectDescribe("78612","ChannelObj");
    }

    @Test
    public void testUpdateColor(){
        dataUpdateService.updateOptionColor(Lists.newArrayList("84931"),Lists.newArrayList());
    }
}
