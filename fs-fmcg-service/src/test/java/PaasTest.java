import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fmcg.framework.http.PaasDataProxy;
import com.fmcg.framework.http.contract.paas.data.PaasDataCreate;
import com.google.common.collect.Lists;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/3/19 下午4:16
 */
public class PaasTest extends TestBase {

    @Resource
    private PaasDataProxy paasDataProxy;


    @Test
    public  void testCreatePersonnel(){
        PaasDataCreate.Result result = createDepartment(84293,1000,"TPM","999999");
        System.out.println(JSON.toJSONString(        createPersonnel(84293,1000,"测试人",result.getResult().getObjectData().getInteger("_id"),"11111111111","M","0")));
    }

    private  PaasDataCreate.Result createPersonnel(Integer tenantId, Integer owner, String name, Integer mainDepartmentId, String phone, String sex, String status){
        PaasDataCreate.Arg arg = new PaasDataCreate.Arg();
        JSONObject master = new JSONObject();
        arg.setObjectData(master);
        master.put("owner", Lists.newArrayList(String.valueOf(owner)));
        master.put("record_type", "default__c");
        master.put("name", name);
        master.put("main_department",Lists.newArrayList(String.valueOf(mainDepartmentId)));
        master.put("phone", phone);
        master.put("sex", sex);
        master.put("status", status);
        master.put("is_active", false);
        return paasDataProxy.create(tenantId, -10000, "PersonnelObj", arg);
    }

    private  PaasDataCreate.Result createDepartment(Integer tenantId,Integer owner,String name,String upperId){
        PaasDataCreate.Arg arg = new PaasDataCreate.Arg();
        JSONObject master = new JSONObject();
        arg.setObjectData(master);
        master.put("owner", Lists.newArrayList(String.valueOf(owner)));
        master.put("record_type", "default__c");
        master.put("name", name);
        master.put("parent_id",Lists.newArrayList(upperId));

        return paasDataProxy.create(tenantId, -10000, "DepartmentObj", arg);
    }
}
