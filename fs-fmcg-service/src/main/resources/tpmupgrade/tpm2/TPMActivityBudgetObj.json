{"_id": "61de89ca50c766000143aff4", "api_name": "TPMActivityBudgetObj", "define_type": "package", "display_name": "活动预算管理", "fields": {"tenant_id": {"is_index": false, "is_active": true, "create_time": 1641974218223, "pattern": "", "description": "tenant_id", "is_unique": false, "label": "tenant_id", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "tenant_id", "define_type": "system", "index_name": "ei", "status": "released", "max_length": 200}, "lock_rule": {"describe_api_name": "TPMActivityBudgetObj", "is_index": false, "is_active": true, "create_time": 1641974218220, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "锁定规则", "default_value": "default_lock_rule", "rules": [], "label": "锁定规则", "type": "lock_rule", "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "_id": "61de89ca50c766000143afd2", "is_index_field": false, "is_single": false, "index_name": "s_1", "status": "new", "help_text": ""}, "code": {"describe_api_name": "TPMActivityBudgetObj", "prefix": "{yyyy}{mm}{dd}-", "auto_adapt_places": false, "is_unique": true, "description": "", "start_number": 1, "type": "auto_number", "is_required": false, "define_type": "package", "postfix": "", "is_single": false, "index_name": "s_2", "is_index": true, "auto_number_type": "normal", "is_active": true, "create_time": 1641974218221, "is_encrypted": false, "default_value": "{yyyy}{mm}{dd}-0001", "serial_number": 4, "label": "预算表编号", "condition": "DAY", "api_name": "code", "func_api_name": "", "_id": "61de89ca50c766000143afd3", "is_index_field": false, "status": "new", "help_text": ""}, "available_amount": {"describe_api_name": "TPMActivityBudgetObj", "return_type": "number", "expression_type": "js", "is_index": true, "expression": "$amount$-$frozen_amount$-$used_amount$+$transfer_in_amount$-$transfer_out_amount$", "is_active": true, "create_time": 1653907533750, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "label": "可用预算(元)", "type": "formula", "decimal_places": 2, "default_to_zero": true, "is_required": false, "api_name": "available_amount", "define_type": "package", "_id": "6294a04d399c4f000198b125", "is_index_field": false, "is_single": false, "index_name": "d_2", "status": "new", "help_text": ""}, "budget_department": {"describe_api_name": "TPMActivityBudgetObj", "is_index": true, "is_active": true, "create_time": 1641974218223, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "label": "预算部门", "type": "department", "is_required": true, "api_name": "budget_department", "define_type": "package", "_id": "61de89ca50c766000143afd5", "is_index_field": false, "is_single": true, "index_name": "a_1", "status": "new", "help_text": ""}, "lock_user": {"describe_api_name": "TPMActivityBudgetObj", "is_index": false, "is_active": true, "create_time": 1641974218224, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "加锁人", "where_type": "field", "label": "加锁人", "type": "employee", "is_need_convert": false, "wheres": [], "is_required": false, "api_name": "lock_user", "define_type": "package", "_id": "61de89ca50c766000143afd6", "is_index_field": false, "is_single": true, "index_name": "a_2", "status": "new", "help_text": ""}, "transfer_in_amount": {"describe_api_name": "TPMActivityBudgetObj", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "type": "number", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "index_name": "d_3", "max_length": 14, "is_index": true, "is_active": true, "create_time": 1641974218225, "is_encrypted": false, "length": 12, "default_value": "0", "label": "转入金额", "api_name": "transfer_in_amount", "_id": "61de89ca50c766000143afd7", "is_index_field": false, "round_mode": 4, "status": "new", "help_text": ""}, "extend_obj_data_id": {"describe_api_name": "TPMActivityBudgetObj", "is_index": true, "is_active": true, "create_time": 1641974218226, "is_encrypted": false, "auto_adapt_places": false, "pattern": "", "is_unique": true, "description": "连接通表的记录ID,扩展字段用", "label": "扩展字段在mt_data中的记录ID", "type": "text", "is_required": false, "api_name": "extend_obj_data_id", "define_type": "system", "_id": "61de89ca50c766000143afd8", "is_index_field": false, "is_single": false, "index_name": "t_1", "status": "released", "max_length": 64}, "is_deleted": {"is_index": false, "create_time": 1641974218223, "description": "is_deleted", "is_unique": false, "default_value": false, "label": "is_deleted", "type": "true_or_false", "is_need_convert": false, "is_required": false, "api_name": "is_deleted", "define_type": "system", "index_name": "is_del", "status": "released"}, "life_status_before_invalid": {"describe_api_name": "TPMActivityBudgetObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "description": "作废前生命状态", "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "index_name": "t_2", "max_length": 256, "is_index": false, "is_active": true, "create_time": 1641974218228, "is_encrypted": false, "default_value": "", "label": "作废前生命状态", "is_need_convert": false, "api_name": "life_status_before_invalid", "_id": "61de89ca50c766000143afda", "is_index_field": false, "status": "new", "help_text": ""}, "object_describe_api_name": {"is_index": false, "is_active": true, "create_time": 1641974218223, "pattern": "", "description": "object_describe_api_name", "is_unique": false, "label": "object_describe_api_name", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "object_describe_api_name", "define_type": "system", "index_name": "api_name", "status": "released", "max_length": 200}, "out_owner": {"is_index": false, "is_active": true, "create_time": 1641974218223, "is_unique": false, "label": "外部负责人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "out_owner", "define_type": "system", "is_single": true, "config": {"display": 1}, "index_name": "o_owner", "status": "released"}, "owner_department": {"describe_api_name": "TPMActivityBudgetObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "description": "", "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": true, "index_name": "owner_dept", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1641974218230, "is_encrypted": false, "default_value": "", "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "_id": "61de89ca50c766000143afdc", "is_index_field": false, "status": "new", "help_text": ""}, "transfer_out_amount": {"describe_api_name": "TPMActivityBudgetObj", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "type": "number", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "index_name": "d_7", "max_length": 14, "is_index": true, "is_active": true, "create_time": 1641974218232, "is_encrypted": false, "length": 12, "default_value": "0", "label": "转出金额", "api_name": "transfer_out_amount", "_id": "61de89ca50c766000143afde", "is_index_field": false, "round_mode": 4, "status": "new", "help_text": ""}, "owner": {"describe_api_name": "TPMActivityBudgetObj", "is_index": true, "is_active": true, "create_time": 1641974218233, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "where_type": "field", "label": "负责人", "type": "employee", "is_need_convert": false, "wheres": [], "is_required": true, "api_name": "owner", "define_type": "package", "_id": "61de89ca50c766000143afdf", "is_index_field": false, "is_single": true, "index_name": "owner", "status": "new", "help_text": ""}, "amount": {"describe_api_name": "TPMActivityBudgetObj", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "description": "", "type": "number", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "index_name": "d_1", "max_length": 14, "is_index": true, "is_active": true, "create_time": 1641974218234, "is_encrypted": false, "step_value": 1, "display_style": "input", "length": 12, "default_value": "", "label": "期初预算金额(元)", "api_name": "amount", "_id": "61de89ca50c766000143afe0", "is_index_field": false, "round_mode": 4, "status": "new", "help_text": ""}, "last_modified_time": {"is_index": true, "create_time": 1641974218223, "description": "last_modified_time", "is_unique": false, "label": "最后修改时间", "time_zone": "", "type": "date_time", "is_need_convert": false, "is_required": false, "api_name": "last_modified_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "index_name": "md_time", "status": "released"}, "package": {"is_index": false, "is_active": true, "create_time": 1641974218223, "pattern": "", "description": "package", "is_unique": false, "label": "package", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "package", "define_type": "system", "index_name": "pkg", "status": "released", "max_length": 200}, "lock_status": {"describe_api_name": "TPMActivityBudgetObj", "is_index": true, "is_active": true, "create_time": 1641974218235, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "锁定状态", "default_value": "0", "label": "锁定状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "lock_status", "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "_id": "61de89ca50c766000143afe1", "is_index_field": false, "is_single": false, "config": {}, "index_name": "s_3", "status": "new", "help_text": ""}, "create_time": {"is_index": true, "create_time": 1641974218223, "description": "create_time", "is_unique": false, "label": "创建时间", "time_zone": "", "type": "date_time", "is_need_convert": false, "is_required": false, "api_name": "create_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "index_name": "crt_time", "status": "released"}, "life_status": {"describe_api_name": "TPMActivityBudgetObj", "is_index": true, "is_active": true, "create_time": 1641974218239, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "default_value": "normal", "label": "生命状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "life_status", "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "_id": "61de89ca50c766000143afe5", "is_index_field": false, "is_single": false, "config": {}, "index_name": "s_4", "status": "new", "help_text": ""}, "last_modified_by": {"is_index": true, "is_active": true, "create_time": 1641974218223, "is_unique": false, "label": "最后修改人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "last_modified_by", "define_type": "system", "is_single": true, "index_name": "md_by", "status": "released"}, "out_tenant_id": {"is_index": false, "is_active": true, "create_time": 1641974218223, "pattern": "", "description": "out_tenant_id", "is_unique": false, "label": "外部企业", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "out_tenant_id", "define_type": "system", "config": {"display": 0}, "index_name": "o_ei", "status": "released", "max_length": 200}, "created_by": {"is_index": true, "is_active": true, "create_time": 1641974218223, "is_unique": false, "label": "创建人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "created_by", "define_type": "system", "is_single": true, "index_name": "crt_by", "status": "released"}, "version": {"is_index": false, "create_time": 1641974218223, "length": 8, "description": "version", "is_unique": false, "label": "version", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "version", "define_type": "system", "index_name": "version", "round_mode": 4, "status": "released"}, "data_source": {"is_index": false, "create_time": 1641974218223, "length": 8, "description": "data_source", "is_unique": false, "label": "data_source", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "data_source", "define_type": "system", "index_name": "l_ds", "round_mode": 4, "status": "released"}, "record_type": {"describe_api_name": "TPMActivityBudgetObj", "is_index": true, "is_active": true, "create_time": 1641974218245, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "record_type", "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "_id": "61de89ca50c766000143afeb", "is_index_field": false, "is_single": false, "config": {}, "index_name": "r_type", "status": "released", "help_text": ""}, "relevant_team": {"describe_api_name": "TPMActivityBudgetObj", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "description": "成员员工", "define_type": "package", "is_unique": false, "label": "成员员工", "is_single": true, "type": "employee", "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "description": "成员角色", "define_type": "package", "is_unique": false, "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "description": "成员权限类型", "define_type": "package", "is_unique": false, "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "create_time": 1641974218244, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "_id": "61de89ca50c766000143afea", "is_index_field": false, "is_single": false, "index_name": "a_team", "status": "new", "help_text": "相关团队"}, "frozen_amount": {"describe_api_name": "TPMActivityBudgetObj", "default_is_expression": true, "auto_adapt_places": false, "is_unique": false, "description": "", "type": "number", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "index_name": "d_4", "max_length": 14, "is_index": true, "is_active": true, "create_time": 1641974218246, "is_encrypted": false, "length": 12, "default_value": "0", "label": "冻结预算(元)", "api_name": "frozen_amount", "_id": "61de89ca50c766000143afec", "is_index_field": false, "round_mode": 4, "status": "new", "help_text": ""}, "data_own_department": {"is_index": true, "is_active": true, "create_time": 1641974218223, "is_unique": false, "label": "归属部门", "type": "department", "is_need_convert": false, "is_required": false, "api_name": "data_own_department", "define_type": "package", "is_single": true, "index_name": "data_owner_dept_id", "status": "released"}, "period_year": {"describe_api_name": "TPMActivityBudgetObj", "is_index": true, "is_active": true, "create_time": 1641974218247, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "default_value": "", "label": "预算年度", "type": "select_one", "is_required": true, "api_name": "period_year", "options": [{"label": "2021", "value": "2021"}, {"label": "2022", "value": "2022"}, {"label": "2023", "value": "2023"}, {"label": "2024", "value": "2024"}, {"label": "2025", "value": "2025"}, {"label": "2026", "value": "2026"}, {"label": "2027", "value": "2027"}, {"label": "2028", "value": "2028"}, {"label": "2029", "value": "2029"}, {"label": "2030", "value": "2030"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "package", "_id": "61de89ca50c766000143afed", "is_index_field": false, "is_single": false, "config": {}, "index_name": "s_5", "status": "new", "help_text": ""}, "total_amount": {"describe_api_name": "TPMActivityBudgetObj", "return_type": "number", "expression_type": "js", "is_index": true, "expression": "$transfer_in_amount$-$transfer_out_amount$+$amount$", "is_active": true, "create_time": 1641974218249, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "label": "预算总金额", "type": "formula", "decimal_places": 2, "default_to_zero": true, "is_required": false, "api_name": "total_amount", "define_type": "package", "_id": "61de89ca50c766000143afef", "is_index_field": false, "is_single": false, "index_name": "d_5", "status": "new", "help_text": ""}, "name": {"describe_api_name": "TPMActivityBudgetObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "is_unique": true, "description": "name", "type": "text", "default_to_zero": false, "is_required": true, "define_type": "system", "input_mode": "", "is_single": false, "index_name": "name", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1641974218357, "is_encrypted": false, "default_value": "", "label": "预算表名称", "api_name": "name", "_id": "61de89ca50c766000143aff0", "is_index_field": false, "status": "new", "help_text": ""}, "order_by": {"is_index": false, "create_time": 1641974218223, "length": 8, "description": "order_by", "is_unique": false, "label": "order_by", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "order_by", "define_type": "system", "index_name": "l_by", "round_mode": 4, "status": "released"}, "used_amount": {"describe_api_name": "TPMActivityBudgetObj", "default_is_expression": true, "auto_adapt_places": false, "is_unique": false, "description": "", "type": "number", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "index_name": "d_6", "max_length": 14, "is_index": true, "is_active": true, "create_time": 1641974218251, "is_encrypted": false, "length": 12, "default_value": "0", "label": "已用预算(元)", "api_name": "used_amount", "_id": "61de89ca50c766000143aff1", "is_index_field": false, "round_mode": 4, "status": "new", "help_text": ""}, "_id": {"is_index": false, "is_active": true, "create_time": 1641974218223, "pattern": "", "description": "_id", "is_unique": false, "label": "_id", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "_id", "define_type": "system", "index_name": "_id", "status": "released", "max_length": 200}}}