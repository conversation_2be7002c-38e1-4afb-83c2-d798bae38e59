{"_id": "61de89ce50c766000143b058", "api_name": "TPMActivityBudgetDetailObj", "define_type": "package", "display_name": "预算收支明细", "fields": {"tenant_id": {"is_index": false, "is_active": true, "create_time": 1641974222813, "pattern": "", "description": "tenant_id", "is_unique": false, "label": "tenant_id", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "tenant_id", "define_type": "system", "index_name": "ei", "status": "released", "max_length": 200}, "lock_rule": {"describe_api_name": "TPMActivityBudgetDetailObj", "is_index": false, "is_active": true, "create_time": 1641974222736, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "锁定规则", "default_value": "default_lock_rule", "rules": [], "label": "锁定规则", "type": "lock_rule", "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "_id": "61de89ce50c766000143b034", "is_index_field": false, "is_single": false, "index_name": "s_1", "status": "new", "help_text": ""}, "amount_before_operation": {"describe_api_name": "TPMActivityBudgetDetailObj", "default_is_expression": false, "auto_adapt_places": false, "remove_mask_roles": {}, "is_unique": false, "description": "", "type": "currency", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "index_name": "d_2", "max_length": 14, "is_index": true, "is_active": true, "create_time": 1641974222737, "is_encrypted": false, "length": 12, "default_value": "", "label": "操作前预算可用金额", "currency_unit": "￥", "api_name": "amount_before_operation", "_id": "61de89ce50c766000143b035", "is_index_field": false, "is_show_mask": false, "round_mode": 4, "status": "new", "help_text": ""}, "remark": {"describe_api_name": "TPMActivityBudgetDetailObj", "is_index": true, "default_is_expression": false, "is_active": true, "create_time": 1641974222738, "is_encrypted": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "description": "", "default_value": "", "label": "预算描述", "type": "long_text", "default_to_zero": false, "is_required": false, "api_name": "remark", "define_type": "package", "_id": "61de89ce50c766000143b036", "is_index_field": false, "is_single": false, "index_name": "t_2", "status": "new", "help_text": "", "max_length": 2000}, "budget_adjust_id": {"describe_api_name": "TPMActivityBudgetDetailObj", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "where_type": "field", "type": "object_reference", "wheres": [], "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "index_name": "s_4", "is_index": true, "is_active": true, "create_time": 1641974222739, "is_encrypted": false, "target_api_name": "TPMActivityBudgetAdjustObj", "label": "预算调整单", "target_related_list_name": "target_related_list_TPMActivityBudgetDetailObj_TPMActivityBudgetAdjustObj__c", "target_related_list_label": "预算收支明细", "action_on_target_delete": "set_null", "api_name": "budget_adjust_id", "_id": "61de89ce50c766000143b037", "is_index_field": true, "status": "new", "help_text": ""}, "type": {"describe_api_name": "TPMActivityBudgetDetailObj", "is_index": true, "is_active": true, "create_time": 1641974222740, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "default_value": "", "label": "明细类型", "type": "select_one", "is_required": false, "api_name": "type", "options": [{"font_color": "#2a304d", "label": "期初", "value": "0"}, {"font_color": "#2a304d", "label": "支出", "value": "1"}, {"font_color": "#2a304d", "label": "收入", "value": "2"}, {"font_color": "#2a304d", "not_usable": true, "label": "其他", "value": "other"}], "define_type": "package", "_id": "61de89ce50c766000143b038", "is_index_field": false, "is_single": false, "config": {}, "index_name": "s_5", "status": "new", "help_text": ""}, "lock_user": {"describe_api_name": "TPMActivityBudgetDetailObj", "is_index": false, "is_active": true, "create_time": 1641974222741, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "加锁人", "where_type": "field", "label": "加锁人", "type": "employee", "is_need_convert": false, "wheres": [], "is_required": false, "api_name": "lock_user", "define_type": "package", "_id": "61de89ce50c766000143b039", "is_index_field": false, "is_single": true, "index_name": "a_1", "status": "new", "help_text": ""}, "extend_obj_data_id": {"describe_api_name": "TPMActivityBudgetDetailObj", "is_index": true, "is_active": true, "create_time": 1641974222742, "is_encrypted": false, "auto_adapt_places": false, "pattern": "", "is_unique": true, "description": "连接通表的记录ID,扩展字段用", "label": "扩展字段在mt_data中的记录ID", "type": "text", "is_required": false, "api_name": "extend_obj_data_id", "define_type": "system", "_id": "61de89ce50c766000143b03a", "is_index_field": false, "is_single": false, "index_name": "t_3", "status": "released", "max_length": 64}, "is_deleted": {"is_index": false, "create_time": 1641974222813, "description": "is_deleted", "is_unique": false, "default_value": false, "label": "is_deleted", "type": "true_or_false", "is_need_convert": false, "is_required": false, "api_name": "is_deleted", "define_type": "system", "index_name": "is_del", "status": "released"}, "life_status_before_invalid": {"describe_api_name": "TPMActivityBudgetDetailObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "description": "作废前生命状态", "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "index_name": "t_5", "max_length": 256, "is_index": false, "is_active": true, "create_time": 1641974222744, "is_encrypted": false, "default_value": "", "label": "作废前生命状态", "is_need_convert": false, "api_name": "life_status_before_invalid", "_id": "61de89ce50c766000143b03c", "is_index_field": false, "status": "new", "help_text": ""}, "object_describe_api_name": {"is_index": false, "is_active": true, "create_time": 1641974222813, "pattern": "", "description": "object_describe_api_name", "is_unique": false, "label": "object_describe_api_name", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "object_describe_api_name", "define_type": "system", "index_name": "api_name", "status": "released", "max_length": 200}, "amount_after_operation": {"describe_api_name": "TPMActivityBudgetDetailObj", "default_is_expression": false, "auto_adapt_places": false, "remove_mask_roles": {}, "is_unique": false, "description": "", "type": "currency", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "index_name": "d_3", "max_length": 14, "is_index": true, "is_active": true, "create_time": 1641974222745, "is_encrypted": false, "length": 12, "default_value": "", "label": "操作后预算可用金额", "currency_unit": "￥", "api_name": "amount_after_operation", "_id": "61de89ce50c766000143b03d", "is_index_field": false, "is_show_mask": false, "round_mode": 4, "status": "new", "help_text": ""}, "activity_id": {"describe_api_name": "TPMActivityBudgetDetailObj", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "description": "", "where_type": "field", "type": "object_reference", "wheres": [], "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "index_name": "s_7", "is_index": true, "is_active": true, "create_time": 1641974222747, "is_encrypted": false, "target_api_name": "TPMActivityObj", "label": "活动方案", "target_related_list_name": "target_related_list_TPMActivityBudgetDetailObj_TPMActivityObj__c", "target_related_list_label": "预算收支明细", "action_on_target_delete": "set_null", "api_name": "activity_id", "_id": "61de89ce50c766000143b03f", "is_index_field": true, "status": "new", "help_text": ""}, "out_owner": {"is_index": false, "is_active": true, "create_time": 1641974222813, "is_unique": false, "label": "外部负责人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "out_owner", "define_type": "system", "is_single": true, "config": {"display": 1}, "index_name": "o_owner", "status": "released"}, "owner_department": {"describe_api_name": "TPMActivityBudgetDetailObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "description": "", "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": true, "index_name": "owner_dept", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1641974222748, "is_encrypted": false, "default_value": "", "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "_id": "61de89ce50c766000143b040", "is_index_field": false, "status": "new", "help_text": ""}, "operate_time": {"describe_api_name": "TPMActivityBudgetDetailObj", "is_index": true, "default_is_expression": false, "is_active": true, "create_time": 1641974222750, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "default_value": "", "label": "操作时间", "time_zone": "GMT+8", "type": "date_time", "default_to_zero": false, "is_required": false, "api_name": "operate_time", "define_type": "package", "date_format": "yyyy-MM-dd HH:mm", "_id": "61de89ce50c766000143b042", "is_index_field": false, "is_single": false, "index_name": "l_1", "status": "new", "help_text": ""}, "approval_instance_id": {"describe_api_name": "TPMActivityBudgetDetailObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "description": "", "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "index_name": "t_1", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1641974222752, "is_encrypted": false, "default_value": "", "label": "关联审批的id", "api_name": "approval_instance_id", "_id": "61de89ce50c766000143b044", "is_index_field": false, "status": "new", "help_text": ""}, "owner": {"describe_api_name": "TPMActivityBudgetDetailObj", "is_index": true, "is_active": true, "create_time": 1641974222751, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "where_type": "field", "label": "负责人", "type": "employee", "is_need_convert": false, "wheres": [], "is_required": true, "api_name": "owner", "define_type": "package", "_id": "61de89ce50c766000143b043", "is_index_field": false, "is_single": true, "index_name": "owner", "status": "new", "help_text": ""}, "amount": {"describe_api_name": "TPMActivityBudgetDetailObj", "default_is_expression": false, "auto_adapt_places": false, "remove_mask_roles": {}, "is_unique": false, "description": "", "type": "currency", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "index_name": "d_1", "max_length": 14, "is_index": true, "is_active": true, "create_time": 1641974222753, "is_encrypted": false, "length": 12, "default_value": "", "label": "操作金额", "currency_unit": "￥", "api_name": "amount", "_id": "61de89ce50c766000143b045", "is_index_field": false, "is_show_mask": false, "round_mode": 4, "status": "new", "help_text": ""}, "last_modified_time": {"is_index": true, "create_time": 1641974222813, "description": "last_modified_time", "is_unique": false, "label": "最后修改时间", "time_zone": "", "type": "date_time", "is_need_convert": false, "is_required": false, "api_name": "last_modified_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "index_name": "md_time", "status": "released"}, "package": {"is_index": false, "is_active": true, "create_time": 1641974222813, "pattern": "", "description": "package", "is_unique": false, "label": "package", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "package", "define_type": "system", "index_name": "pkg", "status": "released", "max_length": 200}, "lock_status": {"describe_api_name": "TPMActivityBudgetDetailObj", "is_index": true, "is_active": true, "create_time": 1641974222754, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "锁定状态", "default_value": "0", "label": "锁定状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "lock_status", "options": [{"font_color": "#2a304d", "label": "未锁定", "value": "0"}, {"font_color": "#2a304d", "label": "锁定", "value": "1"}], "define_type": "package", "_id": "61de89ce50c766000143b046", "is_index_field": false, "is_single": false, "config": {}, "index_name": "s_2", "status": "new", "help_text": ""}, "create_time": {"is_index": true, "create_time": 1641974222813, "description": "create_time", "is_unique": false, "label": "创建时间", "time_zone": "", "type": "date_time", "is_need_convert": false, "is_required": false, "api_name": "create_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "index_name": "crt_time", "status": "released"}, "life_status": {"describe_api_name": "TPMActivityBudgetDetailObj", "is_index": true, "is_active": true, "create_time": 1641974222758, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "default_value": "normal", "label": "生命状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "life_status", "options": [{"font_color": "#2a304d", "label": "未生效", "value": "ineffective"}, {"font_color": "#2a304d", "label": "审核中", "value": "under_review"}, {"font_color": "#2a304d", "label": "正常", "value": "normal"}, {"font_color": "#2a304d", "label": "变更中", "value": "in_change"}, {"font_color": "#2a304d", "label": "作废", "value": "invalid"}], "define_type": "package", "_id": "61de89ce50c766000143b04a", "is_index_field": false, "is_single": false, "config": {}, "index_name": "s_3", "status": "new", "help_text": ""}, "last_modified_by": {"is_index": true, "is_active": true, "create_time": 1641974222813, "is_unique": false, "label": "最后修改人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "last_modified_by", "define_type": "system", "is_single": true, "index_name": "md_by", "status": "released"}, "out_tenant_id": {"is_index": false, "is_active": true, "create_time": 1641974222813, "pattern": "", "description": "out_tenant_id", "is_unique": false, "label": "外部企业", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "out_tenant_id", "define_type": "system", "config": {"display": 0}, "index_name": "o_ei", "status": "released", "max_length": 200}, "created_by": {"is_index": true, "is_active": true, "create_time": 1641974222813, "is_unique": false, "label": "创建人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "created_by", "define_type": "system", "is_single": true, "index_name": "crt_by", "status": "released"}, "version": {"is_index": false, "create_time": 1641974222813, "length": 8, "description": "version", "is_unique": false, "label": "version", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "version", "define_type": "system", "index_name": "version", "round_mode": 4, "status": "released"}, "budget_table_id": {"describe_api_name": "TPMActivityBudgetDetailObj", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "description": "", "where_type": "field", "type": "object_reference", "wheres": [], "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "index_name": "s_6", "is_index": true, "is_active": true, "create_time": 1641974222761, "is_encrypted": false, "target_api_name": "TPMActivityBudgetObj", "label": "预算表", "target_related_list_name": "target_related_list_TPMActivityBudgetDetailObj_TPMActivityBudgetObj__c", "target_related_list_label": "预算收支明细", "action_on_target_delete": "set_null", "api_name": "budget_table_id", "_id": "61de89ce50c766000143b04d", "is_index_field": true, "status": "new", "help_text": ""}, "data_source": {"is_index": false, "create_time": 1641974222813, "length": 8, "description": "data_source", "is_unique": false, "label": "data_source", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "data_source", "define_type": "system", "index_name": "l_ds", "round_mode": 4, "status": "released"}, "record_type": {"describe_api_name": "TPMActivityBudgetDetailObj", "is_index": true, "is_active": true, "create_time": 1641974222765, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "record_type", "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "font_color": "#2a304d", "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "_id": "61de89ce50c766000143b051", "is_index_field": false, "is_single": false, "config": {}, "index_name": "r_type", "status": "released", "help_text": ""}, "relevant_team": {"describe_api_name": "TPMActivityBudgetDetailObj", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "description": "成员员工", "define_type": "package", "is_unique": false, "label": "成员员工", "is_single": true, "type": "employee", "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "description": "成员角色", "define_type": "package", "is_unique": false, "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "description": "成员权限类型", "define_type": "package", "is_unique": false, "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "create_time": 1641974222764, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "_id": "61de89ce50c766000143b050", "is_index_field": false, "is_single": false, "index_name": "a_team", "status": "new", "help_text": "相关团队"}, "data_own_department": {"is_index": true, "is_active": true, "create_time": 1641974222813, "is_unique": false, "label": "归属部门", "type": "department", "is_need_convert": false, "is_required": false, "api_name": "data_own_department", "define_type": "package", "is_single": true, "index_name": "data_owner_dept_id", "status": "released"}, "extra_data": {"describe_api_name": "TPMActivityBudgetDetailObj", "is_index": true, "default_is_expression": false, "is_active": true, "create_time": 1641974222767, "is_encrypted": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "description": "", "default_value": "", "label": "业务记录", "type": "long_text", "default_to_zero": false, "is_required": false, "api_name": "extra_data", "define_type": "package", "_id": "61de89ce50c766000143b053", "is_index_field": false, "is_single": false, "index_name": "t_4", "status": "new", "help_text": "", "max_length": 2000}, "name": {"describe_api_name": "TPMActivityBudgetDetailObj", "default_is_expression": false, "prefix": "{yyyy}-{mm}-{dd}-", "auto_adapt_places": false, "is_unique": true, "description": "", "start_number": 1, "type": "auto_number", "default_to_zero": false, "is_required": true, "define_type": "system", "input_mode": "", "postfix": "", "is_single": false, "index_name": "name", "max_length": 100, "is_index": true, "auto_number_type": "normal", "is_active": true, "create_time": 1641974222907, "is_encrypted": false, "default_value": "{yyyy}-{mm}-{dd}-0001", "serial_number": 4, "label": "预算流水编号", "condition": "NONE", "api_name": "name", "func_api_name": "", "_id": "61de89ce50c766000143b054", "is_index_field": false, "status": "new", "help_text": ""}, "order_by": {"is_index": false, "create_time": 1641974222813, "length": 8, "description": "order_by", "is_unique": false, "label": "order_by", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "order_by", "define_type": "system", "index_name": "l_by", "round_mode": 4, "status": "released"}, "_id": {"is_index": false, "is_active": true, "create_time": 1641974222813, "pattern": "", "description": "_id", "is_unique": false, "label": "_id", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "_id", "define_type": "system", "index_name": "_id", "status": "released", "max_length": 200}, "source_id": {"describe_api_name": "TPMActivityBudgetDetailObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "description": "", "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "index_name": "t_6", "max_length": 100, "is_index": true, "is_active": true, "create_time": 1641974222770, "is_encrypted": false, "default_value": "", "label": "来源编号", "api_name": "source_id", "_id": "61de89ce50c766000143b056", "is_index_field": false, "status": "new", "help_text": ""}}}