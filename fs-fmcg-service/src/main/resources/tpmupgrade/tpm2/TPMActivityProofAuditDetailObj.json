{"store_table_name": "fmcg_tpm_activity_proof_audit_detail", "description": "", "index_version": 1, "is_deleted": false, "define_type": "package", "release_version": "6.4", "package": "CRM", "is_active": true, "display_name": "活动举证检核项目", "version": 15, "icon_index": 27, "api_name": "TPMActivityProofAuditDetailObj", "icon_path": "", "is_udef": true, "short_name": "JlC", "fields": {"tenant_id": {"describe_api_name": "TPMActivityProofAuditDetailObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "tenant_id", "label": "tenant_id", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "tenant_id", "define_type": "system", "max_length": 200, "status": "released"}, "lock_rule": {"describe_api_name": "TPMActivityProofAuditDetailObj", "is_index": false, "is_active": true, "description": "锁定规则", "is_unique": false, "default_value": "default_lock_rule", "rules": [], "label": "锁定规则", "type": "lock_rule", "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "activity_proof_audit_id": {"describe_api_name": "TPMActivityProofAuditDetailObj", "description": "", "is_unique": false, "type": "master_detail", "is_required": true, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "label": "活动检核", "target_api_name": "TPMActivityProofAuditObj", "show_detail_button": false, "target_related_list_name": "target_related_list_TPMActivityProofAuditDetailObj_TPMActivityProofAuditObj__c", "target_related_list_label": "活动举证检核项目", "api_name": "activity_proof_audit_id", "is_create_when_master_create": true, "is_required_when_master_create": false, "help_text": "", "status": "new"}, "audit_item": {"describe_api_name": "TPMActivityProofAuditDetailObj", "is_index": true, "default_is_expression": true, "is_active": true, "is_unique": false, "default_value": "IF(ISNULL($activity_item_id$),\"\",$activity_item_id__r.name$)", "label": "检核项目", "type": "text", "default_to_zero": false, "is_required": false, "api_name": "audit_item", "define_type": "package", "input_mode": "", "help_text": "", "status": "new", "max_length": 100}, "activity_agreement_detail_id": {"describe_api_name": "TPMActivityProofAuditDetailObj", "default_is_expression": true, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "is_required": false, "wheres": [], "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "default_value": "$activity_proof_detail_id__r.activity_agreement_detail_id$", "label": "活动协议项目", "target_api_name": "TPMActivityAgreementDetailObj", "target_related_list_name": "target_related_list_TPMActivityProofAuditDetailObj_TPMActivityAgreementDetailObj__c", "target_related_list_label": "活动举证检核项目", "action_on_target_delete": "set_null", "api_name": "activity_agreement_detail_id", "help_text": "", "status": "new"}, "activity_item_id": {"describe_api_name": "TPMActivityProofAuditDetailObj", "default_is_expression": true, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "is_required": false, "wheres": [], "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "default_value": "$activity_proof_detail_id__r.activity_item_id$", "label": "费用项目", "target_api_name": "TPMActivityItemObj", "target_related_list_name": "target_related_list_TPMActivityProofAuditDetailObj_TPMActivityItemObj__c", "target_related_list_label": "活动举证检核项目", "action_on_target_delete": "set_null", "api_name": "activity_item_id", "help_text": "", "status": "new"}, "type": {"describe_api_name": "TPMActivityProofAuditDetailObj", "is_index": true, "is_active": true, "quote_field_type": "select_one", "description": "", "is_unique": false, "label": "项目类型", "type": "quote", "quote_field": "activity_item_id__r.type", "is_required": false, "api_name": "type", "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "lock_user": {"describe_api_name": "TPMActivityProofAuditDetailObj", "is_index": false, "is_active": true, "description": "加锁人", "is_unique": false, "label": "加锁人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "is_single": true, "help_text": "", "status": "new"}, "extend_obj_data_id": {"describe_api_name": "TPMActivityProofAuditDetailObj", "is_index": true, "is_required": false, "api_name": "extend_obj_data_id", "is_unique": true, "description": "连接通表的记录ID,扩展字段用", "define_type": "system", "label": "扩展字段在mt_data中的记录ID", "type": "text", "status": "released", "max_length": 64}, "is_deleted": {"describe_api_name": "TPMActivityProofAuditDetailObj", "is_index": false, "is_unique": false, "description": "is_deleted", "default_value": "false", "label": "is_deleted", "type": "true_or_false", "is_need_convert": false, "is_required": false, "api_name": "is_deleted", "define_type": "system", "status": "released"}, "audit_subtotal": {"describe_api_name": "TPMActivityProofAuditDetailObj", "default_is_expression": true, "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "currency", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "max_length": 14, "is_index": true, "is_active": true, "length": 12, "default_value": "IF(ISNULL($activity_proof_detail_id$), null,$activity_proof_detail_id__r.proof_detail_cost_standard$ )", "label": "检核费用(元)", "currency_unit": "￥", "api_name": "audit_subtotal", "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "life_status_before_invalid": {"describe_api_name": "TPMActivityProofAuditDetailObj", "default_is_expression": false, "pattern": "", "description": "作废前生命状态", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "max_length": 256, "is_index": false, "is_active": true, "default_value": "", "label": "作废前生命状态", "is_need_convert": false, "api_name": "life_status_before_invalid", "help_text": "", "status": "new"}, "object_describe_api_name": {"describe_api_name": "TPMActivityProofAuditDetailObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "object_describe_api_name", "label": "object_describe_api_name", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "object_describe_api_name", "define_type": "system", "max_length": 200, "status": "released"}, "owner_department": {"describe_api_name": "TPMActivityProofAuditDetailObj", "default_is_expression": false, "pattern": "", "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": true, "max_length": 100, "is_index": true, "is_active": true, "default_value": "", "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "help_text": "", "status": "new"}, "out_owner": {"describe_api_name": "TPMActivityProofAuditDetailObj", "is_index": false, "is_active": true, "is_unique": false, "label": "外部负责人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "out_owner", "define_type": "system", "is_single": true, "status": "released"}, "activity_detail_id": {"describe_api_name": "TPMActivityProofAuditDetailObj", "default_is_expression": true, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "is_required": false, "wheres": [], "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "default_value": "$activity_proof_detail_id__r.activity_detail_id$", "label": "活动项目", "target_api_name": "TPMActivityDetailObj", "target_related_list_name": "target_related_list_TPMActivityProofAuditDetailObj_TPMActivityDetailObj__c", "target_related_list_label": "参与活动项目", "action_on_target_delete": "set_null", "api_name": "activity_detail_id", "help_text": "", "status": "new"}, "owner": {"describe_api_name": "TPMActivityProofAuditDetailObj", "is_index": true, "is_active": true, "description": "", "is_unique": false, "label": "负责人", "type": "employee", "is_need_convert": false, "is_required": true, "api_name": "owner", "define_type": "package", "is_single": true, "help_text": "", "status": "new"}, "proof_detail_cost_standard": {"describe_api_name": "TPMActivityProofAuditDetailObj", "is_index": true, "is_active": true, "quote_field_type": "currency", "description": "", "is_unique": false, "label": "举证项目费用标准(元)", "type": "quote", "quote_field": "activity_proof_detail_id__r.proof_detail_cost_standard", "is_required": false, "api_name": "proof_detail_cost_standard", "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "proof_detail_amount_standard": {"describe_api_name": "TPMActivityProofAuditDetailObj", "is_index": true, "is_active": true, "quote_field_type": "number", "description": "", "is_unique": false, "label": "举证项目数量标准", "type": "quote", "quote_field": "activity_proof_detail_id__r.proof_detail_amount_standard", "is_required": false, "api_name": "proof_detail_amount_standard", "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "amount": {"describe_api_name": "TPMActivityProofAuditDetailObj", "is_index": true, "is_active": true, "quote_field_type": "number", "description": "", "is_unique": false, "label": "举证数量", "type": "quote", "quote_field": "activity_proof_detail_id__r.amount", "is_required": false, "api_name": "amount", "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "lock_status": {"describe_api_name": "TPMActivityProofAuditDetailObj", "is_index": true, "is_active": true, "description": "锁定状态", "is_unique": false, "default_value": "0", "label": "锁定状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "lock_status", "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "package": {"describe_api_name": "TPMActivityProofAuditDetailObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "package", "label": "package", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "package", "define_type": "system", "max_length": 200, "status": "released"}, "last_modified_time": {"describe_api_name": "TPMActivityProofAuditDetailObj", "is_index": true, "is_unique": false, "description": "last_modified_time", "label": "最后修改时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "last_modified_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "status": "released"}, "create_time": {"describe_api_name": "TPMActivityProofAuditDetailObj", "is_index": true, "is_unique": false, "description": "create_time", "label": "创建时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "create_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "status": "released"}, "amount_standard_check": {"describe_api_name": "TPMActivityProofAuditDetailObj", "is_index": true, "is_active": true, "quote_field_type": "true_or_false", "description": "", "is_unique": false, "label": "数量校验", "type": "quote", "quote_field": "activity_detail_id__r.amount_standard_check", "is_required": false, "api_name": "amount_standard_check", "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "calculate_pattern": {"describe_api_name": "TPMActivityProofAuditDetailObj", "is_index": true, "is_active": true, "quote_field_type": "select_one", "description": "", "is_unique": false, "label": "计费模式", "type": "quote", "quote_field": "activity_detail_id__r.calculate_pattern", "is_required": false, "api_name": "calculate_pattern", "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "life_status": {"describe_api_name": "TPMActivityProofAuditDetailObj", "is_index": true, "is_active": true, "description": "", "is_unique": false, "default_value": "normal", "label": "生命状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "life_status", "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "activity_proof_detail_id": {"describe_api_name": "TPMActivityProofAuditDetailObj", "default_is_expression": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "is_required": false, "wheres": [{"connector": "OR", "filters": [{"value_type": 2, "operator": "EQ", "field_name": "activity_proof_id", "field_values": ["$activity_proof_audit_id__r.activity_proof_id$"]}]}], "define_type": "package", "new_cascade_parent_api_name": {"TPMActivityProofAuditObj": ["activity_proof_id"]}, "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "label": "举证项目", "target_api_name": "TPMActivityProofDetailObj", "target_related_list_name": "target_related_list_TPMActivityProofAuditDetailObj_TPMActivityProofDetailObj__c", "target_related_list_label": "活动举证检核项目", "action_on_target_delete": "set_null", "api_name": "activity_proof_detail_id", "help_text": "", "status": "new"}, "last_modified_by": {"describe_api_name": "TPMActivityProofAuditDetailObj", "is_index": true, "is_active": true, "is_unique": false, "label": "最后修改人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "last_modified_by", "define_type": "system", "is_single": true, "status": "released"}, "out_tenant_id": {"describe_api_name": "TPMActivityProofAuditDetailObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "out_tenant_id", "label": "外部企业", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "out_tenant_id", "define_type": "system", "max_length": 200, "status": "released"}, "version": {"describe_api_name": "TPMActivityProofAuditDetailObj", "is_index": false, "length": 8, "is_unique": false, "description": "version", "label": "version", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "version", "define_type": "system", "round_mode": 4, "status": "released"}, "activity_item_cost_standard_id": {"describe_api_name": "TPMActivityProofAuditDetailObj", "default_is_expression": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "is_required": false, "wheres": [{"connector": "OR", "filters": [{"value_type": 2, "operator": "EQ", "field_name": "activity_item", "field_values": ["$activity_item_id$"]}]}], "TPMActivityProofAuditDetailObj": {"TPMActivityProofDetailObj": ["activity_item_id"]}, "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "label": "费用项目高级计费", "target_api_name": "TPMActivityItemCostStandardObj", "target_related_list_name": "target_related_list_TPMActivityProofAuditDetailObj_TPMActivityItemCostStandardObj__c", "target_related_list_label": "活动举证检核项目", "action_on_target_delete": "set_null", "api_name": "activity_item_cost_standard_id", "help_text": "", "status": "new"}, "created_by": {"describe_api_name": "TPMActivityProofAuditDetailObj", "is_index": true, "is_active": true, "is_unique": false, "label": "创建人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "created_by", "define_type": "system", "is_single": true, "status": "released"}, "relevant_team": {"describe_api_name": "TPMActivityProofAuditDetailObj", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "description": "", "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "is_single": false, "help_text": "相关团队", "status": "new"}, "record_type": {"describe_api_name": "TPMActivityProofAuditDetailObj", "is_index": true, "is_active": true, "description": "record_type", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "is_single": false, "help_text": "", "status": "released"}, "data_own_department": {"describe_api_name": "TPMActivityProofAuditDetailObj", "is_index": true, "is_active": true, "is_unique": false, "label": "归属部门", "type": "department", "is_need_convert": false, "is_required": false, "api_name": "data_own_department", "define_type": "package", "is_single": true, "status": "released"}, "audit_amount": {"describe_api_name": "TPMActivityProofAuditDetailObj", "default_is_expression": true, "description": "", "is_unique": false, "type": "number", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "max_length": 14, "is_index": true, "is_active": true, "length": 12, "default_value": "IF(ISNULL($activity_proof_detail_id$), null,$activity_proof_detail_id__r.amount$ )", "label": "检核数量", "api_name": "audit_amount", "round_mode": 4, "help_text": "", "status": "new"}, "subtotal": {"describe_api_name": "TPMActivityProofAuditDetailObj", "expression_type": "js", "return_type": "number", "is_index": true, "expression": "IF($activity_detail_id__r.amount_standard_check$,IF(EQUALS($activity_detail_id__r.calculate_pattern$, \"费用(元)X实际数量\"),IF($audit_amount$>=$activity_proof_detail_id__r.proof_detail_amount_standard$,$audit_amount$*$activity_proof_detail_id__r.proof_detail_cost_standard$,0),IF(EQUALS($activity_detail_id__r.calculate_pattern$,\"一口价\"),IF($activity_proof_detail_id__r.amount$>=$activity_proof_detail_id__r.proof_detail_amount_standard$,$activity_proof_detail_id__r.proof_detail_cost_standard$,0), 0)),IF(EQUALS($activity_detail_id__r.calculate_pattern$,\"费用(元)X实际数量\"),$audit_amount$*$activity_proof_detail_id__r.proof_detail_cost_standard$,IF(EQUALS($activity_detail_id__r.calculate_pattern$,\"一口价\"),$activity_proof_detail_id__r.proof_detail_cost_standard$,0)))", "is_active": true, "description": "", "is_unique": false, "label": "单项费用(元)", "type": "formula", "decimal_places": 2, "default_to_zero": true, "is_required": false, "api_name": "subtotal", "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "name": {"describe_api_name": "TPMActivityProofAuditDetailObj", "default_is_expression": false, "prefix": "APA-{yyyy}-{mm}-{dd}-", "description": "", "is_unique": true, "start_number": 1, "type": "auto_number", "default_to_zero": false, "is_required": true, "define_type": "system", "input_mode": "", "postfix": "", "is_single": false, "max_length": 100, "is_index": true, "auto_number_type": "normal", "is_active": true, "default_value": "APA-{yyyy}-{mm}-{dd}-00000001", "serial_number": 8, "label": "编号", "condition": "DAY", "api_name": "name", "func_api_name": "", "help_text": "", "status": "new"}, "order_by": {"describe_api_name": "TPMActivityProofAuditDetailObj", "is_index": false, "length": 8, "is_unique": false, "description": "order_by", "label": "order_by", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "order_by", "define_type": "system", "round_mode": 4, "status": "released"}, "_id": {"describe_api_name": "TPMActivityProofAuditDetailObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "_id", "label": "_id", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "_id", "define_type": "system", "max_length": 200, "status": "released"}}}