{"store_table_name": "fmcg_tpm_activity_agreement", "description": "", "index_version": 1, "is_deleted": false, "define_type": "package", "release_version": "6.4", "package": "CRM", "is_active": true, "display_name": "活动协议", "version": 17, "icon_index": 20, "api_name": "TPMActivityAgreementObj", "icon_path": "", "is_udef": true, "short_name": "frB", "fields": {"activity_type": {"describe_api_name": "TPMActivityAgreementObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "quote_field_type": "select_one", "is_unique": false, "label": "活动类型", "type": "quote", "quote_field": "activity_id__r.activity_type", "is_required": false, "api_name": "activity_type", "define_type": "package", "is_index_field": false, "is_single": false, "status": "new", "help_text": "", "description": ""}, "tenant_id": {"describe_api_name": "TPMActivityAgreementObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "tenant_id", "label": "tenant_id", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "tenant_id", "define_type": "system", "max_length": 200, "status": "released"}, "code": {"describe_api_name": "TPMActivityAgreementObj", "prefix": "{yyyy}{mm}{dd}-", "is_unique": true, "description": "", "start_number": 1, "type": "auto_number", "is_required": true, "define_type": "package", "postfix": "", "is_single": false, "is_index": true, "is_active": true, "default_value": "{yyyy}{mm}{dd}-0001", "serial_number": 4, "label": "协议编号", "is_abstract": null, "condition": "DAY", "api_name": "code", "is_index_field": false, "help_text": "", "status": "new"}, "action_id": {"describe_api_name": "TPMActivityAgreementObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "description": "", "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "动作ID", "api_name": "action_id", "is_index_field": false, "status": "new", "help_text": ""}, "visit_id": {"describe_api_name": "TPMActivityAgreementObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "description": "", "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "外勤ID", "api_name": "visit_id", "is_index_field": false, "status": "new", "help_text": ""}, "end_date": {"describe_api_name": "TPMActivityAgreementObj", "default_is_expression": true, "is_index": true, "is_active": true, "description": "", "is_unique": false, "default_value": "$activity_id__r.end_date$", "label": "结束日期", "time_zone": "GMT+8", "type": "date", "default_to_zero": false, "is_required": true, "api_name": "end_date", "define_type": "package", "date_format": "yyyy-MM-dd", "is_single": false, "help_text": "", "status": "new"}, "lock_rule": {"describe_api_name": "TPMActivityAgreementObj", "is_index": false, "is_active": true, "description": "锁定规则", "is_unique": false, "default_value": "default_lock_rule", "rules": [], "label": "锁定规则", "type": "lock_rule", "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "begin_date": {"describe_api_name": "TPMActivityAgreementObj", "default_is_expression": true, "is_index": true, "is_active": true, "description": "", "is_unique": false, "default_value": "TODAY()", "label": "开始日期", "time_zone": "GMT+8", "type": "date", "default_to_zero": false, "is_required": true, "api_name": "begin_date", "define_type": "package", "date_format": "yyyy-MM-dd", "is_single": false, "help_text": "", "status": "new"}, "description": {"describe_api_name": "TPMActivityAgreementObj", "default_is_expression": false, "is_index": true, "is_active": true, "pattern": "", "description": "", "is_unique": false, "default_value": "", "label": "备注", "type": "long_text", "default_to_zero": false, "is_required": false, "api_name": "description", "define_type": "package", "is_single": false, "help_text": "", "max_length": 2000, "status": "new"}, "lock_user": {"describe_api_name": "TPMActivityAgreementObj", "is_index": false, "is_active": true, "description": "加锁人", "is_unique": false, "label": "加锁人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "is_single": true, "help_text": "", "status": "new"}, "extend_obj_data_id": {"describe_api_name": "TPMActivityAgreementObj", "is_index": true, "is_required": false, "api_name": "extend_obj_data_id", "is_unique": true, "description": "连接通表的记录ID,扩展字段用", "define_type": "system", "label": "扩展字段在mt_data中的记录ID", "type": "text", "status": "released", "max_length": 64}, "is_deleted": {"describe_api_name": "TPMActivityAgreementObj", "is_index": false, "is_unique": false, "description": "is_deleted", "default_value": "false", "label": "is_deleted", "type": "true_or_false", "is_need_convert": false, "is_required": false, "api_name": "is_deleted", "define_type": "system", "status": "released"}, "life_status_before_invalid": {"describe_api_name": "TPMActivityAgreementObj", "default_is_expression": false, "pattern": "", "description": "作废前生命状态", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "max_length": 256, "is_index": false, "is_active": true, "default_value": "", "label": "作废前生命状态", "is_need_convert": false, "api_name": "life_status_before_invalid", "help_text": "", "status": "new"}, "object_describe_api_name": {"describe_api_name": "TPMActivityAgreementObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "object_describe_api_name", "label": "object_describe_api_name", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "object_describe_api_name", "define_type": "system", "max_length": 200, "status": "released"}, "activity_id": {"describe_api_name": "TPMActivityAgreementObj", "default_is_expression": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "is_required": true, "wheres": [{"connector": "OR", "filters": [{"value_type": 0, "operator": "IN", "field_name": "activity_status", "field_values": ["in_progress", "schedule"]}]}], "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "label": "活动名称", "target_api_name": "TPMActivityObj", "target_related_list_name": "target_related_list_TPMActivityAgreementObj_TPMActivityObj__c", "target_related_list_label": "活动协议", "action_on_target_delete": "set_null", "api_name": "activity_id", "help_text": "", "status": "new"}, "agreement_status": {"describe_api_name": "TPMActivityAgreementObj", "is_index": true, "is_active": true, "description": "", "is_unique": false, "label": "协议状态", "type": "select_one", "is_required": false, "api_name": "agreement_status", "options": [{"font_color": "#FF522A", "not_usable": false, "label": "未生效", "value": "schedule"}, {"font_color": "#30C776", "not_usable": false, "label": "已生效", "value": "in_progress"}, {"font_color": "#91959E", "not_usable": false, "label": "已过期", "value": "end"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "owner_department": {"describe_api_name": "TPMActivityAgreementObj", "default_is_expression": false, "pattern": "", "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": true, "max_length": 100, "is_index": true, "is_active": true, "default_value": "", "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "help_text": "", "status": "new"}, "out_owner": {"describe_api_name": "TPMActivityAgreementObj", "is_index": false, "is_active": true, "is_unique": false, "label": "外部负责人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "out_owner", "define_type": "system", "is_single": true, "status": "released"}, "owner": {"describe_api_name": "TPMActivityAgreementObj", "is_index": true, "is_active": true, "description": "", "is_unique": false, "label": "负责人", "type": "employee", "is_need_convert": false, "is_required": true, "api_name": "owner", "define_type": "package", "is_single": true, "help_text": "", "status": "new"}, "dealer_id": {"describe_api_name": "TPMActivityAgreementObj", "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "is_required": false, "wheres": [{"connector": "OR", "filters": [{"value_type": 0, "operator": "EQ", "field_name": "record_type", "field_values": ["dealer__c"]}]}], "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "default_is_expression": false, "default_value": "", "is_active": true, "label": "经销商", "target_api_name": "AccountObj", "target_related_list_name": "target_related_list_d_TPMActivityAgreementObj_AccountObj__c", "target_related_list_label": "经销商活动协议", "action_on_target_delete": "set_null", "api_name": "dealer_id", "help_text": "", "status": "new"}, "store_id": {"describe_api_name": "TPMActivityAgreementObj", "default_is_expression": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "is_required": true, "wheres": [], "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "label": "协议客户", "target_api_name": "AccountObj", "target_related_list_name": "target_related_list_TPMActivityAgreementObj_AccountObj__c", "target_related_list_label": "活动协议", "action_on_target_delete": "set_null", "api_name": "store_id", "help_text": "", "status": "new"}, "agreement_images": {"describe_api_name": "TPMActivityAgreementObj", "file_amount_limit": 9, "is_index": true, "is_active": true, "watermark": [{"type": "variable", "value": "current_user"}, {"type": "variable", "value": "current_time"}, {"type": "variable", "value": "current_address"}], "description": "", "is_unique": false, "label": "协议图片", "type": "image", "is_watermark": false, "file_size_limit": ********, "is_required": false, "api_name": "agreement_images", "define_type": "package", "is_single": false, "support_file_types": ["jpg", "gif", "jpeg", "png"], "help_text": "单个图片不得超过20M", "status": "new"}, "lock_status": {"describe_api_name": "TPMActivityAgreementObj", "is_index": true, "is_active": true, "description": "锁定状态", "is_unique": false, "default_value": "0", "label": "锁定状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "lock_status", "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "package": {"describe_api_name": "TPMActivityAgreementObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "package", "label": "package", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "package", "define_type": "system", "max_length": 200, "status": "released"}, "last_modified_time": {"describe_api_name": "TPMActivityAgreementObj", "is_index": true, "is_unique": false, "description": "last_modified_time", "label": "最后修改时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "last_modified_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "status": "released"}, "create_time": {"describe_api_name": "TPMActivityAgreementObj", "is_index": true, "is_unique": false, "description": "create_time", "label": "创建时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "create_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "status": "released"}, "life_status": {"describe_api_name": "TPMActivityAgreementObj", "is_index": true, "is_active": true, "description": "", "is_unique": false, "default_value": "normal", "label": "生命状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "life_status", "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "last_modified_by": {"describe_api_name": "TPMActivityAgreementObj", "is_index": true, "is_active": true, "is_unique": false, "label": "最后修改人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "last_modified_by", "define_type": "system", "is_single": true, "status": "released"}, "out_tenant_id": {"describe_api_name": "TPMActivityAgreementObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "out_tenant_id", "label": "外部企业", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "out_tenant_id", "define_type": "system", "max_length": 200, "status": "released"}, "version": {"describe_api_name": "TPMActivityAgreementObj", "is_index": false, "length": 8, "is_unique": false, "description": "version", "label": "version", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "version", "define_type": "system", "round_mode": 4, "status": "released"}, "created_by": {"describe_api_name": "TPMActivityAgreementObj", "is_index": true, "is_active": true, "is_unique": false, "label": "创建人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "created_by", "define_type": "system", "is_single": true, "status": "released"}, "relevant_team": {"describe_api_name": "TPMActivityAgreementObj", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "description": "", "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "is_single": false, "help_text": "相关团队", "status": "new"}, "record_type": {"describe_api_name": "TPMActivityAgreementObj", "is_index": true, "is_active": true, "description": "record_type", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "is_single": false, "help_text": "", "status": "released"}, "data_own_department": {"describe_api_name": "TPMActivityAgreementObj", "is_index": true, "is_active": true, "is_unique": false, "label": "归属部门", "type": "department", "is_need_convert": false, "is_required": false, "api_name": "data_own_department", "define_type": "package", "is_single": true, "status": "released"}, "total": {"describe_api_name": "TPMActivityAgreementObj", "return_type": "currency", "is_unique": false, "description": "", "type": "count", "decimal_places": 2, "sub_object_describe_apiname": "TPMActivityAgreementDetailObj", "is_required": false, "wheres": [], "define_type": "package", "is_single": false, "field_api_name": "activity_agreement_id", "default_result": "d_zero", "is_index": true, "is_active": true, "count_type": "sum", "count_field_api_name": "subtotal", "label": "协议费用(元)-统计", "count_to_zero": false, "api_name": "total", "count_field_type": "formula", "is_index_field": false, "round_mode": 1, "help_text": "", "status": "new"}, "actual_total_amount": {"describe_api_name": "TPMActivityAgreementObj", "default_is_expression": true, "remove_mask_roles": {}, "is_unique": false, "type": "currency", "decimal_places": 2, "default_to_zero": true, "is_required": true, "define_type": "package", "is_single": false, "max_length": 14, "is_index": true, "is_active": true, "is_encrypted": false, "length": 12, "default_value": "$total$", "label": "协议费用(元)", "currency_unit": "￥", "api_name": "actual_total_amount", "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "store_owner_signature": {"describe_api_name": "TPMActivityAgreementObj", "file_amount_limit": 10, "is_index": true, "is_active": true, "description": "", "is_unique": false, "label": "店主签名", "type": "signature", "is_watermark": false, "file_size_limit": 10485760, "is_required": false, "api_name": "store_owner_signature", "define_type": "package", "is_single": false, "support_file_types": [], "help_text": "", "status": "new"}, "name": {"describe_api_name": "TPMActivityAgreementObj", "default_is_expression": false, "pattern": "", "description": "name", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": true, "define_type": "system", "input_mode": "", "is_single": false, "index_name": "name", "max_length": 100, "is_index": true, "is_active": true, "label": "协议名称", "api_name": "name", "is_index_field": false, "help_text": "", "status": "new"}, "order_by": {"describe_api_name": "TPMActivityAgreementObj", "is_index": false, "length": 8, "is_unique": false, "description": "order_by", "label": "order_by", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "order_by", "define_type": "system", "round_mode": 4, "status": "released"}, "_id": {"describe_api_name": "TPMActivityAgreementObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "_id", "label": "_id", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "_id", "define_type": "system", "max_length": 200, "status": "released"}, "agreement_cashing_type": {"describe_api_name": "TPMActivityAgreementObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": "cash", "label": "协议兑付方式", "type": "select_one", "is_required": false, "api_name": "agreement_cashing_type", "options": [{"label": "货补", "value": "goods"}, {"label": "现金", "value": "cash"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "package", "is_single": false, "is_index_field": false, "config": {}, "help_text": "", "status": "new"}}}