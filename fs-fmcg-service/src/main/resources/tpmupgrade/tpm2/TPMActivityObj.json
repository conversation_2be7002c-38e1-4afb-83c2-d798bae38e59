{"store_table_name": "fmcg_tpm_activity", "description": "", "index_version": 1, "is_deleted": false, "define_type": "package", "release_version": "6.4", "package": "CRM", "is_active": true, "display_name": "活动申请", "version": 31, "icon_index": 20, "api_name": "TPMActivityObj", "icon_path": "", "is_udef": true, "short_name": "bvv", "fields": {"dealer_id": {"describe_api_name": "TPMActivityObj", "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "is_required": false, "wheres": [], "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "default_is_expression": false, "is_active": true, "label": "经销商", "target_api_name": "AccountObj", "target_related_list_name": "target_related_list_d_TPMActivityObj_AccountObj__c", "target_related_list_label": "经销商活动方案", "action_on_target_delete": "set_null", "api_name": "dealer_id", "help_text": "", "status": "new"}, "tenant_id": {"describe_api_name": "TPMActivityObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "tenant_id", "label": "tenant_id", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "tenant_id", "define_type": "system", "max_length": 200, "status": "released"}, "end_date": {"describe_api_name": "TPMActivityObj", "default_is_expression": false, "is_index": true, "is_active": true, "description": "", "is_unique": false, "default_value": "", "label": "结束日期", "time_zone": "GMT+8", "type": "date", "default_to_zero": false, "is_required": true, "api_name": "end_date", "define_type": "package", "date_format": "yyyy-MM-dd", "is_single": false, "help_text": "", "status": "new"}, "close_time": {"describe_api_name": "TPMActivityObj", "default_is_expression": false, "is_index": true, "is_active": true, "description": "", "is_unique": false, "default_value": "", "label": "结案时间", "time_zone": "GMT+8", "type": "date", "default_to_zero": false, "is_required": false, "api_name": "close_time", "define_type": "package", "date_format": "yyyy-MM-dd", "is_single": false, "help_text": "", "status": "new"}, "lock_rule": {"describe_api_name": "TPMActivityObj", "is_index": false, "is_active": true, "description": "锁定规则", "is_unique": false, "default_value": "default_lock_rule", "rules": [], "label": "锁定规则", "type": "lock_rule", "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "begin_date": {"describe_api_name": "TPMActivityObj", "default_is_expression": false, "is_index": true, "is_active": true, "description": "", "is_unique": false, "default_value": "", "label": "开始日期", "time_zone": "GMT+8", "type": "date", "default_to_zero": false, "is_required": true, "api_name": "begin_date", "define_type": "package", "date_format": "yyyy-MM-dd", "is_single": false, "help_text": "", "status": "new"}, "description": {"describe_api_name": "TPMActivityObj", "default_is_expression": false, "is_index": true, "is_active": true, "pattern": "", "description": "", "is_unique": false, "default_value": "", "label": "活动说明", "type": "long_text", "default_to_zero": false, "is_required": false, "api_name": "description", "define_type": "package", "is_single": false, "help_text": "", "max_length": 2000, "status": "new"}, "lock_user": {"describe_api_name": "TPMActivityObj", "is_index": false, "is_active": true, "description": "加锁人", "is_unique": false, "label": "加锁人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "is_single": true, "help_text": "", "status": "new"}, "store_range": {"describe_api_name": "TPMActivityObj", "expression_type": "json", "default_is_expression": false, "pattern": "", "description": "", "is_unique": false, "type": "use_range", "default_to_zero": false, "is_required": false, "define_type": "package", "is_single": false, "max_length": 256, "is_index": false, "is_active": true, "default_value": "{\"type\":\"ALL\",\"value\":\"ALL\"}", "label": "门店范围", "target_api_name": "AccountObj", "api_name": "store_range", "help_text": "", "status": "released"}, "activity_amount": {"describe_api_name": "TPMActivityObj", "default_is_expression": false, "remove_mask_roles": {}, "is_unique": false, "type": "currency", "decimal_places": 2, "default_to_zero": true, "is_required": true, "define_type": "package", "is_single": false, "max_length": 14, "is_index": true, "is_active": true, "is_encrypted": false, "length": 12, "default_value": "0", "label": "活动申请费用(元)", "currency_unit": "￥", "api_name": "activity_amount", "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "activity_actual_amount": {"describe_api_name": "TPMActivityObj", "default_is_expression": false, "remove_mask_roles": {}, "is_unique": false, "type": "currency", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "max_length": 14, "is_index": true, "is_active": true, "is_encrypted": false, "length": 12, "default_value": "0", "label": "活动已核销费用(元)", "currency_unit": "￥", "api_name": "activity_actual_amount", "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "cashing_product_range": {"expression_type": "json", "describe_api_name": "TPMActivityObj", "default_is_expression": false, "pattern": "", "description": "", "is_unique": false, "type": "use_range", "default_to_zero": false, "is_required": false, "define_type": "package", "is_single": false, "max_length": 256, "is_index": false, "is_active": true, "default_value": "{\"type\":\"ALL\",\"value\":\"ALL\"}", "label": "兑付产品范围", "target_api_name": "TPMActivityObj", "api_name": "cashing_product_range", "help_text": "", "status": "released"}, "product_range": {"describe_api_name": "TPMActivityObj", "expression_type": "json", "default_is_expression": false, "pattern": "", "description": "", "is_unique": false, "type": "use_range", "default_to_zero": false, "is_required": false, "define_type": "package", "is_single": false, "max_length": 256, "is_index": false, "is_active": true, "default_value": "{\"type\":\"ALL\",\"value\":\"ALL\"}", "label": "产品范围", "target_api_name": "ProductObj", "api_name": "product_range", "help_text": "", "status": "released"}, "activity_unified_case_id": {"describe_api_name": "TPMActivityObj", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "is_required": false, "wheres": [], "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "label": "方案名称", "target_api_name": "TPMActivityUnifiedCaseObj", "target_related_list_name": "target_related_list_TPMActivityObj_TPMActivityUnifiedCaseObj__c", "target_related_list_label": "活动申请", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "activity_unified_case_id", "is_index_field": true, "help_text": "", "status": "new"}, "multi_department_range": {"describe_api_name": "TPMActivityObj", "is_index": true, "is_active": true, "is_encrypted": false, "is_unique": false, "description": "", "where_type": "field", "default_value": "", "label": "参与部门(多选)", "type": "department_many", "is_required": false, "wheres": [], "api_name": "multi_department_range", "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "activity_type": {"describe_api_name": "TPMActivityObj", "is_unique": false, "description": "", "type": "select_one", "is_required": true, "options": [{"font_color": "#2a304d", "not_usable": true, "label": "其他", "value": "other", "config": {"edit": 1, "enable": 1, "remove": 1}}], "define_type": "package", "is_single": false, "maxItems": -1, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "活动类型", "api_name": "activity_type", "is_index_field": false, "help_text": "", "status": "new"}, "extend_obj_data_id": {"describe_api_name": "TPMActivityObj", "is_index": true, "is_required": false, "api_name": "extend_obj_data_id", "is_unique": true, "description": "连接通表的记录ID,扩展字段用", "define_type": "system", "label": "扩展字段在mt_data中的记录ID", "type": "text", "status": "released", "max_length": 64}, "is_deleted": {"describe_api_name": "TPMActivityObj", "is_index": false, "is_unique": false, "description": "is_deleted", "default_value": "false", "label": "is_deleted", "type": "true_or_false", "is_need_convert": false, "is_required": false, "api_name": "is_deleted", "define_type": "system", "status": "released"}, "life_status_before_invalid": {"describe_api_name": "TPMActivityObj", "default_is_expression": false, "pattern": "", "description": "作废前生命状态", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "max_length": 256, "is_index": false, "is_active": true, "default_value": "", "label": "作废前生命状态", "is_need_convert": false, "api_name": "life_status_before_invalid", "help_text": "", "status": "new"}, "object_describe_api_name": {"describe_api_name": "TPMActivityObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "object_describe_api_name", "label": "object_describe_api_name", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "object_describe_api_name", "define_type": "system", "max_length": 200, "status": "released"}, "owner_department": {"describe_api_name": "TPMActivityObj", "default_is_expression": false, "pattern": "", "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": true, "max_length": 100, "is_index": true, "is_active": true, "default_value": "", "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "help_text": "", "status": "new"}, "out_owner": {"describe_api_name": "TPMActivityObj", "is_index": false, "is_active": true, "is_unique": false, "label": "外部负责人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "out_owner", "define_type": "system", "is_single": true, "status": "released"}, "owner": {"describe_api_name": "TPMActivityObj", "is_index": true, "is_active": true, "description": "", "is_unique": false, "label": "负责人", "type": "employee", "is_need_convert": false, "is_required": true, "api_name": "owner", "define_type": "package", "is_single": true, "help_text": "", "status": "new"}, "lock_status": {"describe_api_name": "TPMActivityObj", "is_index": true, "is_active": true, "description": "锁定状态", "is_unique": false, "default_value": "0", "label": "锁定状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "lock_status", "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "package": {"describe_api_name": "TPMActivityObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "package", "label": "package", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "package", "define_type": "system", "max_length": 200, "status": "released"}, "last_modified_time": {"describe_api_name": "TPMActivityObj", "is_index": true, "is_unique": false, "description": "last_modified_time", "label": "最后修改时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "last_modified_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "status": "released"}, "create_time": {"describe_api_name": "TPMActivityObj", "is_index": true, "is_unique": false, "description": "create_time", "label": "创建时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "create_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "status": "released"}, "life_status": {"describe_api_name": "TPMActivityObj", "is_index": true, "is_active": true, "description": "", "is_unique": false, "default_value": "normal", "label": "生命状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "life_status", "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "last_modified_by": {"describe_api_name": "TPMActivityObj", "is_index": true, "is_active": true, "is_unique": false, "label": "最后修改人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "last_modified_by", "define_type": "system", "is_single": true, "status": "released"}, "out_tenant_id": {"describe_api_name": "TPMActivityObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "out_tenant_id", "label": "外部企业", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "out_tenant_id", "define_type": "system", "max_length": 200, "status": "released"}, "version": {"describe_api_name": "TPMActivityObj", "is_index": false, "length": 8, "is_unique": false, "description": "version", "label": "version", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "version", "define_type": "system", "round_mode": 4, "status": "released"}, "created_by": {"describe_api_name": "TPMActivityObj", "is_index": true, "is_active": true, "is_unique": false, "label": "创建人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "created_by", "define_type": "system", "is_single": true, "status": "released"}, "relevant_team": {"describe_api_name": "TPMActivityObj", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "description": "", "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "is_single": false, "help_text": "相关团队", "status": "new"}, "record_type": {"describe_api_name": "TPMActivityObj", "is_index": true, "is_active": true, "description": "record_type", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "is_single": false, "help_text": "", "status": "released"}, "data_own_department": {"describe_api_name": "TPMActivityObj", "is_index": true, "is_active": true, "is_unique": false, "label": "归属部门", "type": "department", "is_need_convert": false, "is_required": false, "api_name": "data_own_department", "define_type": "package", "is_single": true, "status": "released"}, "name": {"describe_api_name": "TPMActivityItemObj", "default_is_expression": false, "pattern": "", "description": "name", "is_unique": true, "type": "text", "default_to_zero": false, "is_required": true, "define_type": "system", "input_mode": "", "is_single": false, "index_name": "name", "max_length": 100, "is_index": true, "is_active": true, "default_value": "", "label": "活动申请名称", "api_name": "name", "is_index_field": false, "help_text": "", "status": "new"}, "order_by": {"describe_api_name": "TPMActivityObj", "is_index": false, "length": 8, "is_unique": false, "description": "order_by", "label": "order_by", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "order_by", "define_type": "system", "round_mode": 4, "status": "released"}, "_id": {"describe_api_name": "TPMActivityObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "_id", "label": "_id", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "_id", "define_type": "system", "max_length": 200, "status": "released"}, "code": {"describe_api_name": "TPMActivityObj", "prefix": "{yyyy}{mm}{dd}-", "is_unique": true, "description": "", "start_number": 1, "type": "auto_number", "is_required": false, "define_type": "package", "postfix": "", "is_single": false, "is_index": true, "is_active": true, "default_value": "{yyyy}{mm}{dd}-0001", "serial_number": 4, "label": "活动编号", "is_abstract": null, "condition": "DAY", "api_name": "code", "is_index_field": false, "help_text": "", "status": "new"}, "closed_status": {"describe_api_name": "TPMActivityObj", "is_index": true, "is_active": true, "is_encrypted": false, "is_unique": false, "default_value": "unclosed", "label": "结案状态", "type": "select_one", "is_required": false, "api_name": "closed_status", "options": [{"font_color": "#91959E", "not_usable": false, "label": "已结案", "value": "closed"}, {"font_color": "#30C776", "not_usable": false, "label": "未结案", "value": "unclosed"}], "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "consignee": {"describe_api_name": "TPMActivityObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "收货人", "api_name": "consignee", "is_show_mask": false, "help_text": "", "status": "new"}, "last_write_off_date": {"describe_api_name": "TPMActivityObj", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "type": "date", "default_to_zero": false, "is_required": false, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "最晚可发起核销时间", "time_zone": "GMT+8", "api_name": "last_write_off_date", "date_format": "yyyy-MM-dd", "help_text": "", "status": "new"}, "application_date": {"describe_api_name": "TPMActivityObj", "default_is_expression": true, "auto_adapt_places": false, "is_unique": false, "type": "date", "default_to_zero": false, "is_required": false, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "TODAY()", "label": "申请日期", "time_zone": "GMT+8", "api_name": "application_date", "date_format": "yyyy-MM-dd", "help_text": "", "status": "new"}, "phone_number": {"describe_api_name": "TPMActivityObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "联系电话", "api_name": "phone_number", "is_show_mask": false, "help_text": "", "status": "new"}, "customer_type": {"describe_api_name": "TPMActivityObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "default_value": "dealerAndStore", "label": "参与活动的客户类型", "type": "select_one", "is_required": false, "api_name": "customer_type", "options": [{"label": "门店", "value": "store"}, {"label": "经销商", "value": "dealer"}, {"label": "经销商和门店", "value": "dealerAndStore"}, {"label": "品牌商", "value": "brand", "not_usable": true}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "recorded_amount": {"return_type": "currency", "describe_api_name": "TPMActivityObj", "auto_adapt_places": false, "remove_mask_roles": {}, "is_unique": false, "type": "count", "decimal_places": 2, "sub_object_describe_apiname": "TPMDealerActivityCostObj", "is_required": false, "wheres": [], "define_type": "package", "is_single": false, "field_api_name": "activity_id", "is_index": true, "default_result": "d_null", "is_active": true, "is_encrypted": false, "count_type": "sum", "count_field_api_name": "confirmed_amount", "label": "活动已入账费用(元)", "count_to_zero": false, "api_name": "recorded_amount", "count_field_type": "currency", "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "remaining_write_off_amount": {"expression_type": "js", "return_type": "number", "describe_api_name": "TPMActivityObj", "auto_adapt_places": false, "remove_mask_roles": {}, "is_unique": false, "type": "formula", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "expression": "$activity_amount$-$activity_actual_amount$", "is_encrypted": false, "label": "剩余核销费用(元)", "api_name": "remaining_write_off_amount", "is_show_mask": false, "help_text": "", "status": "new"}, "attachment": {"describe_api_name": "TPMActivityObj", "is_index": true, "file_amount_limit": 20, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "file_source": ["local", "net"], "label": "活动附件", "type": "file_attachment", "file_size_limit": *********, "is_required": false, "api_name": "attachment", "define_type": "package", "is_single": false, "support_file_types": [], "help_text": "单个文件不得超过100M", "status": "new"}, "account_address_id": {"describe_api_name": "TPMActivityObj", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "is_required": false, "wheres": [], "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "label": "客户地址", "target_api_name": "AccountAddrObj", "target_related_list_name": "target_related_list_TPMActivityObj_AccountAddrObj__c", "target_related_list_label": "活动申请", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "account_address_id", "help_text": "", "status": "new"}, "address": {"describe_api_name": "TPMActivityObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "联系地址", "api_name": "address", "is_show_mask": false, "help_text": "", "status": "new"}, "max_write_off_count": {"describe_api_name": "TPMActivityObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "default_value": "multi", "label": "核销次数", "type": "select_one", "is_required": false, "api_name": "max_write_off_count", "options": [{"label": "一次核销", "value": "once"}, {"label": "多次核销", "value": "multi"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "remarks": {"describe_api_name": "TPMActivityObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "type": "long_text", "default_to_zero": false, "is_required": false, "define_type": "package", "is_single": false, "max_length": 2000, "is_index": true, "is_active": true, "is_encrypted": false, "min_length": 0, "default_value": "", "label": "备注", "api_name": "remarks", "help_text": "", "status": "new"}, "activity_status": {"describe_api_name": "TPMActivityObj", "is_index": true, "is_active": true, "description": "", "is_unique": false, "label": "活动状态", "type": "select_one", "is_required": false, "api_name": "activity_status", "options": [{"font_color": "#FF9B29", "not_usable": false, "label": "审批中", "value": "approval"}, {"font_color": "#FF522A", "not_usable": false, "label": "未生效", "value": "ineffective"}, {"font_color": "#FF9B29", "not_usable": false, "label": "未开始", "value": "schedule"}, {"font_color": "#30C776", "not_usable": false, "label": "进行中", "value": "in_progress"}, {"font_color": "#91959E", "not_usable": false, "label": "已结束", "value": "end"}, {"font_color": "#91959E", "not_usable": false, "label": "已关闭", "value": "closed"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "is_auto_close": {"describe_api_name": "TPMActivityObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "default_value": "false", "label": "是否启用自动结案", "type": "true_or_false", "is_required": false, "api_name": "is_auto_close", "options": [{"label": "是", "value": true}, {"label": "否", "value": false}], "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "dealer_cashing_type": {"describe_api_name": "TPMActivityObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "default_value": "cash", "label": "经销商核销兑付方式", "type": "select_one", "is_required": false, "api_name": "dealer_cashing_type", "options": [{"label": "货补", "value": "goods"}, {"label": "现金", "value": "cash"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "package", "is_single": false, "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "store_cashing_type": {"describe_api_name": "TPMActivityObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "default_value": "cash", "label": "门店奖励兑付方式", "type": "select_one", "is_required": false, "api_name": "store_cashing_type", "options": [{"label": "货补", "value": "goods"}, {"label": "现金", "value": "cash"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "package", "is_single": false, "is_index_field": false, "config": {}, "help_text": "", "status": "new"}}}