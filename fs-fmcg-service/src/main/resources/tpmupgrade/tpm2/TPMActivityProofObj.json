{"store_table_name": "fmcg_tpm_activity_proof", "description": "", "index_version": 1, "is_deleted": false, "define_type": "package", "release_version": "6.4", "package": "CRM", "is_active": true, "display_name": "活动举证", "version": 8, "icon_index": 20, "api_name": "TPMActivityProofObj", "icon_path": "", "is_udef": true, "short_name": "eGA", "fields": {"activity_type": {"describe_api_name": "TPMActivityProofObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "quote_field_type": "select_one", "is_unique": false, "label": "活动类型", "type": "quote", "quote_field": "activity_id__r.activity_type", "is_required": false, "api_name": "activity_type", "define_type": "package", "is_index_field": false, "is_single": false, "status": "new", "help_text": "", "description": ""}, "tenant_id": {"describe_api_name": "TPMActivityProofObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "tenant_id", "label": "tenant_id", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "tenant_id", "define_type": "system", "max_length": 200, "status": "released"}, "visit_id": {"describe_api_name": "TPMActivityProofObj", "default_is_expression": false, "pattern": "", "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "max_length": 100, "is_index": true, "is_active": true, "default_value": "", "label": "外勤编号", "api_name": "visit_id", "help_text": "", "status": "new"}, "remark": {"describe_api_name": "TPMActivityItemObj", "default_is_expression": false, "is_index": true, "is_active": true, "pattern": "", "description": "", "is_unique": false, "default_value": "", "label": "备注", "type": "long_text", "default_to_zero": false, "is_required": false, "api_name": "remark", "define_type": "package", "is_single": false, "help_text": "", "max_length": 2000, "status": "new"}, "lock_rule": {"describe_api_name": "TPMActivityProofObj", "is_index": false, "is_active": true, "description": "锁定规则", "is_unique": false, "default_value": "default_lock_rule", "rules": [], "label": "锁定规则", "type": "lock_rule", "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "is_single": false, "status": "new"}, "proof_images": {"describe_api_name": "TPMActivityProofObj", "file_amount_limit": 9, "is_index": true, "is_active": true, "watermark": [{"type": "variable", "value": "current_user"}, {"type": "variable", "value": "current_time"}, {"type": "variable", "value": "current_address"}], "is_unique": false, "label": "举证照片", "type": "image", "is_watermark": true, "file_size_limit": 20971520, "is_required": false, "api_name": "proof_images", "define_type": "package", "is_single": false, "support_file_types": ["jpg", "gif", "jpeg", "png"], "help_text": "单个图片不得超过20M", "status": "new"}, "audit_status": {"describe_api_name": "TPMActivityProofObj", "is_index": true, "is_active": true, "description": "", "is_unique": false, "default_value": "schedule", "label": "检核结果", "type": "select_one", "is_required": false, "api_name": "audit_status", "options": [{"font_color": "#FF9B29", "not_usable": false, "label": "未检核", "value": "schedule"}, {"font_color": "#30C776", "not_usable": false, "label": "合格", "value": "pass"}, {"font_color": "#FF522A", "not_usable": false, "label": "不合格", "value": "reject"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "lock_user": {"describe_api_name": "TPMActivityProofObj", "is_index": false, "is_active": true, "description": "加锁人", "is_unique": false, "label": "加锁人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "is_single": true, "status": "new"}, "extend_obj_data_id": {"describe_api_name": "TPMActivityProofObj", "is_index": true, "is_required": false, "api_name": "extend_obj_data_id", "is_unique": true, "description": "连接通表的记录ID,扩展字段用", "define_type": "system", "label": "扩展字段在mt_data中的记录ID", "type": "text", "status": "released", "max_length": 64}, "is_deleted": {"describe_api_name": "TPMActivityProofObj", "is_index": false, "is_unique": false, "description": "is_deleted", "default_value": "false", "label": "is_deleted", "type": "true_or_false", "is_need_convert": false, "is_required": false, "api_name": "is_deleted", "define_type": "system", "status": "released"}, "life_status_before_invalid": {"describe_api_name": "TPMActivityProofObj", "is_index": false, "is_active": true, "pattern": "", "description": "作废前生命状态", "is_unique": false, "label": "作废前生命状态", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "life_status_before_invalid", "define_type": "package", "is_single": false, "max_length": 256, "status": "new"}, "object_describe_api_name": {"describe_api_name": "TPMActivityProofObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "object_describe_api_name", "label": "object_describe_api_name", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "object_describe_api_name", "define_type": "system", "max_length": 200, "status": "released"}, "activity_id": {"describe_api_name": "TPMActivityProofObj", "default_is_expression": false, "is_unique": false, "where_type": "field", "type": "object_reference", "is_required": true, "wheres": [], "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "label": "活动名称", "target_api_name": "TPMActivityObj", "target_related_list_name": "target_related_list_TPMActivityProofObj_TPMActivityObj__c", "target_related_list_label": "活动举证", "action_on_target_delete": "set_null", "api_name": "activity_id", "help_text": "", "status": "new"}, "owner_department": {"describe_api_name": "TPMActivityProofObj", "default_is_expression": false, "pattern": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": true, "max_length": 100, "is_index": true, "is_active": true, "default_value": "", "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "help_text": "", "status": "new"}, "actual_total": {"describe_api_name": "TPMActivityProofObj", "default_is_expression": true, "remove_mask_roles": {}, "is_unique": false, "type": "currency", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "max_length": 14, "is_index": true, "is_active": true, "is_encrypted": false, "length": 12, "default_value": "$total$ * $cost_conversion_ratio$", "label": "举证申报费用(元)", "currency_unit": "￥", "api_name": "actual_total", "is_index_field": false, "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new", "description": ""}, "random_audit_status": {"describe_api_name": "TPMActivityProofObj", "is_index": true, "is_active": true, "is_encrypted": false, "is_unique": false, "default_value": "", "label": "抽检状态", "type": "select_one", "is_required": false, "api_name": "random_audit_status", "options": [{"font_color": "#91959E", "not_usable": false, "label": "已抽检", "value": "checked"}, {"font_color": "#FF9B29", "not_usable": false, "label": "未抽检", "value": "unchecked"}, {"font_color": "#2a304d", "not_usable": true, "label": "其他", "value": "other"}], "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "total": {"describe_api_name": "TPMActivityProofObj", "return_type": "currency", "is_unique": false, "description": "", "type": "count", "decimal_places": 2, "sub_object_describe_apiname": "TPMActivityProofDetailObj", "is_required": false, "wheres": [], "define_type": "package", "is_single": false, "field_api_name": "activity_proof_id", "default_result": "d_zero", "is_index": true, "is_active": true, "count_type": "sum", "count_field_api_name": "subtotal", "label": "举证项目总费用(元)", "count_to_zero": false, "api_name": "total", "count_field_type": "formula", "is_index_field": false, "round_mode": 1, "help_text": "", "status": "new"}, "out_owner": {"describe_api_name": "TPMActivityProofObj", "is_index": false, "is_active": true, "is_unique": false, "label": "外部负责人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "out_owner", "define_type": "system", "is_single": true, "status": "released"}, "dealer_id": {"describe_api_name": "TPMActivityProofObj", "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "is_required": false, "wheres": [], "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "default_is_expression": false, "default_value": "", "is_active": true, "label": "经销商", "target_api_name": "AccountObj", "target_related_list_name": "target_related_list_d_TPMActivityProofObj_AccountObj__c", "target_related_list_label": "经销商活动举证", "action_on_target_delete": "set_null", "api_name": "dealer_id", "help_text": "", "status": "new"}, "action_id": {"describe_api_name": "TPMActivityProofObj", "default_is_expression": false, "pattern": "", "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "max_length": 100, "is_index": true, "is_active": true, "default_value": "", "label": "外勤动作编号", "api_name": "action_id", "help_text": "", "status": "new"}, "dealer_activity_cost_id": {"describe_api_name": "TPMActivityProofObj", "default_is_expression": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "is_required": false, "wheres": [], "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "label": "费用核销", "target_api_name": "TPMDealerActivityCostObj", "target_related_list_name": "target_related_list_TPMActivityProofObj__c", "target_related_list_label": "活动举证", "action_on_target_delete": "set_null", "api_name": "dealer_activity_cost_id", "is_index_field": true, "help_text": "", "status": "new"}, "cost_conversion_ratio": {"describe_api_name": "TPMActivityProofObj", "default_is_expression": false, "default_value": "1", "description": "", "is_unique": false, "type": "number", "decimal_places": 3, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "max_length": 14, "is_index": true, "is_active": true, "length": 12, "label": "费用折算比例", "api_name": "cost_conversion_ratio", "round_mode": 4, "help_text": "", "status": "new"}, "activity_agreement_id": {"describe_api_name": "TPMActivityProofObj", "default_is_expression": false, "is_unique": false, "where_type": "field", "type": "object_reference", "is_required": false, "wheres": [{"connector": "OR", "filters": [{"value_type": 2, "operator": "EQ", "field_name": "store_id", "field_values": ["$store_id$"]}, {"value_type": 2, "operator": "EQ", "field_name": "activity_id", "field_values": ["$activity_id$"]}, {"value_type": 0, "operator": "EQ", "field_name": "agreement_status", "field_values": ["in_progress"]}]}], "define_type": "package", "new_cascade_parent_api_name": {"TPMActivityProofObj": ["activity_id", "store_id"]}, "input_mode": "", "is_single": false, "cascade_parent_api_name": ["activity_id", "store_id"], "is_index": true, "is_active": true, "label": "活动协议", "target_api_name": "TPMActivityAgreementObj", "target_related_list_name": "target_related_list_TPMActivityProofObj_TPMActivityAgreementObj__c", "target_related_list_label": "活动举证", "action_on_target_delete": "set_null", "api_name": "activity_agreement_id", "help_text": "", "status": "new"}, "owner": {"describe_api_name": "TPMActivityProofObj", "is_index": true, "is_active": true, "is_unique": false, "label": "负责人", "type": "employee", "is_need_convert": false, "is_required": true, "api_name": "owner", "define_type": "package", "is_single": true, "help_text": "", "status": "new"}, "store_id": {"describe_api_name": "TPMActivityProofObj", "default_is_expression": false, "is_unique": false, "where_type": "field", "type": "object_reference", "is_required": false, "wheres": [], "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "cascade_detail_api_name": {"TPMActivityProofObj": ["activity_agreement_id"]}, "is_active": true, "label": "举证客户", "target_api_name": "AccountObj", "target_related_list_name": "target_related_list_TPMActivityProofObj_AccountObj__c", "target_related_list_label": "活动举证", "action_on_target_delete": "set_null", "api_name": "store_id", "help_text": "", "status": "new"}, "lock_status": {"describe_api_name": "TPMActivityProofObj", "is_index": true, "is_active": true, "description": "锁定状态", "is_unique": false, "default_value": "0", "label": "锁定状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "lock_status", "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "is_single": false, "status": "new"}, "package": {"describe_api_name": "TPMActivityProofObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "package", "label": "package", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "package", "define_type": "system", "max_length": 200, "status": "released"}, "last_modified_time": {"describe_api_name": "TPMActivityProofObj", "is_index": true, "is_unique": false, "description": "last_modified_time", "label": "最后修改时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "last_modified_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "status": "released"}, "create_time": {"describe_api_name": "TPMActivityProofObj", "is_index": true, "is_unique": false, "description": "create_time", "label": "创建时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "create_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "status": "released"}, "life_status": {"describe_api_name": "TPMActivityProofObj", "is_index": true, "is_active": true, "is_unique": false, "default_value": "normal", "label": "生命状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "life_status", "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "last_modified_by": {"describe_api_name": "TPMActivityProofObj", "is_index": true, "is_active": true, "is_unique": false, "label": "最后修改人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "last_modified_by", "define_type": "system", "is_single": true, "status": "released"}, "out_tenant_id": {"describe_api_name": "TPMActivityProofObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "out_tenant_id", "label": "外部企业", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "out_tenant_id", "define_type": "system", "max_length": 200, "status": "released"}, "version": {"describe_api_name": "TPMActivityProofObj", "is_index": false, "length": 8, "is_unique": false, "description": "version", "label": "version", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "version", "define_type": "system", "round_mode": 4, "status": "released"}, "created_by": {"describe_api_name": "TPMActivityProofObj", "is_index": true, "is_active": true, "is_unique": false, "label": "创建人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "created_by", "define_type": "system", "is_single": true, "status": "released"}, "relevant_team": {"describe_api_name": "TPMActivityProofObj", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "is_single": false, "help_text": "相关团队", "status": "new"}, "record_type": {"describe_api_name": "TPMActivityProofObj", "is_index": true, "is_active": true, "description": "record_type", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "is_single": false, "help_text": "", "status": "released"}, "data_own_department": {"describe_api_name": "TPMActivityProofObj", "is_index": true, "is_active": true, "is_unique": false, "label": "归属部门", "type": "department", "is_need_convert": false, "is_required": false, "api_name": "data_own_department", "define_type": "package", "is_single": true, "status": "released"}, "name": {"describe_api_name": "TPMActivityProofObj", "default_is_expression": false, "prefix": "AP-{yyyy}-{mm}-{dd}-", "is_unique": true, "start_number": 1, "type": "auto_number", "default_to_zero": false, "is_required": true, "define_type": "system", "input_mode": "", "postfix": "", "is_single": false, "max_length": 100, "is_index": true, "auto_number_type": "normal", "is_active": true, "default_value": "AP-{yyyy}-{mm}-{dd}-00000001", "serial_number": 8, "label": "举证编号", "condition": "DAY", "api_name": "name", "func_api_name": "", "help_text": "", "status": "new"}, "order_by": {"describe_api_name": "TPMActivityProofObj", "is_index": false, "length": 8, "is_unique": false, "description": "order_by", "label": "order_by", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "order_by", "define_type": "system", "round_mode": 4, "status": "released"}, "_id": {"describe_api_name": "TPMActivityProofObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "_id", "label": "_id", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "_id", "define_type": "system", "max_length": 200, "status": "released"}}}