{"store_table_name": "fmcg_tpm_activity_item_cost_standard", "package": "CRM", "is_active": true, "description": "", "display_name": "费用项目高级计费", "version": 1, "index_version": 1, "icon_index": 0, "is_deleted": false, "api_name": "TPMActivityItemCostStandardObj", "icon_path": "", "is_udef": true, "define_type": "package", "short_name": "aic", "fields": {"tenant_id": {"describe_api_name": "TPMActivityItemCostStandardObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "tenant_id", "label": "tenant_id", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "tenant_id", "define_type": "system", "max_length": 200, "status": "released"}, "lock_rule": {"describe_api_name": "TPMActivityItemCostStandardObj", "is_index": false, "is_active": true, "is_encrypted": false, "description": "锁定规则", "is_unique": false, "default_value": "default_lock_rule", "rules": [], "label": "锁定规则", "type": "lock_rule", "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "is_single": false, "status": "new"}, "calculate_pattern": {"describe_api_name": "TPMActivityItemCostStandardObj", "is_index": true, "is_active": true, "is_encrypted": false, "quote_field_type": "select_one", "is_unique": false, "label": "计费方式", "type": "quote", "quote_field": "activity_item__r.calculate_pattern", "is_required": false, "api_name": "calculate_pattern", "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "is_enable": {"describe_api_name": "TPMActivityItemCostStandardObj", "is_index": true, "is_active": true, "is_encrypted": false, "description": "", "is_unique": false, "default_value": true, "label": "是否启用", "type": "true_or_false", "is_required": false, "api_name": "is_enable", "options": [{"label": "是", "value": true}, {"label": "否", "value": false}], "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "", "status": "new"}, "store_range": {"describe_api_name": "TPMActivityItemCostStandardObj", "expression_type": "json", "default_is_expression": false, "pattern": "", "description": "", "is_unique": false, "type": "use_range", "default_to_zero": false, "is_required": false, "define_type": "package", "is_single": false, "max_length": 256, "is_index": false, "is_active": true, "is_encrypted": false, "default_value": "{\"type\":\"ALL\"}", "label": "适用门店", "target_api_name": "AccountObj", "api_name": "store_range", "help_text": "", "status": "released"}, "amount_standard": {"describe_api_name": "TPMActivityItemCostStandardObj", "default_is_expression": false, "description": "", "is_unique": false, "type": "number", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "max_length": 14, "is_index": true, "is_active": true, "is_encrypted": false, "length": 12, "default_value": "1", "label": "指导数量", "api_name": "amount_standard", "round_mode": 4, "help_text": "", "status": "new"}, "lock_user": {"describe_api_name": "TPMActivityItemCostStandardObj", "is_index": false, "is_active": true, "is_encrypted": false, "description": "加锁人", "is_unique": false, "label": "加锁人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "is_single": true, "status": "new"}, "extend_obj_data_id": {"describe_api_name": "TPMActivityItemCostStandardObj", "is_index": true, "is_required": false, "api_name": "extend_obj_data_id", "is_unique": true, "description": "连接通表的记录ID,扩展字段用", "define_type": "system", "label": "扩展字段在mt_data中的记录ID", "type": "text", "status": "released", "max_length": 64}, "is_deleted": {"describe_api_name": "TPMActivityItemCostStandardObj", "is_index": false, "is_unique": false, "description": "is_deleted", "default_value": "false", "label": "is_deleted", "type": "true_or_false", "is_need_convert": false, "is_required": false, "api_name": "is_deleted", "define_type": "system", "status": "released"}, "life_status_before_invalid": {"describe_api_name": "TPMActivityItemCostStandardObj", "is_index": false, "is_active": true, "is_encrypted": false, "pattern": "", "description": "作废前生命状态", "is_unique": false, "label": "作废前生命状态", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "life_status_before_invalid", "define_type": "package", "is_single": false, "max_length": 256, "status": "new"}, "object_describe_api_name": {"describe_api_name": "TPMActivityItemCostStandardObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "object_describe_api_name", "label": "object_describe_api_name", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "object_describe_api_name", "define_type": "system", "max_length": 200, "status": "released"}, "amount_standard_check": {"describe_api_name": "TPMActivityItemCostStandardObj", "is_index": true, "is_active": true, "is_encrypted": false, "quote_field_type": "true_or_false", "is_unique": false, "label": "数量校验", "type": "quote", "quote_field": "activity_item__r.amount_standard_check", "is_required": false, "api_name": "amount_standard_check", "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "owner_department": {"describe_api_name": "TPMActivityItemCostStandardObj", "default_is_expression": false, "is_index": true, "is_active": true, "is_encrypted": false, "pattern": "", "is_unique": false, "default_value": "", "label": "负责人主属部门", "type": "text", "default_to_zero": false, "is_need_convert": false, "is_required": false, "api_name": "owner_department", "define_type": "package", "input_mode": "", "is_single": true, "max_length": 100, "help_text": "", "status": "new"}, "out_owner": {"describe_api_name": "TPMActivityItemCostStandardObj", "is_index": false, "is_active": true, "is_unique": false, "label": "外部负责人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "out_owner", "define_type": "system", "is_single": true, "status": "released"}, "owner": {"describe_api_name": "TPMActivityItemCostStandardObj", "is_index": true, "is_active": true, "is_encrypted": false, "is_unique": false, "label": "负责人", "type": "employee", "is_need_convert": false, "is_required": true, "api_name": "owner", "define_type": "package", "is_single": true, "help_text": "", "status": "new"}, "lock_status": {"describe_api_name": "TPMActivityItemCostStandardObj", "is_index": true, "is_active": true, "is_encrypted": false, "description": "锁定状态", "is_unique": false, "default_value": "0", "label": "锁定状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "lock_status", "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "is_single": false, "status": "new"}, "package": {"describe_api_name": "TPMActivityItemCostStandardObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "package", "label": "package", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "package", "define_type": "system", "max_length": 200, "status": "released"}, "last_modified_time": {"describe_api_name": "TPMActivityItemCostStandardObj", "is_index": true, "is_unique": false, "description": "last_modified_time", "label": "最后修改时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "last_modified_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "status": "released"}, "create_time": {"describe_api_name": "TPMActivityItemCostStandardObj", "is_index": true, "is_unique": false, "description": "create_time", "label": "创建时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "create_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "status": "released"}, "life_status": {"describe_api_name": "TPMActivityItemCostStandardObj", "is_index": true, "is_active": true, "is_encrypted": false, "is_unique": false, "default_value": "normal", "label": "生命状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "life_status", "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "last_modified_by": {"describe_api_name": "TPMActivityItemCostStandardObj", "is_index": true, "is_active": true, "is_unique": false, "label": "最后修改人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "last_modified_by", "define_type": "system", "is_single": true, "status": "released"}, "out_tenant_id": {"describe_api_name": "TPMActivityItemCostStandardObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "out_tenant_id", "label": "外部企业", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "out_tenant_id", "define_type": "system", "max_length": 200, "status": "released"}, "version": {"describe_api_name": "TPMActivityItemCostStandardObj", "is_index": false, "length": 8, "is_unique": false, "description": "version", "label": "version", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "version", "define_type": "system", "round_mode": 4, "status": "released"}, "created_by": {"describe_api_name": "TPMActivityItemCostStandardObj", "is_index": true, "is_active": true, "is_unique": false, "label": "创建人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "created_by", "define_type": "system", "is_single": true, "status": "released"}, "record_type": {"describe_api_name": "TPMActivityItemCostStandardObj", "is_index": true, "is_active": true, "is_encrypted": false, "description": "record_type", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "is_single": false, "help_text": "", "status": "released"}, "relevant_team": {"describe_api_name": "TPMActivityItemCostStandardObj", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "is_encrypted": false, "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "is_single": false, "help_text": "相关团队", "status": "new"}, "unit": {"describe_api_name": "TPMActivityItemCostStandardObj", "is_index": true, "is_active": true, "is_encrypted": false, "quote_field_type": "select_one", "is_unique": false, "label": "项目单位", "type": "quote", "quote_field": "activity_item__r.unit", "is_required": false, "api_name": "unit", "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "data_own_department": {"describe_api_name": "TPMActivityItemCostStandardObj", "is_index": true, "is_active": true, "is_unique": false, "label": "归属部门", "type": "department", "is_need_convert": false, "is_required": false, "api_name": "data_own_department", "define_type": "package", "is_single": true, "status": "released"}, "cost_standard": {"describe_api_name": "TPMActivityItemCostStandardObj", "default_is_expression": false, "remove_mask_roles": {}, "is_unique": false, "type": "currency", "decimal_places": 2, "default_to_zero": true, "is_required": true, "define_type": "package", "is_single": false, "max_length": 14, "is_index": true, "is_active": true, "is_encrypted": false, "length": 12, "default_value": "", "label": "费用(元)", "currency_unit": "￥", "api_name": "cost_standard", "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "name": {"describe_api_name": "TPMActivityItemCostStandardObj", "default_is_expression": false, "prefix": "{yyyy}-{mm}-{dd}-", "is_unique": true, "start_number": 1, "type": "auto_number", "default_to_zero": false, "is_required": true, "define_type": "system", "input_mode": "", "postfix": "", "is_single": false, "max_length": 100, "is_index": true, "auto_number_type": "normal", "is_active": true, "is_encrypted": false, "default_value": "{yyyy}-{mm}-{dd}-000001", "serial_number": 6, "label": "编号", "condition": "NONE", "api_name": "name", "func_api_name": "", "help_text": "", "status": "new"}, "order_by": {"describe_api_name": "TPMActivityItemCostStandardObj", "is_index": false, "length": 8, "is_unique": false, "description": "order_by", "label": "order_by", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "order_by", "define_type": "system", "round_mode": 4, "status": "released"}, "activity_item": {"describe_api_name": "TPMActivityItemCostStandardObj", "default_is_expression": false, "is_unique": false, "where_type": "field", "type": "object_reference", "is_required": true, "wheres": [], "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "label": "费用项目", "target_api_name": "TPMActivityItemObj", "target_related_list_name": "target_related_list_TPMActivityItemCostStandardObj_TPMActivityItemObj__c", "target_related_list_label": "活动项目费用标准", "action_on_target_delete": "set_null", "api_name": "activity_item", "help_text": "", "status": "new"}, "_id": {"describe_api_name": "TPMActivityItemCostStandardObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "_id", "label": "_id", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "_id", "define_type": "system", "max_length": 200, "status": "released"}}, "release_version": "6.4"}