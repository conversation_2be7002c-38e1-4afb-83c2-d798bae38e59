{"store_table_name": "fmcg_tpm_activity_detail", "description": "", "index_version": 1, "is_deleted": false, "define_type": "package", "release_version": "6.4", "package": "CRM", "is_active": true, "display_name": "参与活动项目", "version": 10, "icon_index": 27, "api_name": "TPMActivityDetailObj", "icon_path": "", "is_udef": true, "short_name": "Btx", "fields": {"standard_display_images": {"describe_api_name": "TPMActivityDetailObj", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "quote_field_type": "image", "is_unique": false, "label": "效果图", "type": "quote", "quote_field": "activity_item_id__r.standard_display_images", "is_required": false, "api_name": "standard_display_images", "define_type": "package", "is_index_field": false, "is_single": false, "status": "new", "help_text": "", "description": ""}, "payment_mode": {"describe_api_name": "TPMActivityDetailObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "default_value": "money", "label": "兑付方式", "type": "select_one", "is_required": false, "api_name": "payment_mode", "options": [{"label": "金钱", "value": "money"}, {"label": "产品", "value": "goods"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "package", "is_index_field": false, "is_single": false, "status": "new", "help_text": ""}, "payment_product": {"describe_api_name": "TPMActivityDetailObj", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "description": "", "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "wheres": [], "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "target_api_name": "ProductObj", "label": "兑付产品", "target_related_list_name": "target_related_list_TPMActivityDetailObj__c", "target_related_list_label": "费用项目", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "payment_product", "is_index_field": true, "status": "new", "help_text": ""}, "payment_product_amount": {"describe_api_name": "TPMActivityDetailObj", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "description": "", "type": "number", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "package", "is_extend": true, "is_single": false, "max_length": 14, "is_index": true, "is_active": true, "is_encrypted": false, "step_value": 1, "display_style": "input", "length": 12, "default_value": "", "label": "兑付产品数量", "api_name": "payment_product_amount", "is_index_field": false, "round_mode": 4, "status": "new", "help_text": ""}, "tenant_id": {"describe_api_name": "TPMActivityDetailObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "tenant_id", "label": "tenant_id", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "tenant_id", "define_type": "system", "max_length": 200, "status": "released"}, "lock_rule": {"describe_api_name": "TPMActivityDetailObj", "is_index": false, "is_active": true, "description": "锁定规则", "is_unique": false, "default_value": "default_lock_rule", "rules": [], "label": "锁定规则", "type": "lock_rule", "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "is_report_item_quantity": {"describe_api_name": "TPMActivityDetailObj", "is_index": true, "is_active": true, "description": "", "is_unique": false, "default_value": "true", "label": "是否上报项目数量", "type": "true_or_false", "is_required": true, "api_name": "is_report_item_quantity", "options": [{"label": "是", "value": true}, {"label": "否", "value": false}], "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "activity_item_id": {"describe_api_name": "TPMActivityDetailObj", "default_is_expression": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "is_required": true, "wheres": [{"connector": "OR", "filters": [{"value_type": 0, "operator": "EQ", "field_name": "is_activated", "field_values": ["true"]}]}], "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "label": "费用项目", "target_api_name": "TPMActivityItemObj", "target_related_list_name": "target_related_list_TPMActivityDetailObj_TPMActivityItemObj__c", "target_related_list_label": "活动方案项目", "action_on_target_delete": "set_null", "api_name": "activity_item_id", "help_text": "", "status": "new"}, "type": {"describe_api_name": "TPMActivityDetailObj", "is_index": true, "is_active": true, "quote_field_type": "select_one", "description": "", "is_unique": false, "label": "项目类型", "type": "quote", "quote_field": "activity_item_id__r.type", "is_required": false, "api_name": "type", "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "lock_user": {"describe_api_name": "TPMActivityDetailObj", "is_index": false, "is_active": true, "description": "加锁人", "is_unique": false, "label": "加锁人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "is_single": true, "help_text": "", "status": "new"}, "extend_obj_data_id": {"describe_api_name": "TPMActivityDetailObj", "is_index": true, "is_required": false, "api_name": "extend_obj_data_id", "is_unique": true, "description": "连接通表的记录ID,扩展字段用", "define_type": "system", "label": "扩展字段在mt_data中的记录ID", "type": "text", "status": "released", "max_length": 64}, "is_deleted": {"describe_api_name": "TPMActivityDetailObj", "is_index": false, "is_unique": false, "description": "is_deleted", "default_value": "false", "label": "is_deleted", "type": "true_or_false", "is_need_convert": false, "is_required": false, "api_name": "is_deleted", "define_type": "system", "status": "released"}, "life_status_before_invalid": {"describe_api_name": "TPMActivityDetailObj", "default_is_expression": false, "pattern": "", "description": "作废前生命状态", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "max_length": 256, "is_index": false, "is_active": true, "default_value": "", "label": "作废前生命状态", "is_need_convert": false, "api_name": "life_status_before_invalid", "help_text": "", "status": "new"}, "code": {"describe_api_name": "TPMActivityDetailObj", "prefix": "{yyyy}{mm}{dd}-", "is_unique": true, "description": "", "start_number": 1, "type": "auto_number", "is_required": false, "define_type": "package", "postfix": "", "is_single": false, "is_index": true, "is_active": true, "default_value": "{yyyy}{mm}{dd}-0001", "serial_number": 4, "label": "编号", "is_abstract": null, "condition": "DAY", "api_name": "code", "is_index_field": false, "help_text": "", "status": "new"}, "object_describe_api_name": {"describe_api_name": "TPMActivityDetailObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "object_describe_api_name", "label": "object_describe_api_name", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "object_describe_api_name", "define_type": "system", "max_length": 200, "status": "released"}, "remark": {"describe_api_name": "TPMActivityItemObj", "default_is_expression": false, "is_index": true, "is_active": true, "pattern": "", "description": "", "is_unique": false, "default_value": "", "label": "项目描述", "type": "long_text", "default_to_zero": false, "is_required": false, "api_name": "remark", "define_type": "package", "is_single": false, "help_text": "", "max_length": 2000, "status": "new"}, "activity_amount_standard": {"describe_api_name": "TPMActivityItemObj", "default_is_expression": true, "default_value": "$activity_item_id__r.amount_standard$", "description": "", "is_unique": false, "type": "number", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "max_length": 14, "is_index": true, "is_active": true, "length": 12, "label": "数量标准", "api_name": "activity_amount_standard", "round_mode": 4, "help_text": "", "status": "new"}, "activity_cost_standard": {"describe_api_name": "TPMActivityDetailObj", "default_is_expression": true, "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "currency", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "max_length": 14, "is_index": true, "is_active": true, "length": 12, "default_value": "$activity_item_id__r.cost_standard$", "label": "费用标准(元)", "currency_unit": "￥", "api_name": "activity_cost_standard", "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "activity_id": {"describe_api_name": "TPMActivityDetailObj", "description": "", "is_unique": false, "type": "master_detail", "is_required": true, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "label": "活动申请", "target_api_name": "TPMActivityObj", "show_detail_button": false, "target_related_list_name": "target_related_list_TPMActivityDetailObj_TPMActivityObj__c", "target_related_list_label": "活动申请项目", "api_name": "activity_id", "is_create_when_master_create": true, "is_required_when_master_create": false, "help_text": "", "status": "new"}, "owner_department": {"describe_api_name": "TPMActivityDetailObj", "default_is_expression": false, "pattern": "", "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": true, "max_length": 100, "is_index": true, "is_active": true, "default_value": "", "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "help_text": "", "status": "new"}, "out_owner": {"describe_api_name": "TPMActivityDetailObj", "is_index": false, "is_active": true, "is_unique": false, "label": "外部负责人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "out_owner", "define_type": "system", "is_single": true, "status": "released"}, "owner": {"describe_api_name": "TPMActivityDetailObj", "is_index": true, "is_active": true, "description": "", "is_unique": false, "label": "负责人", "type": "employee", "is_need_convert": false, "is_required": true, "api_name": "owner", "define_type": "package", "is_single": true, "help_text": "", "status": "new"}, "lock_status": {"describe_api_name": "TPMActivityDetailObj", "is_index": true, "is_active": true, "description": "锁定状态", "is_unique": false, "default_value": "0", "label": "锁定状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "lock_status", "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "package": {"describe_api_name": "TPMActivityDetailObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "package", "label": "package", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "package", "define_type": "system", "max_length": 200, "status": "released"}, "last_modified_time": {"describe_api_name": "TPMActivityDetailObj", "is_index": true, "is_unique": false, "description": "last_modified_time", "label": "最后修改时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "last_modified_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "status": "released"}, "create_time": {"describe_api_name": "TPMActivityDetailObj", "is_index": true, "is_unique": false, "description": "create_time", "label": "创建时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "create_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "status": "released"}, "life_status": {"describe_api_name": "TPMActivityDetailObj", "is_index": true, "is_active": true, "description": "", "is_unique": false, "default_value": "normal", "label": "生命状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "life_status", "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "last_modified_by": {"describe_api_name": "TPMActivityDetailObj", "is_index": true, "is_active": true, "is_unique": false, "label": "最后修改人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "last_modified_by", "define_type": "system", "is_single": true, "status": "released"}, "out_tenant_id": {"describe_api_name": "TPMActivityDetailObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "out_tenant_id", "label": "外部企业", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "out_tenant_id", "define_type": "system", "max_length": 200, "status": "released"}, "version": {"describe_api_name": "TPMActivityDetailObj", "is_index": false, "length": 8, "is_unique": false, "description": "version", "label": "version", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "version", "define_type": "system", "round_mode": 4, "status": "released"}, "created_by": {"describe_api_name": "TPMActivityDetailObj", "is_index": true, "is_active": true, "is_unique": false, "label": "创建人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "created_by", "define_type": "system", "is_single": true, "status": "released"}, "relevant_team": {"describe_api_name": "TPMActivityDetailObj", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "description": "", "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "is_single": false, "help_text": "相关团队", "status": "new"}, "record_type": {"describe_api_name": "TPMActivityDetailObj", "is_index": true, "is_active": true, "description": "record_type", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "is_single": false, "help_text": "", "status": "released"}, "data_own_department": {"describe_api_name": "TPMActivityDetailObj", "is_index": true, "is_active": true, "is_unique": false, "label": "归属部门", "type": "department", "is_need_convert": false, "is_required": false, "api_name": "data_own_department", "define_type": "package", "is_single": true, "status": "released"}, "calculate_pattern": {"describe_api_name": "TPMActivityDetailObj", "is_index": true, "is_active": true, "is_encrypted": false, "description": "", "is_unique": false, "default_value": "1", "label": "计费方式", "type": "select_one", "is_required": false, "api_name": "calculate_pattern", "options": [{"not_usable": false, "label": "费用(元)X实际数量", "value": "1"}, {"not_usable": false, "label": "一口价", "value": "2"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "package", "is_index_field": false, "is_single": false, "config": {}, "help_text": "费用(元)X实际数量（默认值）\n一口价，参与活动的项目费用等于：费用(元)", "status": "new"}, "amount_standard_check": {"describe_api_name": "TPMActivityItemObj", "is_index": true, "is_active": true, "is_encrypted": false, "description": "", "is_unique": false, "default_value": false, "label": "数量校验", "type": "true_or_false", "is_required": false, "api_name": "amount_standard_check", "options": [{"label": "是", "value": true}, {"label": "否", "value": false}], "define_type": "package", "is_index_field": false, "is_single": false, "help_text": "开启数量校验后将在计算举证费用时校验举证中的实际数量是否达标，非达标举证项目将不计算费用。", "status": "new"}, "name": {"describe_api_name": "TPMActivityDetailObj", "default_is_expression": true, "default_value": "$activity_item_id__r.name$", "pattern": "", "description": "name", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": true, "define_type": "system", "input_mode": "", "is_single": false, "index_name": "name", "max_length": 100, "is_index": true, "is_active": true, "label": "活动项目", "api_name": "name", "is_index_field": false, "help_text": "", "status": "new"}, "order_by": {"describe_api_name": "TPMActivityDetailObj", "is_index": false, "length": 8, "is_unique": false, "description": "order_by", "label": "order_by", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "order_by", "define_type": "system", "round_mode": 4, "status": "released"}, "_id": {"describe_api_name": "TPMActivityDetailObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "_id", "label": "_id", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "_id", "define_type": "system", "max_length": 200, "status": "released"}}}