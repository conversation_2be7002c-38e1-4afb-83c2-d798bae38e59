{"store_table_name": "fmcg_tpm_dealer_activity_cost", "description": "", "index_version": 1, "is_deleted": false, "define_type": "package", "release_version": "6.4", "package": "CRM", "is_active": true, "display_name": "费用核销", "version": 10, "icon_index": 11, "api_name": "TPMDealerActivityCostObj", "icon_path": "", "is_udef": true, "short_name": "jEl", "fields": {"tenant_id": {"describe_api_name": "TPMDealerActivityCostObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "tenant_id", "label": "tenant_id", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "tenant_id", "define_type": "system", "max_length": 200, "status": "released"}, "dealer_cashing_type": {"describe_api_name": "TPMDealerActivityCostObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "default_value": "cash", "label": "经销商核销兑付方式", "type": "select_one", "is_required": false, "api_name": "dealer_cashing_type", "options": [{"label": "货补", "value": "goods"}, {"label": "现金", "value": "cash"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "package", "is_single": false, "is_index_field": false, "config": {}, "help_text": "", "status": "new"}, "activity_type": {"describe_api_name": "TPMDealerActivityCostObj", "is_unique": false, "description": "", "type": "select_one", "is_required": false, "options": [{"font_color": "#2a304d", "not_usable": true, "label": "其他", "value": "other"}], "define_type": "package", "is_single": false, "maxItems": -1, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "活动类型", "api_name": "activity_type", "is_index_field": false, "help_text": "", "status": "new"}, "activity_unified_case_id": {"describe_api_name": "TPMDealerActivityCostObj", "default_is_expression": true, "default_value": "$activity_id__r.activity_unified_case_id$", "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "is_required": false, "wheres": [], "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "label": "方案名称", "target_api_name": "TPMActivityUnifiedCaseObj", "target_related_list_name": "target_related_list_TPMDealerActivityCostObj_TPMActivityUnifiedCaseObj__c", "target_related_list_label": "经销商费用核销", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "activity_unified_case_id", "is_index_field": true, "help_text": "", "status": "new"}, "end_date": {"describe_api_name": "TPMDealerActivityCostObj", "is_index": true, "is_active": true, "is_unique": false, "label": "核销时段-期末", "time_zone": "GMT+8", "type": "date", "default_to_zero": false, "is_required": true, "api_name": "end_date", "define_type": "package", "date_format": "yyyy-MM-dd", "is_single": false, "help_text": "", "status": "new"}, "lock_rule": {"describe_api_name": "TPMDealerActivityCostObj", "is_index": false, "is_active": true, "description": "锁定规则", "is_unique": false, "default_value": "default_lock_rule", "rules": [], "label": "锁定规则", "type": "lock_rule", "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "is_single": false, "status": "new"}, "begin_date": {"describe_api_name": "TPMDealerActivityCostObj", "is_index": true, "is_active": true, "is_unique": false, "label": "核销时段-期初", "time_zone": "GMT+8", "type": "date", "default_to_zero": false, "is_required": true, "api_name": "begin_date", "define_type": "package", "date_format": "yyyy-MM-dd", "is_single": false, "help_text": "", "status": "new"}, "remark": {"describe_api_name": "TPMDealerActivityCostObj", "default_is_expression": false, "is_index": true, "is_active": true, "pattern": "", "is_unique": false, "default_value": "", "label": "备注", "type": "long_text", "default_to_zero": false, "is_required": false, "api_name": "remark", "define_type": "package", "is_single": false, "help_text": "", "max_length": 2000, "status": "new"}, "lock_user": {"describe_api_name": "TPMDealerActivityCostObj", "is_index": false, "is_active": true, "description": "加锁人", "is_unique": false, "label": "加锁人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "is_single": true, "status": "new"}, "extend_obj_data_id": {"describe_api_name": "TPMDealerActivityCostObj", "is_index": true, "is_required": false, "api_name": "extend_obj_data_id", "is_unique": true, "description": "连接通表的记录ID,扩展字段用", "define_type": "system", "label": "扩展字段在mt_data中的记录ID", "type": "text", "status": "released", "max_length": 64}, "is_deleted": {"describe_api_name": "TPMDealerActivityCostObj", "is_index": false, "is_unique": false, "description": "is_deleted", "default_value": "false", "label": "is_deleted", "type": "true_or_false", "is_need_convert": false, "is_required": false, "api_name": "is_deleted", "define_type": "system", "status": "released"}, "life_status_before_invalid": {"describe_api_name": "TPMDealerActivityCostObj", "is_index": false, "is_active": true, "pattern": "", "description": "作废前生命状态", "is_unique": false, "label": "作废前生命状态", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "life_status_before_invalid", "define_type": "package", "is_single": false, "max_length": 256, "status": "new"}, "object_describe_api_name": {"describe_api_name": "TPMDealerActivityCostObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "object_describe_api_name", "label": "object_describe_api_name", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "object_describe_api_name", "define_type": "system", "max_length": 200, "status": "released"}, "activity_id": {"describe_api_name": "TPMDealerActivityCostObj", "default_is_expression": false, "is_unique": false, "where_type": "field", "type": "object_reference", "is_required": false, "wheres": [], "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "label": "活动名称", "target_api_name": "TPMActivityObj", "target_related_list_name": "target_related_list_TPMDealerActivityCostObj_TPMActivityObj__c", "target_related_list_label": "活动费用核销", "action_on_target_delete": "set_null", "api_name": "activity_id", "help_text": "", "status": "new"}, "owner_department": {"describe_api_name": "TPMDealerActivityCostObj", "default_is_expression": false, "pattern": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": true, "max_length": 100, "is_index": true, "is_active": true, "default_value": "", "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "help_text": "", "status": "new"}, "out_owner": {"describe_api_name": "TPMDealerActivityCostObj", "is_index": false, "is_active": true, "is_unique": false, "label": "外部负责人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "out_owner", "define_type": "system", "is_single": true, "status": "released"}, "write_off_status": {"describe_api_name": "TPMDealerActivityCostObj", "is_index": true, "is_active": true, "description": "", "is_unique": false, "default_value": "to_be_written_off", "label": "核销结果", "type": "select_one", "is_required": false, "api_name": "write_off_status", "options": [{"font_color": "#FF9B29", "not_usable": false, "label": "待核销", "value": "to_be_written_off"}, {"font_color": "#30C776", "not_usable": false, "label": "审批同意", "value": "pass"}, {"font_color": "#FF522A", "not_usable": false, "label": "审批驳回", "value": "reject"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "confirmed_amount": {"describe_api_name": "TPMDealerActivityCostObj", "default_is_expression": false, "remove_mask_roles": {}, "is_unique": false, "type": "currency", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "max_length": 14, "is_index": true, "is_active": true, "length": 12, "default_value": "", "label": "核销费用(元)", "currency_unit": "￥", "api_name": "confirmed_amount", "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "ref_audit_cost": {"describe_api_name": "TPMDealerActivityCostObj", "default_is_expression": false, "remove_mask_roles": {}, "is_unique": false, "type": "currency", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "max_length": 14, "is_index": true, "is_active": true, "length": 12, "default_value": "", "label": "参考核销费用(元)", "currency_unit": "￥", "api_name": "ref_audit_cost", "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "activity_actual_amount": {"describe_api_name": "TPMDealerActivityCostObj", "is_index": false, "is_active": true, "is_encrypted": false, "quote_field_type": "currency", "is_unique": false, "label": "活动已核销费用(元)", "type": "quote", "quote_field": "activity_id__r.activity_actual_amount", "is_required": false, "api_name": "activity_actual_amount", "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "activity_amount": {"describe_api_name": "TPMDealerActivityCostObj", "is_index": false, "is_active": true, "is_encrypted": false, "quote_field_type": "currency", "is_unique": false, "label": "活动申请费用(元)", "type": "quote", "quote_field": "activity_id__r.activity_amount", "is_required": false, "api_name": "activity_amount", "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "dealer_id": {"describe_api_name": "TPMDealerActivityCostObj", "default_is_expression": false, "is_unique": false, "where_type": "field", "type": "object_reference", "is_required": false, "wheres": [{"connector": "OR", "filters": [{"value_type": 0, "operator": "EQ", "field_name": "record_type", "field_values": ["dealer__c"]}]}], "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "label": "经销商", "target_api_name": "AccountObj", "target_related_list_name": "target_related_list_TPMDealerActivityCostObj_AccountObj__c", "target_related_list_label": "活动费用核销", "action_on_target_delete": "set_null", "api_name": "dealer_id", "help_text": "", "status": "new"}, "owner": {"describe_api_name": "TPMDealerActivityCostObj", "is_index": true, "is_active": true, "is_unique": false, "label": "负责人", "type": "employee", "is_need_convert": false, "is_required": true, "api_name": "owner", "define_type": "package", "is_single": true, "help_text": "", "status": "new"}, "lock_status": {"describe_api_name": "TPMDealerActivityCostObj", "is_index": true, "is_active": true, "description": "锁定状态", "is_unique": false, "default_value": "0", "label": "锁定状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "lock_status", "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "is_single": false, "status": "new"}, "package": {"describe_api_name": "TPMDealerActivityCostObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "package", "label": "package", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "package", "define_type": "system", "max_length": 200, "status": "released"}, "last_modified_time": {"describe_api_name": "TPMDealerActivityCostObj", "is_index": true, "is_unique": false, "description": "last_modified_time", "label": "最后修改时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "last_modified_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "status": "released"}, "create_time": {"describe_api_name": "TPMDealerActivityCostObj", "is_index": true, "is_unique": false, "description": "create_time", "label": "创建时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "create_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "status": "released"}, "life_status": {"describe_api_name": "TPMDealerActivityCostObj", "is_index": true, "is_active": true, "is_unique": false, "default_value": "normal", "label": "生命状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "life_status", "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "last_modified_by": {"describe_api_name": "TPMDealerActivityCostObj", "is_index": true, "is_active": true, "is_unique": false, "label": "最后修改人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "last_modified_by", "define_type": "system", "is_single": true, "status": "released"}, "out_tenant_id": {"describe_api_name": "TPMDealerActivityCostObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "out_tenant_id", "label": "外部企业", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "out_tenant_id", "define_type": "system", "max_length": 200, "status": "released"}, "version": {"describe_api_name": "TPMDealerActivityCostObj", "is_index": false, "length": 8, "is_unique": false, "description": "version", "label": "version", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "version", "define_type": "system", "round_mode": 4, "status": "released"}, "created_by": {"describe_api_name": "TPMDealerActivityCostObj", "is_index": true, "is_active": true, "is_unique": false, "label": "创建人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "created_by", "define_type": "system", "is_single": true, "status": "released"}, "record_type": {"describe_api_name": "TPMDealerActivityCostObj", "is_index": true, "is_active": true, "description": "record_type", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "is_single": false, "help_text": "", "status": "released"}, "relevant_team": {"describe_api_name": "TPMDealerActivityCostObj", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "is_single": false, "help_text": "相关团队", "status": "new"}, "data_own_department": {"describe_api_name": "TPMDealerActivityCostObj", "is_index": true, "is_active": true, "is_unique": false, "label": "归属部门", "type": "department", "is_need_convert": false, "is_required": false, "api_name": "data_own_department", "define_type": "package", "is_single": true, "status": "released"}, "name": {"describe_api_name": "TPMDealerActivityCostObj", "default_is_expression": false, "prefix": "DAC-{yyyy}-{mm}-{dd}-", "is_unique": true, "start_number": 1, "type": "auto_number", "default_to_zero": false, "is_required": true, "define_type": "system", "input_mode": "", "postfix": "", "is_single": false, "max_length": 100, "is_index": true, "auto_number_type": "normal", "is_active": true, "default_value": "DAC-{yyyy}-{mm}-{dd}-000001", "serial_number": 6, "label": "费用核销编号", "condition": "NONE", "api_name": "name", "func_api_name": "", "help_text": "", "status": "new"}, "order_by": {"describe_api_name": "TPMDealerActivityCostObj", "is_index": false, "length": 8, "is_unique": false, "description": "order_by", "label": "order_by", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "order_by", "define_type": "system", "round_mode": 4, "status": "released"}, "audited_amount": {"describe_api_name": "TPMDealerActivityCostObj", "default_is_expression": true, "remove_mask_roles": {}, "is_unique": false, "type": "currency", "decimal_places": 2, "default_to_zero": true, "is_required": true, "define_type": "package", "is_single": false, "max_length": 14, "is_index": true, "is_active": true, "length": 12, "default_value": "$ref_audit_cost$", "label": "申请核销费用(元)", "currency_unit": "￥", "api_name": "audited_amount", "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "_id": {"describe_api_name": "TPMDealerActivityCostObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "_id", "label": "_id", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "_id", "define_type": "system", "max_length": 200, "status": "released"}, "goods_pay_number": {"return_type": "number", "describe_api_name": "TPMDealerActivityCostObj", "auto_adapt_places": false, "remove_mask_roles": {}, "is_unique": false, "type": "count", "decimal_places": 0, "sub_object_describe_apiname": "TPMDealerActivityCashingProductObj", "is_required": false, "wheres": [], "define_type": "package", "is_single": false, "field_api_name": "dealer_activity_cost_id", "is_index": true, "default_result": "d_zero", "is_active": true, "is_encrypted": false, "count_type": "sum", "count_field_api_name": "activity_cashing_quantity", "label": "申请货补产品数量", "count_to_zero": false, "api_name": "goods_pay_number", "count_field_type": "number", "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "enter_account_way": {"describe_api_name": "TPMDealerActivityCostObj", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "type": "select_one", "default_to_zero": false, "is_required": false, "options": [{"label": "自动入账", "value": "auto"}, {"label": "人工入账", "value": "artificial"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "artificial", "label": "入账方式", "api_name": "enter_account_way", "help_text": "", "status": "new"}, "effective_period": {"describe_api_name": "TPMDealerActivityCostObj", "default_is_expression": false, "auto_adapt_places": false, "remove_mask_roles": {}, "is_unique": false, "type": "number", "decimal_places": 0, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "max_length": 14, "is_index": true, "is_active": true, "is_encrypted": false, "display_style": "input", "step_value": 1, "length": 14, "default_value": "", "label": "有效周期(天)", "api_name": "effective_period", "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "effective_date": {"describe_api_name": "TPMDealerActivityCostObj", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "type": "date", "default_to_zero": false, "is_required": false, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "生效日期", "time_zone": "GMT+8", "api_name": "effective_date", "date_format": "yyyy-MM-dd", "help_text": "", "status": "new"}, "expiring_date": {"describe_api_name": "TPMDealerActivityCostObj", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "type": "date", "default_to_zero": false, "is_required": false, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "失效日期", "time_zone": "GMT+8", "api_name": "expiring_date", "date_format": "yyyy-MM-dd", "help_text": "", "status": "new"}, "cost_cashing_quantity": {"return_type": "number", "describe_api_name": "TPMDealerActivityCostObj", "auto_adapt_places": false, "remove_mask_roles": {}, "is_unique": false, "type": "count", "decimal_places": 2, "sub_object_describe_apiname": "TPMDealerActivityCashingProductObj", "is_required": false, "wheres": [], "define_type": "package", "is_single": false, "field_api_name": "dealer_activity_cost_id", "is_index": true, "default_result": "d_zero", "is_active": true, "is_encrypted": false, "count_type": "sum", "count_field_api_name": "cost_cashing_quantity", "label": "核销兑付产品总量", "count_to_zero": false, "api_name": "cost_cashing_quantity", "count_field_type": "number", "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "enter_account": {"describe_api_name": "TPMDealerActivityCostObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "default_value": "false", "label": "核销单入账", "type": "true_or_false", "is_required": false, "api_name": "enter_account", "options": [{"label": "启用", "value": true}, {"label": "禁用", "value": false}], "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "goods_pay_usage": {"describe_api_name": "TPMDealerActivityCostObj", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "type": "select_one", "default_to_zero": false, "is_required": false, "options": [{"label": "按金额兑付", "value": "Amount"}, {"label": "按数量兑付", "value": "Quantity"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "Quantity", "label": "货补使用方式", "api_name": "goods_pay_usage", "help_text": "", "status": "new"}, "cash_usage": {"describe_api_name": "TPMDealerActivityCostObj", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "type": "select_one", "default_to_zero": false, "is_required": false, "options": [{"label": "冲抵回款", "value": "Cash"}, {"label": "分摊折价", "value": "Discount"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "Discount", "label": "现金使用方式", "api_name": "cash_usage", "help_text": "", "status": "new"}, "cashing_fund_account_id": {"describe_api_name": "TPMDealerActivityCostObj", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "is_required": false, "wheres": [{"connector": "OR", "filters": [{"value_type": 0, "operator": "EQ", "field_name": "status", "field_values": ["true"]}]}], "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "兑付入账账户", "target_api_name": "FundAccountObj", "target_related_list_name": "target_related_list_TPMDealerActivityCostObj_FundAccountObj__c", "target_related_list_label": "经销商费用核销入账", "action_on_target_delete": "set_null", "is_need_convert": false, "related_wheres": [], "api_name": "cashing_fund_account_id", "help_text": "", "status": "released"}}}