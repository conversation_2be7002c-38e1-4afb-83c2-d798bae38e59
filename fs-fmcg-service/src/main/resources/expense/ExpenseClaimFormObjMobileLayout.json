{"components": [{"field_section": [{"show_header": true, "form_fields": [{"is_readonly": false, "is_required": false, "render_type": "auto_number", "field_name": "name"}, {"is_readonly": false, "is_required": true, "render_type": "record_type", "field_name": "record_type"}, {"is_readonly": false, "is_required": true, "is_tiled": false, "render_type": "select_one", "field_name": "life_status"}, {"is_readonly": false, "is_required": false, "render_type": "employee", "field_name": "applicant"}, {"is_readonly": false, "is_required": false, "render_type": "department", "field_name": "applied_department"}, {"is_readonly": false, "is_required": false, "render_type": "department", "field_name": "payment_department"}, {"is_readonly": false, "is_required": false, "render_type": "employee", "field_name": "payee"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "bank"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "bank_account"}, {"is_readonly": false, "is_required": false, "render_type": "date_time", "field_name": "application_date"}, {"is_readonly": false, "is_required": false, "render_type": "file_attachment", "field_name": "attachment"}, {"is_readonly": false, "full_line": true, "is_required": false, "render_type": "long_text", "field_name": "description"}], "api_name": "base_field_section__c", "tab_index": "ltr", "column": 2, "header": "基本信息", "collapse": false}, {"show_header": true, "form_fields": [{"is_readonly": true, "is_required": false, "render_type": "count", "field_name": "total_application_amount"}, {"is_readonly": false, "is_required": false, "render_type": "currency", "field_name": "amount_in_words"}], "api_name": "group_xEvMN__c", "tab_index": "ltr", "column": 2, "header": "分组名称vK7", "collapse": false}, {"show_header": true, "form_fields": [{"is_readonly": false, "is_required": false, "render_type": "employee", "field_name": "owner"}, {"is_readonly": false, "is_required": false, "render_type": "department", "field_name": "data_own_department"}, {"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "created_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "create_time"}, {"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "last_modified_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "last_modified_time"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "owner_department"}], "api_name": "group_0416p__c", "tab_index": "ltr", "column": 2, "header": "系统信息", "collapse": true}], "buttons": [], "api_name": "form_component", "related_list_name": "", "column": 2, "header": "表单组件", "nameI18nKey": "paas.udobj.form_component", "type": "form", "grayLimit": 1, "order": 6}, {"buttons": [], "api_name": "ExpenseClaimFormDetailObj__c_md_group_component", "related_list_name": "target_related_list_q31tq__c", "button_info": [{"hidden": [], "render_type": "list_normal", "order": ["Single_Add_button_default", "Batch_Lookup_Add_button_checkin_id__c", "Batch_Lookup_Add_button_account_id__c"]}, {"hidden": [], "render_type": "list_batch", "order": ["Batch_Edit_button_default", "Delete_button_default", "Clone_button_default"]}, {"hidden": [], "render_type": "list_single", "order": ["Delete_button_default", "Clone_button_default", "Tile_button_default", "Insert_button_default"]}], "ref_object_api_name": "ExpenseClaimFormDetailObj__c", "limit": 1, "child_components": [], "header": "费用报销明细", "nameI18nKey": "ExpenseClaimFormDetailObj__c.field.expense_claim_form_id__c.reference_label", "type": "multi_table", "field_api_name": "expense_claim_form_id__c"}, {"components": [["form_component"], ["operation_log"]], "buttons": [], "api_name": "tabs_component", "tabs": [], "limit": 0, "header": "页签容器", "define_type": "container", "type": "tabs"}, {"field_section": [], "buttons": [], "api_name": "head_info", "button_info": [{"hidden": [], "page_type": "create", "render_type": "normal", "order": ["Add_Save_button_default", "Add_Save_Continue_button_default", "Add_Save_Draft_button_default"]}, {"hidden": [], "page_type": "edit", "render_type": "normal", "order": ["Edit_Save"]}], "limit": 1, "header": "标题和按钮", "define_type": "general", "nameI18nKey": "paas.udobj.head_info", "type": "simple"}], "ref_object_api_name": "ExpenseClaimFormObj", "layout_type": "edit", "hidden_buttons": [], "ui_event_ids": [], "is_deleted": false, "default_component": "form_component", "layout_structure": {"layout": [{"components": [["head_info"]], "columns": [{"width": "100%"}]}, {"components": [["form_component", "tabs_component"]], "columns": [{"width": "100%"}]}], "is_tile_help_text": false, "is_open_tile_help_text": false}, "buttons": [], "package": "CRM", "display_name": "费用信息(新建/编辑页)", "is_default": true, "version": 1, "api_name": "layout_ExpenseClaimFormObj_mobile", "layout_description": ""}