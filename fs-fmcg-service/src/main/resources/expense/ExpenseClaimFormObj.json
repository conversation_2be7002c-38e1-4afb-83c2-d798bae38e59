{"store_table_name": "fmcg_expense_claim_form", "description": "新预置对象，用以承载差旅费报销、业务招待费报销、办公费报销、福利费报销、车辆使用费报销、租赁费报销等费用报销。", "enabled_change_order": false, "index_version": 1, "is_deleted": false, "define_type": "package", "release_version": "6.4", "package": "CRM", "is_active": true, "display_name": "高级费用报销", "is_open_display_name": false, "icon_index": 26, "api_name": "ExpenseClaimFormObj", "icon_path": "", "short_name": "ecf", "fields": {"tenant_id": {"describe_api_name": "ExpenseClaimFormObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "tenant_id", "label": "tenant_id", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "tenant_id", "define_type": "system", "max_length": 200, "status": "released"}, "lock_rule": {"describe_api_name": "ExpenseClaimFormObj", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "锁定规则", "is_unique": false, "rules": [], "default_value": "default_lock_rule", "label": "锁定规则", "type": "lock_rule", "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "is_single": false, "status": "new"}, "description": {"describe_api_name": "ExpenseClaimFormObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "description": "", "is_unique": false, "type": "long_text", "default_to_zero": false, "is_required": false, "define_type": "package", "is_single": false, "max_length": 2000, "is_index": true, "is_active": true, "is_encrypted": false, "min_length": 0, "default_value": "", "label": "费用说明", "api_name": "description", "help_text": "", "status": "new"}, "total_application_amount": {"return_type": "currency", "describe_api_name": "ExpenseClaimFormObj", "auto_adapt_places": false, "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "count", "decimal_places": 2, "sub_object_describe_apiname": "ExpenseClaimFormDetailObj", "is_required": false, "wheres": [], "define_type": "package", "is_single": false, "field_api_name": "expense_claim_form_id", "is_index": true, "default_result": "d_zero", "is_active": true, "is_encrypted": false, "count_type": "sum", "count_field_api_name": "amount", "label": "申请报销金额", "count_to_zero": false, "api_name": "total_application_amount", "count_field_type": "currency", "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "lock_user": {"describe_api_name": "ExpenseClaimFormObj", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "加锁人", "is_unique": false, "label": "加锁人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "is_single": true, "status": "new"}, "payee": {"describe_api_name": "ExpenseClaimFormObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "label": "费用收款人", "default_value": "$applicant$", "default_is_expression": true, "type": "employee", "is_required": false, "wheres": [], "api_name": "payee", "define_type": "package", "is_single": true, "help_text": "", "status": "new"}, "extend_obj_data_id": {"describe_api_name": "ExpenseClaimFormObj", "is_index": true, "is_required": false, "api_name": "extend_obj_data_id", "is_unique": true, "description": "连接通表的记录ID,扩展字段用", "define_type": "system", "label": "扩展字段在mt_data中的记录ID", "type": "text", "status": "released", "max_length": 64}, "bank": {"describe_api_name": "ExpenseClaimFormObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "开户行", "api_name": "bank", "is_show_mask": false, "help_text": "", "status": "new"}, "is_deleted": {"describe_api_name": "ExpenseClaimFormObj", "is_index": false, "is_unique": false, "description": "is_deleted", "default_value": "false", "label": "is_deleted", "type": "true_or_false", "is_need_convert": false, "is_required": false, "api_name": "is_deleted", "options": [], "define_type": "system", "status": "released"}, "attachment": {"describe_api_name": "ExpenseClaimFormObj", "is_index": true, "file_amount_limit": 20, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "file_source": ["local", "net"], "label": "附件", "type": "file_attachment", "file_size_limit": *********, "is_required": false, "api_name": "attachment", "define_type": "package", "is_single": false, "support_file_types": [], "help_text": "单个文件不得超过100M", "status": "new"}, "life_status_before_invalid": {"describe_api_name": "ExpenseClaimFormObj", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "pattern": "", "description": "作废前生命状态", "is_unique": false, "label": "作废前生命状态", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "life_status_before_invalid", "define_type": "package", "is_single": false, "max_length": 256, "status": "new"}, "object_describe_api_name": {"describe_api_name": "ExpenseClaimFormObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "object_describe_api_name", "label": "object_describe_api_name", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "object_describe_api_name", "define_type": "system", "max_length": 200, "status": "released"}, "owner_department": {"describe_api_name": "ExpenseClaimFormObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": true, "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "is_show_mask": false, "help_text": "", "status": "new"}, "out_owner": {"describe_api_name": "ExpenseClaimFormObj", "is_index": true, "is_active": true, "is_unique": false, "label": "外部负责人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "out_owner", "define_type": "system", "is_single": true, "status": "released"}, "bank_account": {"describe_api_name": "ExpenseClaimFormObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "银行账号", "api_name": "bank_account", "is_show_mask": false, "help_text": "", "status": "new"}, "owner": {"describe_api_name": "ExpenseClaimFormObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "label": "负责人", "type": "employee", "is_need_convert": false, "is_required": true, "wheres": [], "api_name": "owner", "define_type": "package", "is_single": true, "help_text": "", "status": "new"}, "applied_department": {"describe_api_name": "ExpenseClaimFormObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "label": "申请部门", "type": "department", "default_value": "$applicant__r.main_department$", "default_is_expression": true, "is_required": false, "wheres": [], "api_name": "applied_department", "define_type": "package", "is_single": true, "help_text": "", "status": "new"}, "lock_status": {"describe_api_name": "ExpenseClaimFormObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "锁定状态", "is_unique": false, "default_value": "0", "label": "锁定状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "lock_status", "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "package": {"describe_api_name": "ExpenseClaimFormObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "package", "label": "package", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "package", "define_type": "system", "max_length": 200, "status": "released"}, "last_modified_time": {"describe_api_name": "ExpenseClaimFormObj", "is_index": true, "is_unique": false, "description": "last_modified_time", "label": "最后修改时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "last_modified_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "status": "released"}, "create_time": {"describe_api_name": "ExpenseClaimFormObj", "is_index": true, "is_unique": false, "description": "create_time", "label": "创建时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "create_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "status": "released"}, "life_status": {"describe_api_name": "ExpenseClaimFormObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": "normal", "label": "生命状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "life_status", "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "last_modified_by": {"describe_api_name": "ExpenseClaimFormObj", "is_index": true, "is_active": true, "is_unique": false, "label": "最后修改人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "last_modified_by", "define_type": "system", "is_single": true, "status": "released"}, "out_tenant_id": {"describe_api_name": "ExpenseClaimFormObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "out_tenant_id", "label": "外部企业", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "out_tenant_id", "define_type": "system", "max_length": 200, "status": "released"}, "payment_department": {"describe_api_name": "ExpenseClaimFormObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "label": "付款主体", "type": "department", "is_required": false, "wheres": [], "api_name": "payment_department", "define_type": "package", "is_single": true, "help_text": "", "status": "new"}, "version": {"describe_api_name": "ExpenseClaimFormObj", "is_index": false, "length": 8, "is_unique": false, "description": "version", "label": "version", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "version", "define_type": "system", "round_mode": 4, "status": "released"}, "created_by": {"describe_api_name": "ExpenseClaimFormObj", "is_index": true, "is_active": true, "is_unique": false, "label": "创建人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "created_by", "define_type": "system", "is_single": true, "status": "released"}, "relevant_team": {"describe_api_name": "ExpenseClaimFormObj", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "is_single": false, "help_text": "相关团队", "status": "new"}, "record_type": {"describe_api_name": "ExpenseClaimFormObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "record_type", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "差旅费报销"}, {"is_active": true, "api_name": "record_kYs9j__c", "label": "业务招待费报销"}, {"is_active": true, "api_name": "record_Lu2Cc__c", "label": "办公费报销"}, {"is_active": true, "api_name": "record_bD1Pl__c", "label": "福利费报销"}, {"is_active": true, "api_name": "record_Ix1qT__c", "label": "车辆使用费报销"}, {"is_active": true, "api_name": "record_wGbbj__c", "label": "租赁费报销"}, {"is_active": true, "api_name": "record_kpmx0__c", "label": "其他"}], "define_type": "package", "is_single": false, "help_text": "", "status": "released"}, "applicant": {"describe_api_name": "ExpenseClaimFormObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "label": "费用申请人", "default_value": "$created_by$", "default_is_expression": true, "type": "employee", "is_required": false, "wheres": [], "api_name": "applicant", "define_type": "package", "is_single": true, "help_text": "", "status": "new"}, "data_own_department": {"describe_api_name": "ExpenseClaimFormObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "label": "归属部门", "type": "department", "is_need_convert": false, "is_required": false, "wheres": [], "api_name": "data_own_department", "define_type": "package", "is_single": true, "help_text": "", "status": "new"}, "name": {"describe_api_name": "ExpenseClaimFormObj", "default_is_expression": false, "prefix": "{yyyy}-{mm}-{dd}-", "auto_adapt_places": false, "remove_mask_roles": {}, "description": "", "is_unique": true, "start_number": 1, "type": "auto_number", "default_to_zero": false, "is_required": true, "define_type": "system", "postfix": "", "input_mode": "", "is_single": false, "max_length": 100, "is_index": true, "is_active": true, "auto_number_type": "normal", "is_encrypted": false, "serial_number": 4, "default_value": "{yyyy}-{mm}-{dd}-0001", "label": "费用报销单号", "condition": "DAY", "api_name": "name", "func_api_name": "", "is_show_mask": false, "help_text": "", "status": "new"}, "order_by": {"describe_api_name": "ExpenseClaimFormObj", "is_index": false, "length": 8, "is_unique": false, "description": "order_by", "label": "order_by", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "order_by", "define_type": "system", "round_mode": 4, "status": "released"}, "application_date": {"describe_api_name": "ExpenseClaimFormObj", "default_is_expression": true, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "date_time", "default_to_zero": false, "is_required": false, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "not_use_multitime_zone": false, "default_value": "NOW()", "label": "申请日期", "time_zone": "GMT+8", "api_name": "application_date", "date_format": "yyyy-MM-dd HH:mm", "help_text": "", "status": "new"}, "_id": {"describe_api_name": "ExpenseClaimFormObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "_id", "label": "_id", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "_id", "define_type": "system", "max_length": 200, "status": "released"}, "amount_in_words": {"expression_type": "js", "return_type": "text", "describe_api_name": "ExpenseClaimFormObj", "auto_adapt_places": false, "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "formula", "default_to_zero": false, "is_required": false, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "expression": "NUMBERSTRINGRMB($total_application_amount$)", "is_encrypted": false, "label": "金额（大写）", "api_name": "amount_in_words", "is_show_mask": false, "help_text": "", "status": "new"}}}