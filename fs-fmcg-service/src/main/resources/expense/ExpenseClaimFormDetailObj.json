{"store_table_name": "fmcg_expense_claim_form_detail", "description": "", "enabled_change_order": false, "index_version": 1, "is_deleted": false, "define_type": "package", "release_version": "6.4", "package": "CRM", "is_active": true, "display_name": "高级费用报销明细", "is_open_display_name": false, "icon_index": 0, "api_name": "ExpenseClaimFormDetailObj", "icon_path": "", "short_name": "ecd", "fields": {"tenant_id": {"describe_api_name": "ExpenseClaimFormDetailObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "tenant_id", "label": "tenant_id", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "tenant_id", "define_type": "system", "max_length": 200, "status": "released"}, "end_date": {"describe_api_name": "ExpenseClaimFormDetailObj", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "date", "default_to_zero": false, "is_required": false, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "结束日期", "time_zone": "GMT+8", "api_name": "end_date", "date_format": "yyyy-MM-dd", "help_text": "", "status": "new"}, "lock_rule": {"describe_api_name": "ExpenseClaimFormDetailObj", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "锁定规则", "is_unique": false, "rules": [], "default_value": "default_lock_rule", "label": "锁定规则", "type": "lock_rule", "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "is_single": false, "status": "new"}, "begin_date": {"describe_api_name": "ExpenseClaimFormDetailObj", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "type": "date", "default_to_zero": false, "is_required": false, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "开始日期", "time_zone": "GMT+8", "api_name": "begin_date", "date_format": "yyyy-MM-dd", "help_text": "", "status": "new"}, "checkin_id": {"describe_api_name": "ExpenseClaimFormDetailObj", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "is_required": false, "wheres": [], "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "label": "关联外勤", "target_api_name": "CheckinsObj", "target_related_list_name": "target_related_list_ExpenseClaimFormDetailObj_CheckinsObj__c", "target_related_list_label": "费用报销明细", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "checkin_id", "help_text": "", "status": "new"}, "destination": {"describe_api_name": "ExpenseClaimFormDetailObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "目的地", "api_name": "destination", "is_show_mask": false, "help_text": "", "status": "new"}, "invoice_pictures": {"describe_api_name": "ExpenseClaimFormDetailObj", "auto_adapt_places": false, "description": "", "is_unique": false, "file_source": [], "type": "image", "is_required": false, "define_type": "package", "is_single": false, "support_file_types": ["jpg", "gif", "jpeg", "png"], "is_index": true, "file_amount_limit": 20, "is_active": true, "watermark": [{"type": "variable", "value": "current_user"}, {"type": "variable", "value": "current_time"}, {"type": "variable", "value": "current_address"}], "is_encrypted": false, "file_amount_min_limit": 1, "label": "发票图片", "is_watermark": false, "file_size_limit": 20971520, "is_ocr_recognition": false, "api_name": "invoice_pictures", "is_need_cdn": false, "identify_type": "", "help_text": "单个图片不得超过20M", "status": "new"}, "type": {"describe_api_name": "ExpenseClaimFormDetailObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": "", "label": "类型", "type": "select_one", "is_required": false, "api_name": "type", "options": [{"label": "出差住宿费", "value": "1"}, {"label": "市内交通费、铁路（火车、动车、高铁）", "value": "2"}, {"label": "机票费", "value": "3"}, {"label": "公路水路", "value": "4"}, {"label": "餐费", "value": "5"}, {"label": "住宿费", "value": "6"}, {"label": "礼品费", "value": "7"}, {"label": "办公耗材费", "value": "8"}, {"label": "电话费", "value": "9"}, {"label": "快递物流费", "value": "10"}, {"label": "水电费", "value": "11"}, {"label": "专利年费", "value": "12"}, {"label": "环境卫生费", "value": "13"}, {"label": "团建费", "value": "14"}, {"label": "职工住宿费", "value": "15"}, {"label": "职工餐费", "value": "16"}, {"label": "职工礼品", "value": "17"}, {"label": "职工医药费", "value": "18"}, {"label": "停车费", "value": "19"}, {"label": "保险费", "value": "20"}, {"label": "维修保养费", "value": "21"}, {"label": "燃油费", "value": "22"}, {"label": "路桥费", "value": "23"}, {"label": "房租费", "value": "24"}, {"label": "场地租赁费", "value": "25"}, {"label": "设备租赁费", "value": "26"}, {"label": "车辆租赁费", "value": "27"}, {"is_required": true, "label": "其他", "value": "other"}], "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "lock_user": {"describe_api_name": "ExpenseClaimFormDetailObj", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "加锁人", "is_unique": false, "label": "加锁人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "is_single": true, "status": "new"}, "extend_obj_data_id": {"describe_api_name": "ExpenseClaimFormDetailObj", "is_index": true, "is_required": false, "api_name": "extend_obj_data_id", "is_unique": true, "description": "连接通表的记录ID,扩展字段用", "define_type": "system", "label": "扩展字段在mt_data中的记录ID", "type": "text", "status": "released", "max_length": 64}, "is_deleted": {"describe_api_name": "ExpenseClaimFormDetailObj", "is_index": false, "is_unique": false, "description": "is_deleted", "default_value": "false", "label": "is_deleted", "type": "true_or_false", "is_need_convert": false, "is_required": false, "api_name": "is_deleted", "options": [], "define_type": "system", "status": "released"}, "attachment": {"describe_api_name": "ExpenseClaimFormDetailObj", "is_index": true, "file_amount_limit": 20, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "file_source": ["local", "net"], "label": "相关附件", "type": "file_attachment", "file_size_limit": 104857600, "is_required": false, "api_name": "attachment", "define_type": "package", "is_single": false, "support_file_types": [], "help_text": "单个文件不得超过100M", "status": "new"}, "life_status_before_invalid": {"describe_api_name": "ExpenseClaimFormDetailObj", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "pattern": "", "description": "作废前生命状态", "is_unique": false, "label": "作废前生命状态", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "life_status_before_invalid", "define_type": "package", "is_single": false, "max_length": 256, "status": "new"}, "object_describe_api_name": {"describe_api_name": "ExpenseClaimFormDetailObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "object_describe_api_name", "label": "object_describe_api_name", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "object_describe_api_name", "define_type": "system", "max_length": 200, "status": "released"}, "owner_department": {"describe_api_name": "ExpenseClaimFormDetailObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": true, "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "is_show_mask": false, "help_text": "", "status": "new"}, "out_owner": {"describe_api_name": "ExpenseClaimFormDetailObj", "is_index": true, "is_active": true, "is_unique": false, "label": "外部负责人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "out_owner", "define_type": "system", "is_single": true, "status": "released"}, "invoice_type": {"describe_api_name": "ExpenseClaimFormDetailObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": "vat", "label": "发票类型", "type": "select_one", "is_required": false, "api_name": "invoice_type", "options": [{"label": "普通发票", "value": "normal"}, {"label": "增值税专用发票", "value": "vat"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "owner": {"describe_api_name": "ExpenseClaimFormDetailObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "label": "负责人", "type": "employee", "is_need_convert": false, "is_required": true, "wheres": [], "api_name": "owner", "define_type": "package", "is_single": true, "help_text": "", "status": "new"}, "amount": {"describe_api_name": "ExpenseClaimFormDetailObj", "default_is_expression": false, "auto_adapt_places": false, "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "currency", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "max_length": 14, "is_index": true, "is_active": true, "is_encrypted": false, "length": 12, "default_value": "", "label": "金额（价税合计）", "currency_unit": "￥", "api_name": "amount", "is_show_mask": false, "round_mode": 4, "help_text": "", "status": "new"}, "lock_status": {"describe_api_name": "ExpenseClaimFormDetailObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "锁定状态", "is_unique": false, "default_value": "0", "label": "锁定状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "lock_status", "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "package": {"describe_api_name": "ExpenseClaimFormDetailObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "package", "label": "package", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "package", "define_type": "system", "max_length": 200, "status": "released"}, "last_modified_time": {"describe_api_name": "ExpenseClaimFormDetailObj", "is_index": true, "is_unique": false, "description": "last_modified_time", "label": "最后修改时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "last_modified_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "status": "released"}, "create_time": {"describe_api_name": "ExpenseClaimFormDetailObj", "is_index": true, "is_unique": false, "description": "create_time", "label": "创建时间", "type": "date_time", "time_zone": "", "is_need_convert": false, "is_required": false, "api_name": "create_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "status": "released"}, "life_status": {"describe_api_name": "ExpenseClaimFormDetailObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "default_value": "normal", "label": "生命状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "life_status", "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "is_single": false, "help_text": "", "status": "new"}, "last_modified_by": {"describe_api_name": "ExpenseClaimFormDetailObj", "is_index": true, "is_active": true, "is_unique": false, "label": "最后修改人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "last_modified_by", "define_type": "system", "is_single": true, "status": "released"}, "expense_claim_form_id": {"describe_api_name": "ExpenseClaimFormDetailObj", "auto_adapt_places": false, "description": "", "is_unique": false, "type": "master_detail", "is_required": true, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "label": "费用报销", "target_api_name": "ExpenseClaimFormObj", "show_detail_button": false, "target_related_list_name": "target_related_list_ExpenseClaimFormDetailObj_ExpenseClaimFormObj__c", "target_related_list_label": "费用报销明细", "api_name": "expense_claim_form_id", "is_create_when_master_create": true, "is_required_when_master_create": true, "help_text": "", "status": "new"}, "out_tenant_id": {"describe_api_name": "ExpenseClaimFormDetailObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "out_tenant_id", "label": "外部企业", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "out_tenant_id", "define_type": "system", "max_length": 200, "status": "released"}, "version": {"describe_api_name": "ExpenseClaimFormDetailObj", "is_index": false, "length": 8, "is_unique": false, "description": "version", "label": "version", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "version", "define_type": "system", "round_mode": 4, "status": "released"}, "created_by": {"describe_api_name": "ExpenseClaimFormDetailObj", "is_index": true, "is_active": true, "is_unique": false, "label": "创建人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "created_by", "define_type": "system", "is_single": true, "status": "released"}, "relevant_team": {"describe_api_name": "ExpenseClaimFormDetailObj", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "is_unique": false, "define_type": "package", "description": "成员员工", "label": "成员员工", "type": "employee", "is_single": true, "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "is_unique": false, "define_type": "package", "description": "成员角色", "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "is_unique": false, "define_type": "package", "description": "成员权限类型", "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "is_single": false, "help_text": "相关团队", "status": "new"}, "record_type": {"describe_api_name": "ExpenseClaimFormDetailObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "record_type", "is_unique": false, "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "child_options": [{"type__c": ["5", "6", "7", "other"]}], "label": "业务招待费报销"}, {"is_active": true, "api_name": "record_P1mpZ__c", "child_options": [{"type__c": ["1", "2", "3", "4", "other"]}], "label": "差旅费报销"}, {"is_active": true, "api_name": "record_Ak61I__c", "child_options": [{"type__c": ["15", "16", "17", "18", "other"]}], "label": "福利费报销"}, {"is_active": true, "api_name": "record_0fFNl__c", "child_options": [{"type__c": ["19", "20", "21", "22", "23", "other"]}], "label": "车辆使用费报销"}, {"is_active": true, "api_name": "record_Eex1M__c", "child_options": [{"type__c": ["8", "9", "10", "11", "12", "13", "14", "other"]}], "label": "办公费报销"}, {"is_active": true, "api_name": "record_oT4K1__c", "child_options": [{"type__c": ["24", "25", "26", "27", "other"]}], "label": "租赁费报销"}], "define_type": "package", "is_single": false, "help_text": "", "status": "released"}, "departure_place": {"describe_api_name": "ExpenseClaimFormDetailObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "出发地", "api_name": "departure_place", "is_show_mask": false, "help_text": "", "status": "new"}, "data_own_department": {"describe_api_name": "ExpenseClaimFormDetailObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "label": "归属部门", "type": "department", "is_need_convert": false, "is_required": false, "wheres": [], "api_name": "data_own_department", "define_type": "package", "is_single": true, "help_text": "", "status": "new"}, "account_id": {"describe_api_name": "ExpenseClaimFormDetailObj", "default_is_expression": false, "auto_adapt_places": false, "description": "", "is_unique": false, "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "is_required": false, "wheres": [], "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "label": "客户", "target_api_name": "AccountObj", "target_related_list_name": "target_related_list_ExpenseClaimFormDetailObj_AccountObj__c", "target_related_list_label": "费用报销明细", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "account_id", "help_text": "", "status": "new"}, "car_number": {"describe_api_name": "ExpenseClaimFormDetailObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "remove_mask_roles": {}, "description": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "车牌号", "api_name": "car_number", "is_show_mask": false, "help_text": "", "status": "new"}, "name": {"describe_api_name": "ExpenseClaimFormDetailObj", "default_is_expression": false, "prefix": "{yyyy}-{mm}-{dd}-", "auto_adapt_places": false, "remove_mask_roles": {}, "description": "", "is_unique": true, "start_number": 1, "type": "auto_number", "default_to_zero": false, "is_required": true, "define_type": "system", "postfix": "", "input_mode": "", "is_single": false, "max_length": 100, "is_index": true, "is_active": true, "auto_number_type": "normal", "is_encrypted": false, "serial_number": 5, "default_value": "{yyyy}-{mm}-{dd}-00001", "label": "费用报销明细编号", "condition": "DAY", "api_name": "name", "func_api_name": "", "is_show_mask": false, "help_text": "", "status": "new"}, "order_by": {"describe_api_name": "ExpenseClaimFormDetailObj", "is_index": false, "length": 8, "is_unique": false, "description": "order_by", "label": "order_by", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "order_by", "define_type": "system", "round_mode": 4, "status": "released"}, "_id": {"describe_api_name": "ExpenseClaimFormDetailObj", "is_index": false, "is_active": true, "pattern": "", "is_unique": false, "description": "_id", "label": "_id", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "_id", "define_type": "system", "max_length": 200, "status": "released"}, "remarks": {"describe_api_name": "ExpenseClaimFormDetailObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "description": "", "is_unique": false, "type": "long_text", "default_to_zero": false, "is_required": false, "define_type": "package", "is_single": false, "max_length": 2000, "is_index": true, "is_active": true, "is_encrypted": false, "min_length": 0, "default_value": "", "label": "备注", "api_name": "remarks", "help_text": "", "status": "new"}}}