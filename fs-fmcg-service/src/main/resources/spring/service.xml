<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns="http://www.springframework.org/schema/beans"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://www.springframework.org/schema/aop
        http://www.springframework.org/schema/aop/spring-aop-3.0.xsd">

    <aop:aspectj-autoproxy/>

    <bean id="fmcgServiceProfiler" class="com.facishare.fmcg.provider.log.FmcgServiceProfiler"/>

    <aop:config proxy-target-class="true">
        <aop:aspect ref="fmcgServiceProfiler" order="0">
            <aop:around method="profile"
                        pointcut="execution(* com.facishare.fmcg.provider.impl..*ServiceImpl.*(..))"/>
        </aop:aspect>
    </aop:config>
</beans>
