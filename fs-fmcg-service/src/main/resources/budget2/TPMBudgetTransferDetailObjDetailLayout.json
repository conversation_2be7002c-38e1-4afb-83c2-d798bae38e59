{"components": [{"field_section": [], "buttons": [], "api_name": "relevant_team_component", "related_list_name": "", "header": "相关团队", "nameI18nKey": "paas.udobj.constant.relevant_team", "type": "user_list"}, {"field_section": [], "buttons": [], "api_name": "operation_log", "related_list_name": "", "header": "修改记录", "nameI18nKey": "paas.udobj.modify_log", "type": "related_record"}, {"components": [["form_component"], ["operation_log"]], "buttons": [], "api_name": "tabs_erA2j__c", "tabs": [{"api_name": "form_component_gGnjs__c", "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info"}, {"api_name": "operation_log_Yph22__c", "header": "修改记录", "nameI18nKey": "paas.udobj.modify_log"}], "header": "页签容器", "type": "tabs"}, {"field_section": [], "buttons": [{"action_type": "default", "api_name": "Edit_button_default", "label": "编辑"}, {"action_type": "default", "api_name": "SaleRecord_button_default", "label": "销售记录"}, {"action_type": "default", "api_name": "Dial_button_default", "label": "打电话"}, {"action_type": "default", "api_name": "ChangeOwner_button_default", "label": "更换负责人"}, {"action_type": "default", "api_name": "StartBPM_button_default", "label": "发起业务流程"}, {"action_type": "default", "api_name": "Abolish_button_default", "label": "作废"}, {"action_type": "default", "api_name": "StartStagePropellor_button_default", "label": "发起阶段推进器"}, {"action_type": "default", "api_name": "Lock_button_default", "label": "锁定"}, {"action_type": "default", "api_name": "Unlock_button_default", "label": "解锁"}, {"action_type": "default", "api_name": "Clone_button_default", "label": "复制"}, {"action_type": "default", "api_name": "ChangePartnerOwner_button_default", "label": "更换外部负责人"}, {"action_type": "default", "api_name": "SendMail_button_default"}, {"action_type": "default", "api_name": "Discuss_button_default"}, {"action_type": "default", "api_name": "Remind_button_default"}, {"action_type": "default", "api_name": "Schedule_button_default"}, {"action_type": "default", "api_name": "Print_button_default"}], "api_name": "head_info", "related_list_name": "", "header": "标题和按钮", "nameI18nKey": "paas.udobj.head_info", "exposedButton": 3, "type": "simple"}, {"field_section": [{"render_type": "employee", "field_name": "owner"}, {"render_type": "date_time", "field_name": "last_modified_time"}, {"render_type": "record_type", "field_name": "record_type"}], "buttons": [], "api_name": "top_info", "related_list_name": "", "header": "摘要信息", "type": "top_info"}, {"field_section": [{"show_header": true, "form_fields": [{"is_readonly": false, "is_required": false, "render_type": "auto_number", "field_name": "name"}, {"is_readonly": true, "is_required": false, "is_tiled": false, "render_type": "select_one", "field_name": "transfer_type"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference", "field_name": "transfer_out_budget_account_id"}, {"is_readonly": false, "is_required": false, "render_type": "object_reference", "field_name": "transfer_in_budget_account_id"}, {"is_readonly": false, "full_line": true, "is_required": false, "render_type": "long_text", "field_name": "budget_change_detail"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "operate_time"}, {"is_readonly": false, "full_line": true, "is_required": false, "render_type": "long_text", "field_name": "remarks"}], "api_name": "base_field_section__c", "tab_index": "ltr", "column": 2, "header": "基本信息"}, {"show_header": true, "form_fields": [{"is_readonly": false, "is_required": false, "render_type": "currency", "field_name": "amount"}, {"is_readonly": false, "is_required": false, "render_type": "select_one", "field_name": "operation_status"}, {"is_readonly": true, "is_required": false, "render_type": "currency", "field_name": "amount_before_transfer_in"}, {"is_readonly": true, "is_required": false, "render_type": "formula", "field_name": "amount_after_transfer_in"}, {"is_readonly": true, "is_required": false, "render_type": "currency", "field_name": "amount_before_transfer_out"}, {"is_readonly": true, "is_required": false, "render_type": "formula", "field_name": "amount_after_transfer_out"}, {"is_readonly": true, "is_required": false, "is_tiled": false, "render_type": "select_one", "field_name": "operation_status"}], "api_name": "group_transfer_default_adjust__c", "tab_index": "ltr", "column": 2, "header": "调整信息"}, {"show_header": true, "form_fields": [{"is_readonly": false, "is_required": true, "render_type": "employee", "field_name": "owner"}, {"is_readonly": false, "is_required": false, "render_type": "department", "field_name": "data_own_department"}, {"is_readonly": false, "is_required": true, "is_tiled": false, "render_type": "select_one", "field_name": "life_status"}, {"is_readonly": false, "is_required": true, "render_type": "record_type", "field_name": "record_type"}, {"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "created_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "create_time"}, {"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "last_modified_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "last_modified_time"}], "api_name": "group_transfer_default_system__c", "tab_index": "ltr", "column": 2, "header": "系统信息"}], "buttons": [], "api_name": "form_component", "related_list_name": "", "column": 2, "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info", "type": "form"}], "ref_object_api_name": "TPMBudgetTransferDetailObj", "layout_type": "detail", "hidden_buttons": [], "ui_event_ids": [], "is_deleted": false, "default_component": "form_component", "layout_structure": {"layout": [{"components": [["head_info"]], "columns": [{"width": "100%"}]}, {"components": [["top_info", "tabs_erA2j__c"], ["relevant_team_component"]], "columns": [{"width": "auto"}, {"width": "500px", "retractable": true}]}]}, "buttons": [], "package": "CRM", "display_name": "调整布局", "is_default": true, "version": 3, "api_name": "layout_default_TPMBudgetTransferDetailObj__c", "layout_description": ""}