{"store_table_name": "fmcg_tpm_budget_carry_forward", "package": "CRM", "is_active": true, "description": "", "display_name": "预算结转", "is_open_display_name": false, "index_version": 1, "icon_index": 0, "is_deleted": false, "api_name": "TPMBudgetCarryForwardObj", "icon_path": "", "is_udef": false, "define_type": "package", "short_name": "prr", "fields": {"tenant_id": {"is_index": false, "is_active": true, "pattern": "", "description": "tenant_id", "is_unique": false, "label": "tenant_id", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "tenant_id", "define_type": "system", "index_name": "tenant_id", "status": "released", "max_length": 200}, "source_quarter": {"describe_api_name": "TPMBudgetCarryForwardObj", "is_index": true, "default_is_expression": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "default_value": "", "label": "结转期间-年季", "time_zone": "GMT+8", "type": "date", "default_to_zero": false, "is_required": false, "api_name": "source_quarter", "define_type": "package", "date_format": "yyyy-MM-dd", "is_index_field": false, "is_single": false, "status": "new", "help_text": ""}, "lock_rule": {"describe_api_name": "TPMBudgetCarryForwardObj", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "锁定规则", "default_value": "default_lock_rule", "rules": [], "label": "锁定规则", "type": "lock_rule", "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "is_index_field": false, "is_single": false, "status": "new"}, "target_month": {"describe_api_name": "TPMBudgetCarryForwardObj", "is_index": true, "default_is_expression": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "default_value": "", "label": "结转到期间-年月", "time_zone": "GMT+8", "type": "date", "default_to_zero": false, "is_required": false, "api_name": "target_month", "define_type": "package", "date_format": "yyyy-MM-dd", "is_index_field": false, "is_single": false, "status": "new", "help_text": ""}, "lock_user": {"describe_api_name": "TPMBudgetCarryForwardObj", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "加锁人", "label": "加锁人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "is_index_field": false, "is_single": true, "status": "new"}, "is_deleted": {"is_index": false, "is_need_convert": false, "is_required": false, "api_name": "is_deleted", "define_type": "system", "description": "is_deleted", "is_unique": false, "default_value": false, "label": "is_deleted", "type": "true_or_false", "index_name": "is_del", "status": "released"}, "life_status_before_invalid": {"describe_api_name": "TPMBudgetCarryForwardObj", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "description": "作废前生命状态", "label": "作废前生命状态", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "life_status_before_invalid", "define_type": "package", "is_index_field": false, "is_single": false, "status": "new", "max_length": 256}, "object_describe_api_name": {"is_index": false, "is_active": true, "pattern": "", "description": "object_describe_api_name", "is_unique": false, "label": "object_describe_api_name", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "object_describe_api_name", "define_type": "system", "index_name": "api_name", "status": "released", "max_length": 200}, "out_owner": {"is_index": false, "is_active": true, "is_unique": false, "label": "外部负责人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "out_owner", "define_type": "system", "is_single": true, "config": {"display": 1}, "index_name": "o_owner", "status": "released", "description": ""}, "owner_department": {"describe_api_name": "TPMBudgetCarryForwardObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "description": "", "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": true, "index_name": "owner_dept", "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "is_index_field": false, "status": "new", "help_text": ""}, "owner": {"describe_api_name": "TPMBudgetCarryForwardObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "where_type": "field", "label": "负责人", "type": "employee", "is_need_convert": false, "wheres": [], "is_required": true, "api_name": "owner", "define_type": "package", "is_index_field": false, "is_single": true, "index_name": "owner", "status": "new", "help_text": ""}, "last_modified_time": {"is_index": true, "create_time": 1658720776266, "description": "last_modified_time", "is_unique": false, "label": "最后修改时间", "time_zone": "", "type": "date_time", "is_need_convert": false, "is_required": false, "api_name": "last_modified_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "index_name": "md_time", "status": "released"}, "package": {"is_index": false, "is_active": true, "create_time": 1658720776266, "pattern": "", "description": "package", "is_unique": false, "label": "package", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "package", "define_type": "system", "index_name": "pkg", "status": "released", "max_length": 200}, "lock_status": {"describe_api_name": "TPMBudgetCarryForwardObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "锁定状态", "default_value": "0", "label": "锁定状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "lock_status", "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "is_index_field": false, "is_single": false, "config": {}, "status": "new", "help_text": ""}, "create_time": {"is_index": true, "description": "create_time", "is_unique": false, "label": "创建时间", "time_zone": "", "type": "date_time", "is_need_convert": false, "is_required": false, "api_name": "create_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "index_name": "crt_time", "status": "released"}, "target_quarter": {"describe_api_name": "TPMBudgetCarryForwardObj", "is_index": true, "default_is_expression": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "default_value": "", "label": "结转到期间-年季", "time_zone": "GMT+8", "type": "date", "default_to_zero": false, "is_required": false, "api_name": "target_quarter", "define_type": "package", "date_format": "yyyy-MM-dd", "is_index_field": false, "is_single": false, "status": "new", "help_text": ""}, "amount": {"describe_api_name": "TPMBudgetCarryForwardObj", "return_type": "currency", "auto_adapt_places": false, "is_unique": false, "description": "", "type": "count", "sub_object_describe_apiname": "TPMBudgetCarryForwardDetailObj", "decimal_places": 2, "wheres": [], "is_required": false, "define_type": "package", "is_single": false, "field_api_name": "budget_carry_forward_id", "default_result": "d_zero", "is_index": true, "is_active": true, "create_time": 1658730569982, "is_encrypted": false, "count_type": "sum", "count_field_api_name": "amount", "label": "结转总金额", "count_to_zero": false, "api_name": "amount", "count_field_type": "currency", "is_index_field": false, "round_mode": 4, "status": "new", "help_text": ""}, "life_status": {"describe_api_name": "TPMBudgetCarryForwardObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "default_value": "normal", "label": "生命状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "life_status", "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "is_index_field": false, "is_single": false, "config": {}, "status": "new", "help_text": ""}, "last_modified_by": {"is_index": true, "is_active": true, "is_need_convert": true, "is_required": false, "api_name": "last_modified_by", "define_type": "system", "is_unique": false, "label": "最后修改人", "is_single": true, "type": "employee", "index_name": "md_by", "status": "released", "description": ""}, "out_tenant_id": {"is_index": false, "is_active": true, "pattern": "", "description": "out_tenant_id", "is_unique": false, "label": "外部企业", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "out_tenant_id", "define_type": "system", "config": {"display": 0}, "index_name": "o_ei", "status": "released", "max_length": 200}, "target_year": {"describe_api_name": "TPMBudgetCarryForwardObj", "is_index": true, "default_is_expression": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "default_value": "", "label": "结转到期间-年", "time_zone": "GMT+8", "type": "date", "default_to_zero": false, "is_required": false, "api_name": "target_year", "define_type": "package", "date_format": "yyyy-MM-dd", "is_index_field": false, "is_single": false, "status": "new", "help_text": ""}, "created_by": {"is_index": true, "is_active": true, "is_need_convert": true, "create_time": 1658720776266, "is_required": false, "api_name": "created_by", "define_type": "system", "is_unique": false, "label": "创建人", "is_single": true, "type": "employee", "index_name": "crt_by", "status": "released", "description": ""}, "version": {"is_index": false, "create_time": 1658720776266, "length": 8, "description": "version", "is_unique": false, "label": "version", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "version", "define_type": "system", "index_name": "version", "round_mode": 4, "status": "released"}, "record_type": {"describe_api_name": "TPMBudgetCarryForwardObj", "is_index": true, "is_active": true, "create_time": 1658720776083, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "record_type", "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "is_index_field": false, "is_single": false, "config": {}, "index_name": "r_type", "status": "released", "help_text": ""}, "relevant_team": {"describe_api_name": "TPMBudgetCarryForwardObj", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "description": "成员员工", "define_type": "package", "is_unique": false, "label": "成员员工", "is_single": true, "type": "employee", "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "description": "成员角色", "define_type": "package", "is_unique": false, "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "description": "成员权限类型", "define_type": "package", "is_unique": false, "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "create_time": 1658720776085, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "is_index_field": false, "is_single": false, "index_name": "a_team", "status": "new", "help_text": "相关团队"}, "source_year": {"describe_api_name": "TPMBudgetCarryForwardObj", "is_index": true, "default_is_expression": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "default_value": "", "label": "结转期间-年", "time_zone": "GMT+8", "type": "date", "default_to_zero": false, "is_required": false, "api_name": "source_year", "define_type": "package", "date_format": "yyyy-MM-dd", "is_index_field": false, "is_single": false, "status": "new", "help_text": ""}, "source_month": {"describe_api_name": "TPMBudgetCarryForwardObj", "is_index": true, "default_is_expression": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "default_value": "", "label": "结转期间-年月", "time_zone": "GMT+8", "type": "date", "default_to_zero": false, "is_required": false, "api_name": "source_month", "define_type": "package", "date_format": "yyyy-MM-dd", "is_index_field": false, "is_single": false, "status": "new", "help_text": ""}, "data_own_department": {"describe_api_name": "TPMBudgetCarryForwardObj", "is_index": true, "is_active": true, "create_time": 1658720776078, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "where_type": "field", "label": "归属部门", "type": "department", "is_need_convert": false, "wheres": [], "is_required": false, "api_name": "data_own_department", "define_type": "package", "is_index_field": false, "is_single": true, "index_name": "data_owner_dept_id", "status": "new", "help_text": ""}, "budget_type_id": {"describe_api_name": "TPMBudgetCarryForwardObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "default_value": "", "label": "预算类型", "type": "select_one", "is_required": false, "api_name": "budget_type_id", "options": [], "define_type": "package", "is_index_field": false, "is_single": false, "config": {}, "status": "new", "help_text": ""}, "budget_node_id": {"describe_api_name": "TPMBudgetCarryForwardObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "default_value": "", "label": "预算层级", "type": "select_one", "is_required": false, "api_name": "budget_node_id", "options": [], "define_type": "package", "is_index_field": false, "is_single": false, "config": {}, "status": "new", "help_text": ""}, "carry_forward_time": {"describe_api_name": "TPMBudgetCarryForwardObj", "default_is_expression": false, "is_index": true, "is_active": true, "description": "", "is_unique": false, "default_value": "", "label": "结转完成时间", "time_zone": "GMT+8", "type": "date_time", "default_to_zero": false, "is_required": false, "api_name": "carry_forward_time", "define_type": "package", "date_format": "yyyy-MM-dd HH:mm:ss", "is_single": false, "help_text": "", "status": "new"}, "carry_forward_status": {"describe_api_name": "TPMBudgetCarryForwardObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "default_value": "schedule", "label": "结转状态", "type": "select_one", "is_required": false, "api_name": "carry_forward_status", "options": [{"font_color": "#8b8a8a", "label": "未生效", "value": "ineffective"}, {"font_color": "#8b8a8a", "label": "已解冻", "value": "unfrozen"}, {"font_color": "#ff522a", "label": "解冻失败", "value": "unfrozen_failed"}, {"font_color": "#936de3", "label": "待结转", "value": "schedule"}, {"font_color": "#ff8000", "label": "结转中", "value": "processing"}, {"font_color": "#ff8000", "label": "已冻结", "value": "frozen"}, {"font_color": "#ff522a", "label": "冻结失败", "value": "frozen_failed"}, {"font_color": "#ff522a", "label": "结转失败", "value": "failed"}, {"font_color": "#30c776", "label": "结转成功", "value": "success"}], "define_type": "package", "is_index_field": false, "is_single": false, "config": {}, "status": "new", "help_text": ""}, "name": {"describe_api_name": "TPMBudgetCarryForwardObj", "default_is_expression": false, "prefix": "BCF-{yyyy}-{mm}-{dd}-", "auto_adapt_places": false, "is_unique": true, "description": "", "start_number": 1, "type": "auto_number", "default_to_zero": false, "is_required": true, "define_type": "system", "input_mode": "", "postfix": "", "is_single": false, "index_name": "name", "max_length": 100, "is_index": true, "auto_number_type": "normal", "is_active": true, "is_encrypted": false, "default_value": "BCF-{yyyy}-{mm}-{dd}-000001", "serial_number": 6, "label": "预算结转编号", "condition": "NONE", "api_name": "name", "func_api_name": "", "is_index_field": false, "status": "new", "help_text": ""}, "_id": {"is_index": false, "is_active": true, "pattern": "", "description": "_id", "is_unique": false, "label": "_id", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "_id", "define_type": "system", "index_name": "_id", "status": "released", "max_length": 200}}, "release_version": "6.4"}