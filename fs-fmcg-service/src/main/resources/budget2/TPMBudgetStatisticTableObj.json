{"store_table_name": "fmcg_tpm_budget_statistic_table", "package": "CRM", "is_active": true, "description": "", "display_name": "预算汇总表", "is_open_display_name": false, "index_version": 1, "version": 0, "icon_index": 0, "is_deleted": false, "api_name": "TPMBudgetStatisticTableObj", "icon_path": "", "is_udef": true, "define_type": "package", "short_name": "Jmn", "fields": {"tenant_id": {"describe_api_name": "TPMBudgetStatisticTableObj", "is_index": false, "is_active": true, "pattern": "", "description": "tenant_id", "is_unique": false, "label": "tenant_id", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "tenant_id", "define_type": "system", "status": "released", "max_length": 200}, "lock_rule": {"describe_api_name": "TPMBudgetStatisticTableObj", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "锁定规则", "default_value": "default_lock_rule", "rules": [], "label": "锁定规则", "type": "lock_rule", "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "is_single": false, "status": "new"}, "available_amount": {"describe_api_name": "TPMBudgetStatisticTableObj", "return_type": "number", "auto_adapt_places": false, "is_unique": false, "type": "count", "sub_object_describe_apiname": "TPMBudgetAccountObj", "decimal_places": 2, "wheres": [{"connector": "OR", "filters": [{"value_type": 0, "operator": "EQ", "field_values": ["enable"], "field_name": "budget_status"}, {"value_type": 0, "operator": "EQ", "field_values": ["normal"], "field_name": "life_status"}]}], "is_required": false, "define_type": "package", "is_single": false, "field_api_name": "budget_statistic_table_id", "default_result": "d_null", "is_index": true, "is_active": true, "is_encrypted": false, "count_type": "sum", "count_field_api_name": "available_amount", "label": "可用金额", "count_to_zero": false, "api_name": "available_amount", "count_field_type": "formula", "round_mode": 4, "status": "new", "help_text": ""}, "budget_department": {"describe_api_name": "TPMBudgetStatisticTableObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "where_type": "field", "label": "预算部门", "type": "department", "wheres": [], "is_required": false, "api_name": "budget_department", "define_type": "package", "is_single": true, "status": "new", "help_text": ""}, "lock_user": {"describe_api_name": "TPMBudgetStatisticTableObj", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "加锁人", "label": "加锁人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "is_single": true, "status": "new"}, "extend_obj_data_id": {"describe_api_name": "TPMBudgetStatisticTableObj", "is_index": true, "is_required": false, "api_name": "extend_obj_data_id", "is_unique": true, "description": "连接通表的记录ID,扩展字段用", "define_type": "system", "label": "扩展字段在mt_data中的记录ID", "type": "text", "status": "released", "max_length": 64}, "is_deleted": {"describe_api_name": "TPMBudgetStatisticTableObj", "is_index": false, "description": "is_deleted", "is_unique": false, "default_value": "false", "label": "is_deleted", "type": "true_or_false", "is_need_convert": false, "is_required": false, "api_name": "is_deleted", "define_type": "system", "status": "released"}, "life_status_before_invalid": {"describe_api_name": "TPMBudgetStatisticTableObj", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "description": "作废前生命状态", "label": "作废前生命状态", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "life_status_before_invalid", "define_type": "package", "is_single": false, "status": "new", "max_length": 256}, "object_describe_api_name": {"describe_api_name": "TPMBudgetStatisticTableObj", "is_index": false, "is_active": true, "pattern": "", "description": "object_describe_api_name", "is_unique": false, "label": "object_describe_api_name", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "object_describe_api_name", "define_type": "system", "status": "released", "max_length": 200}, "out_owner": {"describe_api_name": "TPMBudgetStatisticTableObj", "is_index": false, "is_active": true, "is_unique": false, "label": "外部负责人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "out_owner", "define_type": "system", "is_single": true, "status": "released"}, "owner_department": {"describe_api_name": "TPMBudgetStatisticTableObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": true, "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "status": "new", "help_text": ""}, "owner": {"describe_api_name": "TPMBudgetStatisticTableObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "where_type": "field", "label": "负责人", "type": "employee", "is_need_convert": false, "wheres": [], "is_required": true, "api_name": "owner", "define_type": "package", "is_single": true, "status": "new", "help_text": ""}, "last_modified_time": {"describe_api_name": "TPMBudgetStatisticTableObj", "is_index": true, "description": "last_modified_time", "is_unique": false, "label": "最后修改时间", "time_zone": "", "type": "date_time", "is_need_convert": false, "is_required": false, "api_name": "last_modified_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "status": "released"}, "package": {"describe_api_name": "TPMBudgetStatisticTableObj", "is_index": false, "is_active": true, "pattern": "", "description": "package", "is_unique": false, "label": "package", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "package", "define_type": "system", "status": "released", "max_length": 200}, "lock_status": {"describe_api_name": "TPMBudgetStatisticTableObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "锁定状态", "default_value": "0", "label": "锁定状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "lock_status", "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "is_single": false, "status": "new"}, "create_time": {"describe_api_name": "TPMBudgetStatisticTableObj", "is_index": true, "description": "create_time", "is_unique": false, "label": "创建时间", "time_zone": "", "type": "date_time", "is_need_convert": false, "is_required": false, "api_name": "create_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "status": "released"}, "statistic_period": {"describe_api_name": "TPMBudgetStatisticTableObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "default_value": "", "label": "汇总期间", "type": "select_one", "is_required": false, "api_name": "statistic_period", "options": [{"label": "年", "value": "year"}, {"label": "季", "value": "quarter"}, {"label": "月", "value": "month"}, {"not_usable": true, "label": "其他", "value": "other"}], "define_type": "package", "is_single": false, "status": "new", "help_text": ""}, "life_status": {"describe_api_name": "TPMBudgetStatisticTableObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "default_value": "normal", "label": "生命状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "life_status", "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "is_single": false, "status": "new", "help_text": ""}, "last_modified_by": {"describe_api_name": "TPMBudgetStatisticTableObj", "is_index": true, "is_active": true, "is_unique": false, "label": "最后修改人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "last_modified_by", "define_type": "system", "is_single": true, "status": "released"}, "out_tenant_id": {"describe_api_name": "TPMBudgetStatisticTableObj", "is_index": false, "is_active": true, "pattern": "", "description": "out_tenant_id", "is_unique": false, "label": "外部企业", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "out_tenant_id", "define_type": "system", "status": "released", "max_length": 200}, "created_by": {"describe_api_name": "TPMBudgetStatisticTableObj", "is_index": true, "is_active": true, "is_unique": false, "label": "创建人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "created_by", "define_type": "system", "is_single": true, "status": "released"}, "version": {"describe_api_name": "TPMBudgetStatisticTableObj", "is_index": false, "length": 8, "description": "version", "is_unique": false, "label": "version", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "version", "define_type": "system", "round_mode": 4, "status": "released"}, "relevant_team": {"describe_api_name": "TPMBudgetStatisticTableObj", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "description": "成员员工", "define_type": "package", "is_unique": false, "label": "成员员工", "is_single": true, "type": "employee", "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "description": "成员角色", "define_type": "package", "is_unique": false, "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "description": "成员权限类型", "define_type": "package", "is_unique": false, "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "is_single": false, "status": "new", "help_text": "相关团队"}, "record_type": {"describe_api_name": "TPMBudgetStatisticTableObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "record_type", "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "is_single": false, "status": "released", "help_text": ""}, "frozen_amount": {"describe_api_name": "TPMBudgetStatisticTableObj", "return_type": "number", "auto_adapt_places": false, "is_unique": false, "type": "count", "sub_object_describe_apiname": "TPMBudgetAccountObj", "decimal_places": 2, "wheres": [{"connector": "OR", "filters": [{"value_type": 0, "operator": "EQ", "field_values": ["enable"], "field_name": "budget_status"}, {"value_type": 0, "operator": "EQ", "field_values": ["normal"], "field_name": "life_status"}]}], "is_required": false, "define_type": "package", "is_single": false, "field_api_name": "budget_statistic_table_id", "default_result": "d_null", "is_index": true, "is_active": true, "is_encrypted": false, "count_type": "sum", "count_field_api_name": "frozen_amount", "label": "冻结金额", "count_to_zero": false, "api_name": "frozen_amount", "count_field_type": "formula", "round_mode": 4, "status": "new", "help_text": ""}, "data_own_department": {"describe_api_name": "TPMBudgetStatisticTableObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "where_type": "field", "label": "归属部门", "type": "department", "is_need_convert": false, "wheres": [], "is_required": false, "api_name": "data_own_department", "define_type": "package", "is_single": true, "status": "new", "help_text": ""}, "total_amount": {"describe_api_name": "TPMBudgetStatisticTableObj", "return_type": "number", "expression_type": "js", "is_index": true, "expression": "$available_amount$+$used_amount$+$frozen_amount$", "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "label": "总金额", "type": "formula", "decimal_places": 2, "default_to_zero": true, "is_required": false, "api_name": "total_amount", "define_type": "package", "is_single": false, "status": "new", "help_text": ""}, "name": {"describe_api_name": "TPMBudgetStatisticTableObj", "default_is_expression": false, "prefix": "{yyyy}-{mm}-{dd}-", "auto_adapt_places": false, "is_unique": true, "start_number": 1, "type": "auto_number", "default_to_zero": false, "is_required": true, "define_type": "system", "input_mode": "", "postfix": "", "is_single": false, "max_length": 100, "is_index": true, "auto_number_type": "normal", "is_active": true, "is_encrypted": false, "default_value": "{yyyy}-{mm}-{dd}-0001", "serial_number": 4, "label": "预算汇总表编号", "condition": "NONE", "api_name": "name", "func_api_name": "", "status": "new", "help_text": ""}, "order_by": {"describe_api_name": "TPMBudgetStatisticTableObj", "is_index": false, "length": 8, "description": "order_by", "is_unique": false, "label": "order_by", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "order_by", "define_type": "system", "round_mode": 4, "status": "released"}, "used_amount": {"describe_api_name": "TPMBudgetStatisticTableObj", "return_type": "number", "auto_adapt_places": false, "is_unique": false, "type": "count", "sub_object_describe_apiname": "TPMBudgetAccountObj", "decimal_places": 2, "wheres": [{"connector": "OR", "filters": [{"value_type": 0, "operator": "EQ", "field_values": ["normal"], "field_name": "life_status"}]}], "is_required": false, "define_type": "package", "is_single": false, "field_api_name": "budget_statistic_table_id", "default_result": "d_null", "is_index": true, "is_active": true, "is_encrypted": false, "count_type": "sum", "count_field_api_name": "used_amount", "label": "已用金额", "count_to_zero": false, "api_name": "used_amount", "count_field_type": "formula", "round_mode": 4, "status": "new", "help_text": ""}, "_id": {"describe_api_name": "TPMBudgetStatisticTableObj", "is_index": false, "is_active": true, "pattern": "", "description": "_id", "is_unique": false, "label": "_id", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "_id", "define_type": "system", "status": "released", "max_length": 200}, "budget_statistic_table": {"describe_api_name": "TPMBudgetStatisticTableObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "预算汇总表名称", "api_name": "budget_statistic_table", "status": "new", "help_text": ""}, "product_category_id": {"describe_api_name": "TPMBudgetStatisticTableObj", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "description": "", "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "wheres": [], "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "target_api_name": "ProductCategoryObj", "label": "品类", "target_related_list_name": "target_related_list_TPMBudgetStatisticTableObj_ProductCategoryObj__c", "target_related_list_label": "预算汇总表", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "product_category_id", "status": "new", "help_text": ""}, "product_id": {"describe_api_name": "TPMBudgetStatisticTableObj", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "description": "", "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "wheres": [], "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "target_api_name": "ProductObj", "label": "产品", "target_related_list_name": "target_related_list_TPMBudgetStatisticTableObj_ProductObj__c", "target_related_list_label": "预算汇总表", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "product_id", "status": "new", "help_text": ""}, "budget_subject_id": {"describe_api_name": "TPMBudgetStatisticTableObj", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "description": "", "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "wheres": [], "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "target_api_name": "TPMBudgetBusinessSubjectObj", "label": "科目", "target_related_list_name": "target_related_list_TPMBudgetStatisticTableObj_TPMBudgetBusinessSubjectObj__c", "target_related_list_label": "预算汇总表", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "budget_subject_id", "status": "new", "help_text": ""}, "dealer_id": {"describe_api_name": "TPMBudgetStatisticTableObj", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "description": "", "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "wheres": [], "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "target_api_name": "AccountObj", "label": "经销商", "target_related_list_name": "target_related_list_TPMBudgetStatisticTableObj_AccountObj__c", "target_related_list_label": "预算汇总表", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "dealer_id", "status": "new", "help_text": ""}, "budget_period_month": {"describe_api_name": "TPMBudgetStatisticTableObj", "is_index": true, "default_is_expression": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "default_value": "", "label": "预算期间-年月", "time_zone": "GMT+8", "type": "date", "default_to_zero": false, "is_required": false, "api_name": "budget_period_month", "define_type": "package", "date_format": "yyyy-MM", "is_single": false, "status": "new", "help_text": ""}, "budget_period_year": {"describe_api_name": "TPMBudgetStatisticTableObj", "is_index": true, "default_is_expression": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "default_value": "", "label": "预算期间-年", "time_zone": "GMT+8", "type": "date", "default_to_zero": false, "is_required": false, "api_name": "budget_period_year", "define_type": "package", "date_format": "yyyy", "is_single": false, "status": "new", "help_text": ""}, "budget_period_quarter": {"describe_api_name": "TPMBudgetStatisticTableObj", "is_index": true, "default_is_expression": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "default_value": "", "label": "预算期间-年季", "time_zone": "GMT+8", "type": "date", "default_to_zero": false, "is_required": false, "api_name": "budget_period_quarter", "define_type": "package", "date_format": "yyyy-QQQ", "is_single": false, "status": "new", "help_text": ""}, "budget_type_id": {"describe_api_name": "TPMBudgetStatisticTableObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "default_value": "", "label": "预算类型", "type": "select_one", "is_required": false, "api_name": "budget_type_id", "options": [], "define_type": "package", "is_index_field": false, "is_single": false, "config": {}, "status": "new", "help_text": ""}, "budget_node_id": {"describe_api_name": "TPMBudgetStatisticTableObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "default_value": "", "label": "预算节点", "type": "select_one", "is_required": false, "api_name": "budget_node_id", "options": [], "define_type": "package", "is_index_field": false, "is_single": false, "config": {}, "status": "new", "help_text": ""}}, "release_version": "6.4"}