{"store_table_name": "fmcg_tpm_budget_carry_forward_detail", "package": "CRM", "is_active": true, "description": "", "display_name": "预算结转明细", "version": 0, "is_open_display_name": false, "index_version": 1, "icon_index": 0, "is_deleted": false, "api_name": "TPMBudgetCarryForwardDetailObj", "icon_path": "", "is_udef": false, "define_type": "package", "short_name": "dJr", "fields": {"tenant_id": {"is_index": false, "is_active": true, "pattern": "", "description": "tenant_id", "is_unique": false, "label": "tenant_id", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "tenant_id", "define_type": "system", "index_name": "tenant_id", "status": "released", "max_length": 200}, "lock_rule": {"describe_api_name": "TPMBudgetCarryForwardDetailObj", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "锁定规则", "default_value": "default_lock_rule", "rules": [], "label": "锁定规则", "type": "lock_rule", "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "is_index_field": false, "is_single": false, "status": "new"}, "source_budget_account_id": {"describe_api_name": "TPMBudgetCarryForwardDetailObj", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "description": "", "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "wheres": [], "is_required": true, "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "target_api_name": "TPMBudgetAccountObj", "label": "转出预算表", "target_related_list_name": "target_related_list_TPMBudgetCarryForwardDetailObj_from__c", "target_related_list_label": "预算结转明细（转出方）", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "source_budget_account_id", "is_index_field": true, "status": "new", "help_text": ""}, "lock_user": {"describe_api_name": "TPMBudgetCarryForwardDetailObj", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "加锁人", "label": "加锁人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "is_index_field": false, "is_single": true, "status": "new"}, "carry_forward_status": {"describe_api_name": "TPMBudgetCarryForwardDetailObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "default_value": "schedule", "label": "结转状态", "type": "select_one", "is_required": false, "api_name": "carry_forward_status", "options": [{"font_color": "#8b8a8a", "label": "未生效", "value": "ineffective"}, {"font_color": "#8b8a8a", "label": "已解冻", "value": "unfrozen"}, {"font_color": "#ff522a", "label": "解冻失败", "value": "unfrozen_failed"}, {"font_color": "#936de3", "label": "待结转", "value": "schedule"}, {"font_color": "#ff8000", "label": "结转中", "value": "processing"}, {"font_color": "#ff8000", "label": "已冻结", "value": "frozen"}, {"font_color": "#ff522a", "label": "冻结失败", "value": "frozen_failed"}, {"font_color": "#ff522a", "label": "结转失败", "value": "failed"}, {"font_color": "#30c776", "label": "结转成功", "value": "success"}], "define_type": "package", "is_index_field": false, "is_single": false, "config": {}, "status": "new", "help_text": ""}, "budget_carry_forward_id": {"describe_api_name": "TPMBudgetCarryForwardDetailObj", "auto_adapt_places": false, "is_unique": false, "description": "", "type": "master_detail", "is_required": true, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "target_api_name": "TPMBudgetCarryForwardObj", "label": "预算结转", "show_detail_button": false, "target_related_list_name": "target_related_list_TPMBudgetCarryForwardDetailObj__c", "target_related_list_label": "预算结转明细", "api_name": "budget_carry_forward_id", "is_create_when_master_create": true, "is_required_when_master_create": true, "is_index_field": true, "status": "new", "help_text": ""}, "is_deleted": {"is_index": false, "is_need_convert": false, "create_time": *************, "is_required": false, "api_name": "is_deleted", "define_type": "system", "description": "is_deleted", "is_unique": false, "default_value": false, "label": "is_deleted", "type": "true_or_false", "index_name": "is_del", "status": "released"}, "life_status_before_invalid": {"describe_api_name": "TPMBudgetCarryForwardDetailObj", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "description": "作废前生命状态", "label": "作废前生命状态", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "life_status_before_invalid", "define_type": "package", "is_index_field": false, "is_single": false, "status": "new", "max_length": 256}, "object_describe_api_name": {"is_index": false, "is_active": true, "pattern": "", "description": "object_describe_api_name", "is_unique": false, "label": "object_describe_api_name", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "object_describe_api_name", "define_type": "system", "index_name": "api_name", "status": "released", "max_length": 200}, "after_carry_forward_amount": {"describe_api_name": "TPMBudgetCarryForwardDetailObj", "default_is_expression": true, "auto_adapt_places": false, "remove_mask_roles": {}, "is_unique": false, "description": "", "type": "currency", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "max_length": 14, "is_index": true, "is_active": true, "is_encrypted": false, "length": 12, "default_value": "$target_budget_account_id__r.available_amount$+$source_budget_account_id__r.available_amount$", "label": "结转后金额", "currency_unit": "￥", "api_name": "after_carry_forward_amount", "is_index_field": false, "is_show_mask": false, "round_mode": 4, "status": "new", "help_text": ""}, "out_owner": {"is_index": false, "is_active": true, "create_time": *************, "is_unique": false, "label": "外部负责人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "out_owner", "define_type": "system", "is_single": true, "config": {"display": 1}, "index_name": "o_owner", "status": "released", "description": ""}, "owner_department": {"describe_api_name": "TPMBudgetCarryForwardDetailObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "description": "", "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "is_single": true, "index_name": "owner_dept", "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "is_index_field": false, "status": "new", "help_text": ""}, "carry_forward_failure_message": {"describe_api_name": "TPMBudgetCarryForwardDetailObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "type": "long_text", "default_to_zero": false, "is_required": false, "define_type": "package", "is_single": false, "max_length": 2000, "is_index": true, "is_active": true, "min_length": 0, "is_encrypted": false, "default_value": "", "label": "结转异常信息", "api_name": "carry_forward_failure_message", "status": "new", "help_text": ""}, "owner": {"describe_api_name": "TPMBudgetCarryForwardDetailObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "where_type": "field", "label": "负责人", "type": "employee", "is_need_convert": false, "wheres": [], "is_required": true, "api_name": "owner", "define_type": "package", "is_index_field": false, "is_single": true, "index_name": "owner", "status": "new", "help_text": ""}, "last_modified_time": {"is_index": true, "description": "last_modified_time", "is_unique": false, "label": "最后修改时间", "time_zone": "", "type": "date_time", "is_need_convert": false, "is_required": false, "api_name": "last_modified_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "index_name": "md_time", "status": "released"}, "package": {"is_index": false, "is_active": true, "pattern": "", "description": "package", "is_unique": false, "label": "package", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "package", "define_type": "system", "index_name": "pkg", "status": "released", "max_length": 200}, "lock_status": {"describe_api_name": "TPMBudgetCarryForwardDetailObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "锁定状态", "default_value": "0", "label": "锁定状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "lock_status", "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "is_index_field": false, "is_single": false, "config": {}, "status": "new", "help_text": ""}, "create_time": {"is_index": true, "description": "create_time", "is_unique": false, "label": "创建时间", "time_zone": "", "type": "date_time", "is_need_convert": false, "is_required": false, "api_name": "create_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "index_name": "crt_time", "status": "released"}, "amount": {"describe_api_name": "TPMBudgetCarryForwardDetailObj", "default_is_expression": true, "auto_adapt_places": false, "remove_mask_roles": {}, "is_unique": false, "description": "", "type": "currency", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "max_length": 14, "is_index": true, "is_active": true, "create_time": *************, "is_encrypted": false, "length": 12, "default_value": "$source_budget_account_id__r.available_amount$", "label": "可结转金额", "currency_unit": "￥", "api_name": "amount", "is_index_field": false, "is_show_mask": false, "round_mode": 4, "status": "new", "help_text": ""}, "life_status": {"describe_api_name": "TPMBudgetCarryForwardDetailObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "default_value": "normal", "label": "生命状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "life_status", "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "is_index_field": false, "is_single": false, "config": {}, "status": "new", "help_text": ""}, "last_modified_by": {"is_index": true, "is_active": true, "is_need_convert": true, "create_time": *************, "is_required": false, "api_name": "last_modified_by", "define_type": "system", "is_unique": false, "label": "最后修改人", "is_single": true, "type": "employee", "index_name": "md_by", "status": "released", "description": ""}, "out_tenant_id": {"is_index": false, "is_active": true, "create_time": *************, "pattern": "", "description": "out_tenant_id", "is_unique": false, "label": "外部企业", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "out_tenant_id", "define_type": "system", "config": {"display": 0}, "index_name": "o_ei", "status": "released", "max_length": 200}, "created_by": {"is_index": true, "is_active": true, "is_need_convert": true, "create_time": *************, "is_required": false, "api_name": "created_by", "define_type": "system", "is_unique": false, "label": "创建人", "is_single": true, "type": "employee", "index_name": "crt_by", "status": "released", "description": ""}, "version": {"is_index": false, "create_time": *************, "length": 8, "description": "version", "is_unique": false, "label": "version", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "version", "define_type": "system", "index_name": "version", "round_mode": 4, "status": "released"}, "record_type": {"describe_api_name": "TPMBudgetCarryForwardDetailObj", "is_index": true, "is_active": true, "create_time": 1658730030856, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "record_type", "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "is_index_field": false, "is_single": false, "config": {}, "index_name": "r_type", "status": "released", "help_text": ""}, "relevant_team": {"describe_api_name": "TPMBudgetCarryForwardDetailObj", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "description": "成员员工", "define_type": "package", "is_unique": false, "label": "成员员工", "is_single": true, "type": "employee", "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "description": "成员角色", "define_type": "package", "is_unique": false, "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "description": "成员权限类型", "define_type": "package", "is_unique": false, "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "create_time": 1658730030858, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "is_index_field": false, "is_single": false, "index_name": "a_team", "status": "new", "help_text": "相关团队"}, "data_own_department": {"describe_api_name": "TPMBudgetCarryForwardDetailObj", "is_index": true, "is_active": true, "create_time": 1658730030851, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "where_type": "field", "label": "归属部门", "type": "department", "is_need_convert": false, "wheres": [], "is_required": false, "api_name": "data_own_department", "define_type": "package", "is_index_field": false, "is_single": true, "index_name": "data_owner_dept_id", "status": "new", "help_text": ""}, "name": {"describe_api_name": "TPMBudgetCarryForwardDetailObj", "default_is_expression": false, "prefix": "BCFD-{yyyy}-{mm}-{dd}-", "auto_adapt_places": false, "is_unique": true, "description": "", "start_number": 1, "type": "auto_number", "default_to_zero": false, "is_required": true, "define_type": "system", "input_mode": "", "postfix": "", "is_single": false, "index_name": "name", "max_length": 100, "is_index": true, "auto_number_type": "normal", "is_active": true, "create_time": 1658730030888, "is_encrypted": false, "default_value": "BCFD-{yyyy}-{mm}-{dd}-000001", "serial_number": 6, "label": "预算结转明细编号", "condition": "DAY", "api_name": "name", "func_api_name": "", "is_index_field": false, "status": "new", "help_text": ""}, "_id": {"is_index": false, "is_active": true, "create_time": *************, "pattern": "", "description": "_id", "is_unique": false, "label": "_id", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "_id", "define_type": "system", "index_name": "_id", "status": "released", "max_length": 200}, "target_budget_account_id": {"describe_api_name": "TPMBudgetCarryForwardDetailObj", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "description": "", "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "wheres": [], "is_required": false, "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "create_time": *************, "is_encrypted": false, "target_api_name": "TPMBudgetAccountObj", "label": "转入预算表", "target_related_list_name": "target_related_list_TPMBudgetCarryForwardDetailObj_to__c", "target_related_list_label": "预算结转明细（转入方）", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "target_budget_account_id", "is_index_field": true, "status": "new", "help_text": ""}}, "release_version": "6.4"}