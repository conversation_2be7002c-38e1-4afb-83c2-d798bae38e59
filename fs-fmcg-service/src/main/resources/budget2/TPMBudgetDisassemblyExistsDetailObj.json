{"store_table_name": "fmcg_tpm_budget_disassembly_exists_detail", "package": "CRM", "is_active": true, "description": "", "display_name": "预算拆解明细-已有预算表", "is_open_display_name": false, "index_version": 1, "icon_index": 0, "is_deleted": false, "api_name": "TPMBudgetDisassemblyExistsDetailObj", "icon_path": "", "is_udef": true, "define_type": "package", "short_name": "CBv", "fields": {"tenant_id": {"describe_api_name": "TPMBudgetDisassemblyExistsDetailObj", "is_index": false, "is_active": true, "pattern": "", "description": "tenant_id", "is_unique": false, "label": "tenant_id", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "tenant_id", "define_type": "system", "status": "released", "max_length": 200}, "lock_rule": {"describe_api_name": "TPMBudgetDisassemblyExistsDetailObj", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "锁定规则", "default_value": "default_lock_rule", "rules": [], "label": "锁定规则", "type": "lock_rule", "is_need_convert": false, "is_required": false, "api_name": "lock_rule", "define_type": "package", "label_r": "锁定规则", "is_single": false, "status": "new"}, "budget_node_id": {"describe_api_name": "TPMBudgetDisassemblyExistsDetailObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "", "default_value": "", "label": "预算层级", "type": "select_one", "is_required": false, "api_name": "budget_node_id", "options": [], "define_type": "package", "is_index_field": false, "is_single": false, "config": {}, "status": "new", "help_text": ""}, "budget_type": {"describe_api_name": "TPMBudgetDisassemblyExistsDetailObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "quote_field_type": "select_one", "is_unique": false, "label": "预算类型", "type": "quote", "quote_field": "budget_account_id__r.budget_type_id", "is_required": false, "api_name": "budget_type", "define_type": "package", "is_single": false, "status": "new", "help_text": ""}, "available_amount": {"describe_api_name": "TPMBudgetDisassemblyExistsDetailObj", "default_is_expression": true, "auto_adapt_places": false, "remove_mask_roles": {}, "is_unique": false, "description": "", "type": "currency", "decimal_places": 2, "default_to_zero": true, "is_required": false, "define_type": "package", "is_single": false, "max_length": 14, "is_index": true, "is_active": true, "is_encrypted": false, "length": 12, "default_value": "$budget_account_id__r.available_amount$", "label": "可用金额", "currency_unit": "￥", "api_name": "available_amount", "is_show_mask": false, "round_mode": 4, "status": "new", "help_text": ""}, "budget_department": {"describe_api_name": "TPMBudgetDisassemblyExistsDetailObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "quote_field_type": "department", "is_unique": false, "label": "预算部门", "type": "quote", "quote_field": "budget_account_id__r.budget_department", "is_required": false, "api_name": "budget_department", "define_type": "package", "is_single": false, "status": "new", "help_text": ""}, "lock_user": {"describe_api_name": "TPMBudgetDisassemblyExistsDetailObj", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "加锁人", "label": "加锁人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "lock_user", "define_type": "package", "label_r": "加锁人", "is_single": true, "status": "new"}, "extend_obj_data_id": {"describe_api_name": "TPMBudgetDisassemblyExistsDetailObj", "is_index": true, "is_required": false, "api_name": "extend_obj_data_id", "is_unique": true, "description": "连接通表的记录ID,扩展字段用", "define_type": "system", "label": "扩展字段在mt_data中的记录ID", "type": "text", "status": "released", "max_length": 64}, "is_deleted": {"describe_api_name": "TPMBudgetDisassemblyExistsDetailObj", "is_index": false, "description": "is_deleted", "is_unique": false, "default_value": "false", "label": "is_deleted", "type": "true_or_false", "is_need_convert": false, "is_required": false, "api_name": "is_deleted", "define_type": "system", "status": "released"}, "life_status_before_invalid": {"describe_api_name": "TPMBudgetDisassemblyExistsDetailObj", "is_index": false, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "description": "作废前生命状态", "label": "作废前生命状态", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "life_status_before_invalid", "define_type": "package", "label_r": "作废前生命状态", "is_single": false, "status": "new", "max_length": 256}, "object_describe_api_name": {"describe_api_name": "TPMBudgetDisassemblyExistsDetailObj", "is_index": false, "is_active": true, "pattern": "", "description": "object_describe_api_name", "is_unique": false, "label": "object_describe_api_name", "type": "text", "is_need_convert": false, "is_required": true, "api_name": "object_describe_api_name", "define_type": "system", "status": "released", "max_length": 200}, "amount_after_operation": {"describe_api_name": "TPMBudgetDisassemblyExistsDetailObj", "return_type": "number", "expression_type": "js", "is_index": true, "expression": "$available_amount$+$amount$", "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "label": "操作后可用金额", "type": "formula", "decimal_places": 2, "default_to_zero": true, "is_required": false, "api_name": "amount_after_operation", "define_type": "package", "is_single": false, "status": "new", "help_text": ""}, "out_owner": {"describe_api_name": "TPMBudgetDisassemblyExistsDetailObj", "is_index": false, "is_active": true, "is_unique": false, "label": "外部负责人", "type": "employee", "is_need_convert": false, "is_required": false, "api_name": "out_owner", "define_type": "system", "is_single": true, "status": "released"}, "owner_department": {"describe_api_name": "TPMBudgetDisassemblyExistsDetailObj", "default_is_expression": false, "auto_adapt_places": false, "pattern": "", "is_unique": false, "type": "text", "default_to_zero": false, "is_required": false, "define_type": "package", "input_mode": "", "label_r": "负责人主属部门", "is_single": true, "max_length": 100, "is_index": true, "is_active": true, "is_encrypted": false, "default_value": "", "label": "负责人主属部门", "is_need_convert": false, "api_name": "owner_department", "status": "new", "help_text": ""}, "owner": {"describe_api_name": "TPMBudgetDisassemblyExistsDetailObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "where_type": "field", "label": "负责人", "type": "employee", "is_need_convert": false, "wheres": [], "is_required": true, "api_name": "owner", "define_type": "package", "label_r": "负责人", "is_single": true, "status": "new", "help_text": ""}, "amount": {"describe_api_name": "TPMBudgetDisassemblyExistsDetailObj", "default_is_expression": false, "auto_adapt_places": false, "remove_mask_roles": {}, "is_unique": false, "type": "currency", "decimal_places": 2, "default_to_zero": true, "is_required": true, "define_type": "package", "is_single": false, "max_length": 14, "is_index": true, "is_active": true, "is_encrypted": false, "length": 12, "default_value": "", "label": "操作金额", "currency_unit": "￥", "api_name": "amount", "is_show_mask": false, "round_mode": 4, "status": "new", "help_text": ""}, "last_modified_time": {"describe_api_name": "TPMBudgetDisassemblyExistsDetailObj", "is_index": true, "description": "last_modified_time", "is_unique": false, "label": "最后修改时间", "time_zone": "", "type": "date_time", "is_need_convert": false, "is_required": false, "api_name": "last_modified_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "status": "released"}, "package": {"describe_api_name": "TPMBudgetDisassemblyExistsDetailObj", "is_index": false, "is_active": true, "pattern": "", "description": "package", "is_unique": false, "label": "package", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "package", "define_type": "system", "status": "released", "max_length": 200}, "lock_status": {"describe_api_name": "TPMBudgetDisassemblyExistsDetailObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "锁定状态", "default_value": "0", "label": "锁定状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "lock_status", "options": [{"label": "未锁定", "value": "0"}, {"label": "锁定", "value": "1"}], "define_type": "package", "label_r": "锁定状态", "is_single": false, "status": "new"}, "budget_disassembly_id": {"describe_api_name": "TPMBudgetDisassemblyExistsDetailObj", "auto_adapt_places": false, "is_unique": false, "type": "master_detail", "is_required": true, "define_type": "package", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "target_api_name": "TPMBudgetDisassemblyObj", "label": "预算拆解", "show_detail_button": false, "target_related_list_name": "target_related_list_TPMBudgetDisassemblyExistsDetailObj_TPMBudgetDisassemblyObj__c", "target_related_list_label": "预算拆解明细-已有预算表", "api_name": "budget_disassembly_id", "is_create_when_master_create": true, "is_required_when_master_create": false, "status": "new", "help_text": ""}, "create_time": {"describe_api_name": "TPMBudgetDisassemblyExistsDetailObj", "is_index": true, "description": "create_time", "is_unique": false, "label": "创建时间", "time_zone": "", "type": "date_time", "is_need_convert": false, "is_required": false, "api_name": "create_time", "define_type": "system", "date_format": "yyyy-MM-dd HH:mm:ss", "status": "released"}, "life_status": {"describe_api_name": "TPMBudgetDisassemblyExistsDetailObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "default_value": "normal", "label": "生命状态", "type": "select_one", "is_need_convert": false, "is_required": false, "api_name": "life_status", "options": [{"label": "未生效", "value": "ineffective"}, {"label": "审核中", "value": "under_review"}, {"label": "正常", "value": "normal"}, {"label": "变更中", "value": "in_change"}, {"label": "作废", "value": "invalid"}], "define_type": "package", "label_r": "生命状态", "is_single": false, "status": "new", "help_text": ""}, "budget_account_id": {"describe_api_name": "TPMBudgetDisassemblyExistsDetailObj", "default_is_expression": false, "auto_adapt_places": false, "is_unique": false, "description": "", "where_type": "field", "type": "object_reference", "relation_outer_data_privilege": "not_related", "related_where_type": "", "wheres": [], "is_required": true, "define_type": "package", "input_mode": "", "is_single": false, "is_index": true, "is_active": true, "is_encrypted": false, "target_api_name": "TPMBudgetAccountObj", "label": "预算表名称", "target_related_list_name": "target_related_list_TPMBudgetDisassemblyExistsDetailObj_TPMBudgetAccountObj__c", "target_related_list_label": "预算拆解明细-已有预算表", "action_on_target_delete": "set_null", "related_wheres": [], "api_name": "budget_account_id", "status": "new", "help_text": ""}, "budget_period": {"describe_api_name": "TPMBudgetDisassemblyExistsDetailObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "quote_field_type": "select_one", "is_unique": false, "label": "编制期间", "type": "quote", "quote_field": "budget_account_id__r.effective_period", "is_required": false, "api_name": "budget_period", "define_type": "package", "is_single": false, "status": "new", "help_text": ""}, "last_modified_by": {"describe_api_name": "TPMBudgetDisassemblyExistsDetailObj", "is_index": true, "is_active": true, "is_unique": false, "label": "最后修改人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "last_modified_by", "define_type": "system", "is_single": true, "status": "released"}, "out_tenant_id": {"describe_api_name": "TPMBudgetDisassemblyExistsDetailObj", "is_index": false, "is_active": true, "pattern": "", "description": "out_tenant_id", "is_unique": false, "label": "外部企业", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "out_tenant_id", "define_type": "system", "status": "released", "max_length": 200}, "created_by": {"describe_api_name": "TPMBudgetDisassemblyExistsDetailObj", "is_index": true, "is_active": true, "is_unique": false, "label": "创建人", "type": "employee", "is_need_convert": true, "is_required": false, "api_name": "created_by", "define_type": "system", "is_single": true, "status": "released"}, "version": {"describe_api_name": "TPMBudgetDisassemblyExistsDetailObj", "is_index": false, "length": 8, "description": "version", "is_unique": false, "label": "version", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "version", "define_type": "system", "round_mode": 4, "status": "released"}, "record_type": {"describe_api_name": "TPMBudgetDisassemblyExistsDetailObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "description": "record_type", "label": "业务类型", "type": "record_type", "is_need_convert": false, "is_required": false, "api_name": "record_type", "options": [{"is_active": true, "api_name": "default__c", "description": "预设业务类型", "label": "预设业务类型"}], "define_type": "package", "label_r": "业务类型", "is_single": false, "status": "released", "help_text": ""}, "relevant_team": {"describe_api_name": "TPMBudgetDisassemblyExistsDetailObj", "embedded_fields": {"teamMemberEmployee": {"is_index": true, "is_need_convert": true, "is_required": false, "api_name": "teamMemberEmployee", "description": "成员员工", "define_type": "package", "is_unique": false, "label": "成员员工", "is_single": true, "type": "employee", "help_text": "成员员工"}, "teamMemberRole": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberRole", "options": [{"label": "负责人", "value": "1"}, {"label": "普通成员", "value": "4"}], "description": "成员角色", "define_type": "package", "is_unique": false, "label": "成员角色", "type": "select_one", "help_text": "成员角色"}, "teamMemberPermissionType": {"is_index": true, "is_need_convert": false, "is_required": false, "api_name": "teamMemberPermissionType", "options": [{"label": "只读", "value": "1"}, {"label": "读写", "value": "2"}], "description": "成员权限类型", "define_type": "package", "is_unique": false, "label": "成员权限类型", "type": "select_one", "help_text": "成员权限类型"}}, "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "label": "相关团队", "type": "embedded_object_list", "is_need_convert": false, "is_required": false, "api_name": "relevant_team", "define_type": "package", "label_r": "相关团队", "is_single": false, "status": "new", "help_text": "相关团队"}, "data_own_department": {"describe_api_name": "TPMBudgetDisassemblyExistsDetailObj", "is_index": true, "is_active": true, "is_encrypted": false, "auto_adapt_places": false, "is_unique": false, "where_type": "field", "label": "归属部门", "type": "department", "is_need_convert": false, "wheres": [], "is_required": false, "api_name": "data_own_department", "define_type": "package", "label_r": "归属部门", "is_single": true, "status": "new", "help_text": ""}, "name": {"describe_api_name": "TPMBudgetDisassemblyExistsDetailObj", "default_is_expression": false, "prefix": "BDED-{yyyy}-{mm}-{dd}-", "auto_adapt_places": false, "pattern": "", "is_unique": true, "description": "name", "start_number": 1, "type": "auto_number", "default_to_zero": false, "is_required": true, "define_type": "system", "input_mode": "", "postfix": "", "label_r": "预算拆解明细编号", "is_single": false, "max_length": 100, "is_index": true, "auto_number_type": "normal", "is_active": true, "is_encrypted": false, "default_value": "BDED-{yyyy}-{mm}-{dd}-000001", "serial_number": 6, "label": "预算拆解明细编号", "condition": "NONE", "api_name": "name", "func_api_name": "", "status": "new", "help_text": ""}, "order_by": {"describe_api_name": "TPMBudgetDisassemblyExistsDetailObj", "is_index": false, "length": 8, "description": "order_by", "is_unique": false, "label": "order_by", "type": "number", "decimal_places": 0, "is_need_convert": false, "is_required": false, "api_name": "order_by", "define_type": "system", "round_mode": 4, "status": "released"}, "_id": {"describe_api_name": "TPMBudgetDisassemblyExistsDetailObj", "is_index": false, "is_active": true, "pattern": "", "description": "_id", "is_unique": false, "label": "_id", "type": "text", "is_need_convert": false, "is_required": false, "api_name": "_id", "define_type": "system", "status": "released", "max_length": 200}}, "release_version": "6.4"}