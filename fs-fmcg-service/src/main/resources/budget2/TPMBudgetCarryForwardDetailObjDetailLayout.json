{"buttons": [], "components": [{"field_section": [], "buttons": [{"action_type": "default", "api_name": "Edit_button_default", "label": "编辑"}, {"action_type": "default", "api_name": "SaleRecord_button_default", "label": "销售记录"}, {"action_type": "default", "api_name": "Dial_button_default", "label": "打电话"}, {"action_type": "default", "api_name": "ChangeOwner_button_default", "label": "更换负责人"}, {"action_type": "default", "api_name": "StartBPM_button_default", "label": "发起业务流程"}, {"action_type": "default", "api_name": "Abolish_button_default", "label": "作废"}, {"action_type": "default", "api_name": "StartStagePropellor_button_default", "label": "发起阶段推进器"}, {"action_type": "default", "api_name": "Lock_button_default", "label": "锁定"}, {"action_type": "default", "api_name": "Unlock_button_default", "label": "解锁"}, {"action_type": "default", "api_name": "Clone_button_default", "label": "复制"}, {"action_type": "default", "api_name": "ChangePartnerOwner_button_default", "label": "更换外部负责人"}, {"action_type": "default", "api_name": "SendMail_button_default"}, {"action_type": "default", "api_name": "Discuss_button_default"}, {"action_type": "default", "api_name": "Remind_button_default"}, {"action_type": "default", "api_name": "Schedule_button_default"}, {"action_type": "default", "api_name": "Print_button_default"}], "api_name": "head_info", "related_list_name": "", "header": "标题和按钮", "nameI18nKey": "paas.udobj.head_info", "exposedButton": 3, "type": "simple", "_id": "head_info"}, {"field_section": [{"render_type": "employee", "field_name": "owner"}, {"render_type": "text", "field_name": "owner_department"}, {"render_type": "date_time", "field_name": "last_modified_time"}, {"render_type": "record_type", "field_name": "record_type"}], "buttons": [], "api_name": "top_info", "related_list_name": "", "header": "摘要信息", "type": "top_info", "nameI18nKey": "paas.udobj.summary_info", "_id": "top_info"}, {"field_section": [], "buttons": [], "api_name": "relevant_team_component", "related_list_name": "", "header": "相关团队", "nameI18nKey": "paas.udobj.constant.relevant_team", "type": "user_list", "_id": "relevant_team_component"}, {"field_section": [{"show_header": true, "form_fields": [{"is_readonly": false, "is_required": true, "render_type": "object_reference", "field_name": "source_budget_account_id"}, {"is_readonly": true, "is_required": false, "render_type": "currency", "field_name": "amount"}, {"is_readonly": true, "is_required": false, "render_type": "object_reference", "field_name": "target_budget_account_id"}, {"is_readonly": true, "is_required": false, "render_type": "currency", "field_name": "after_carry_forward_amount"}, {"is_readonly": true, "is_required": false, "is_tiled": false, "render_type": "select_one", "field_name": "carry_forward_status"}, {"is_readonly": true, "is_required": false, "render_type": "long_text", "field_name": "carry_forward_failure_message"}, {"is_readonly": true, "is_required": false, "render_type": "auto_number", "field_name": "name"}, {"is_readonly": true, "is_required": true, "render_type": "master_detail", "field_name": "budget_carry_forward_id"}], "api_name": "base_field_section__c", "tab_index": "ltr", "column": 2, "header": "基本信息"}, {"show_header": true, "form_fields": [{"is_readonly": false, "is_required": false, "render_type": "employee", "field_name": "owner"}, {"is_readonly": false, "is_required": false, "render_type": "text", "field_name": "owner_department"}, {"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "created_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "create_time"}, {"is_readonly": true, "is_required": false, "render_type": "employee", "field_name": "last_modified_by"}, {"is_readonly": true, "is_required": false, "render_type": "date_time", "field_name": "last_modified_time"}, {"is_readonly": false, "is_required": true, "is_tiled": false, "render_type": "select_one", "field_name": "life_status"}, {"is_readonly": false, "is_required": true, "render_type": "record_type", "field_name": "record_type"}, {"is_readonly": false, "is_required": false, "render_type": "department", "field_name": "data_own_department"}], "api_name": "group_1QNy5__c", "tab_index": "ltr", "column": 2, "header": "系统信息"}], "buttons": [], "api_name": "form_component", "related_list_name": "", "column": 2, "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info", "type": "form", "_id": "form_component"}, {"field_section": [], "buttons": [], "api_name": "operation_log", "related_list_name": "", "header": "修改记录", "nameI18nKey": "paas.udobj.modify_log", "type": "related_record", "_id": "operation_log"}, {"components": [["form_component"], ["operation_log"], ["BPM_related_list"], ["Approval_related_list"], ["payment_recordrelated_list_generate_by_UDObjectServer__c"]], "buttons": [], "api_name": "tabs_2zy91__c", "tabs": [{"api_name": "form_component_Z044E__c", "header": "详细信息", "nameI18nKey": "paas.udobj.detail_info"}, {"api_name": "operation_log_Lnvbi__c", "header": "修改记录", "nameI18nKey": "paas.udobj.modify_log"}, {"api_name": "BPM_related_list_EL32n__c", "header": "流程列表", "nameI18nKey": "paas.udobj.process_list"}, {"api_name": "Approval_related_list_iq698__c", "header": "审批流程", "nameI18nKey": "paas.udobj.approvalflow"}, {"api_name": "tab_payment_recordrelated_list_generate_by_UDObjectServer__c", "header": "收款记录", "nameI18nKey": "paas.udobj.receipt"}], "header": "页签容器", "type": "tabs", "_id": "tabs_2zy91__c"}, {"type": "relatedlist", "buttons": [], "define_type": "business", "relationType": 2, "api_name": "payment_recordrelated_list_generate_by_UDObjectServer__c", "header": "收款记录", "nameI18nKey": "paas.udobj.receipt", "ref_object_api_name": "payment_record", "related_list_name": "payment_record_LIST", "limit": 1, "_id": "payment_recordrelated_list_generate_by_UDObjectServer__c"}, {"type": "relatedlist", "buttons": [], "define_type": "general", "api_name": "BPM_related_list", "header": "流程列表", "nameI18nKey": "paas.udobj.process_list", "ref_object_api_name": "BPM", "related_list_name": "", "limit": 1, "field_section": [], "_id": "BPM_related_list"}, {"type": "relatedlist", "buttons": [], "define_type": "general", "api_name": "Approval_related_list", "header": "审批流程", "nameI18nKey": "paas.udobj.approvalflow", "ref_object_api_name": "Approval", "related_list_name": "", "limit": 1, "field_section": [], "_id": "Approval_related_list"}], "is_deleted": false, "version": 0, "agent_type": null, "is_show_fieldname": null, "layout_description": "", "api_name": "layout_TPMBudgetCarryForwardDetailObj__c", "what_api_name": null, "default_component": "form_component", "layout_structure": {"layout": [{"components": [["head_info"]], "columns": [{"width": "100%"}]}, {"components": [["top_info", "tabs_2zy91__c"], ["relevant_team_component"]], "columns": [{"width": "auto"}, {"width": "500px", "retractable": true}]}]}, "display_name": "默认布局", "is_default": true, "layout_type": "detail", "package": "CRM", "ref_object_api_name": "TPMBudgetCarryForwardDetailObj", "ui_event_ids": [], "hidden_buttons": [], "hidden_components": ["sale_log"], "namespace": null, "enable_mobile_layout": false, "events": []}