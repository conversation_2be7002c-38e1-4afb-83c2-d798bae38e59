package com.facishare.fmcg.adapter.license;

import com.facishare.fmcg.adapter.license.dto.ValidateLicense;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface LicenseAdapter {

    /**
     * validate tenant package
     *
     * @param tenantId
     * @param version
     * @return
     */
    boolean validate(int tenantId, String version);

    ValidateLicense.LicenseInfo get(int tenantId, String version);

    /**
     * 是否包含其中一个模块
     * @param tenantId
     * @param moduleCodes
     * @return
     */
    boolean validate(int tenantId, List<String> moduleCodes);
}
