package com.facishare.fmcg.adapter.license;

import com.facishare.fmcg.adapter.common.HttpUtil;
import com.facishare.fmcg.adapter.exception.AdapterException;
import com.facishare.fmcg.adapter.license.dto.CacheLicenseMqObj;
import com.facishare.fmcg.adapter.license.dto.ValidateLicense.LicenseContext;

import com.facishare.fmcg.adapter.license.dto.ValidateLicense;
import com.facishare.fmcg.adapter.redisCache.TPMLocalCache;
import com.facishare.paas.license.Result.LicenseVersionResult;
import com.facishare.paas.license.Result.ModuleInfoResult;
import com.facishare.paas.license.arg.QueryModuleArg;
import com.facishare.paas.license.arg.QueryProductArg;
import com.facishare.paas.license.common.Result;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.license.pojo.ModuleInfoPojo;
import com.facishare.paas.license.pojo.ProductVersionPojo;
import com.fxiaoke.common.release.GrayRelease;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.common.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@SuppressWarnings("Duplicates")
public class LicenseAdapterImpl implements LicenseAdapter {

    private static final Logger logger = LoggerFactory.getLogger(LicenseAdapterImpl.class);

    @Resource
    private TPMLocalCache tpmLocalCache;

    @Resource
    private LicenseClient licenseClient;

    @Override
    public boolean validate(int tenantId, String version) {

        Set<String> versionAndModuleCodes = getVersionAndModuleCodes(tenantId);
        if (versionAndModuleCodes.stream().anyMatch(v -> v.equals(version))) {
            logger.info("license version {} is in module", version);
            return true;
        }

        if (existCache(tenantId, version)) {
            logger.info("license version {} is in cache", version);
            return true;
        }

        if (GrayRelease.isAllow("fmcg", "SKIP_VALIDATE_LICENSE_CODE", tenantId)) {
            logger.info("skip validate license code for tenant {}", tenantId);
            return true;
        }
        return false;
    }

    private boolean existCache(int tenantId, String version) {
        if (Strings.isEmpty(version)) {
            return false;
        }
        String key = tpmLocalCache.getKey(String.valueOf(tenantId), TPMLocalCache.KEY_TPM_LICENSE);
        if (tpmLocalCache.exists(key)) {
            CacheLicenseMqObj licenseInfo = tpmLocalCache.findCacheToObj(key, CacheLicenseMqObj.class);
            if (Objects.isNull(licenseInfo)) {
                return false;
            }
            if (version.startsWith("user_license") || version.endsWith("_limit")) {
                return licenseInfo.getMqModuleParas().stream().anyMatch(v -> version.equals(v.getParaKey()));
            } else {
                return licenseInfo.getModuleCodes().contains(version);
            }
        } else {
            return false;
        }
    }

    @Override
    public ValidateLicense.LicenseInfo get(int tenantId, String version) {
        ValidateLicense.Arg arg = new ValidateLicense.Arg();

        LicenseContext context = new LicenseContext();
        context.setTenantId(String.valueOf(tenantId));
        context.setAppId("CRM");
        context.setUserId("-10000");
        arg.setLicenseContext(context);

        try {
            ValidateLicense.Result result = HttpUtil.post(
                    HttpUtil.initUrl("adapter.license.validate"),
                    HttpUtil.initMetadataHeader(tenantId),
                    arg,
                    ValidateLicense.Result.class);

            if (result.getErrCode().equals(0)) {
                return result.getResult().stream().filter(f -> f.getCurrentVersion().equals(version)).findFirst().orElse(null);
            } else {
                throw new AdapterException("license", result.getErrMessage(), result.getErrCode());
            }
        } catch (IOException ex) {
            throw new AdapterException("crm", "http remote call error.", 100500);
        }
    }

    @Override
    public boolean validate(int tenantId, List<String> moduleCodes) {
        if (CollectionUtils.isEmpty(moduleCodes)) {
            return false;
        }
        Set<String> versionAndModuleCodes = getVersionAndModuleCodes(tenantId);
        return moduleCodes.stream().anyMatch(versionAndModuleCodes::contains);
    }

    private Set<String> getVersionList(int tenantId) {
        List<ProductVersionPojo> versionPojoList;
        com.facishare.paas.license.common.LicenseContext context = buildLicenseContext(tenantId);
        QueryProductArg arg = new QueryProductArg();
        arg.setLicenseContext(context);
        LicenseVersionResult licenseVersionResult = licenseClient.queryProductVersion(arg);
        if (!Objects.isNull(licenseVersionResult) && isSuccess(licenseVersionResult) && CollectionUtils.isNotEmpty(licenseVersionResult.getResult())) {
            versionPojoList = licenseVersionResult.getResult();
        } else {
            versionPojoList = Lists.newArrayList();
        }
        return versionPojoList.stream().map(ProductVersionPojo::getCurrentVersion).collect(Collectors.toSet());
    }

    private Set<String> getModuleList(int tenantId) {
        com.facishare.paas.license.common.LicenseContext context = buildLicenseContext(tenantId);
        QueryModuleArg arg = new QueryModuleArg();
        arg.setLicenseContext(context);
        ModuleInfoResult moduleInfoResult = licenseClient.queryModule(arg);
        if (!Objects.isNull(moduleInfoResult) && isSuccess(moduleInfoResult) && CollectionUtils.isNotEmpty(moduleInfoResult.getResult())) {
            return moduleInfoResult.getResult().stream().map(ModuleInfoPojo::getModuleCode).collect(Collectors.toSet());
        } else {
            return Sets.newHashSet();
        }
    }

    private com.facishare.paas.license.common.LicenseContext buildLicenseContext(long tenantId) {
        com.facishare.paas.license.common.LicenseContext context = new com.facishare.paas.license.common.LicenseContext();
        context.setTenantId(String.valueOf(tenantId));
        context.setUserId("-10000");
        context.setAppId("CRM");
        return context;
    }

    private boolean isSuccess(Result result) {
        return Objects.equals(result.getErrCode(), 0);
    }

    /**
     * 获取版本和模块
     *
     * @param eId
     * @return
     */
    private Set<String> getVersionAndModuleCodes(int eId) {
        Set<String> moduleList = getModuleList(eId);
        Set<String> versionList = getVersionList(eId);
        //合并
        Set<String> versionAndModuleCodes = new HashSet<>();
        if (CollectionUtils.isNotEmpty(moduleList)) {

            versionAndModuleCodes.addAll(moduleList);
        }
        if (CollectionUtils.isNotEmpty(versionList)) {
            versionAndModuleCodes.addAll(versionList);
        }
        return versionAndModuleCodes;
    }
}
