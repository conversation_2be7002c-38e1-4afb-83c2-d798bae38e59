# fs-fmcg 项目

## 项目概述

fs-fmcg是一个快消品行业的AI模型管理系统，主要用于管理和配置AI模型，包括模型的添加、更新、查询、状态切换等功能，以及对象映射和AI检测规则的管理。

## 项目架构

项目采用多模块架构设计，主要包含以下模块：

- **fs-fmcg-api**: 定义接口、DTO和错误码
- **fs-fmcg-adapter**: 适配器层，负责与外部系统交互
- **fs-fmcg-provider**: 服务提供者，实现API接口
- **fs-fmcg-service**: 业务服务层，包含核心业务逻辑

## 主要功能

### AI模型管理

- 获取AI模型场景列表
- 根据场景获取AI模型列表
- 根据ID获取模型详情
- 添加/更新模型
- 切换模型状态
- 获取模型描述信息

### 对象映射管理

- 查询对象映射列表
- 批量保存或更新对象映射
- 批量删除对象映射

### AI检测规则管理

- 查询规则列表
- 保存或更新检测规则
- 删除检测规则
- 批量查询AI规则
- 根据ID查询单个AI规则

### AI检测服务

- 门头识别：支持批量图片的门头检测，识别门头文字、位置、属性等信息

## 接口文档

### 根据ID查询单个AI规则 (getAIRuleById)

#### 接口描述
根据规则ID查询单个AI检测规则的详细信息。

#### 请求URL
```
POST /ai/model/getAIRuleById
```

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ----- | ---- | ---- | ---- |
| ruleId | String | 是 | AI规则ID |

#### 请求示例
```json
{
  "data": {
    "ruleId": "rule_12345"
  },
  "tenantId": 10000,
  "userId": 10001
}
```

#### 返回参数
| 参数名 | 类型 | 描述 |
| ----- | ---- | ---- |
| rule | AIDetectRuleDTO | AI规则详情 |

#### AIDetectRuleDTO 结构
| 参数名 | 类型 | 描述 |
| ----- | ---- | ---- |
| id | String | 规则ID |
| tenantId | Integer | 租户ID |
| modelId | String | 模型ID |
| name | String | 规则名称 |
| ruleDescribe | String | 规则描述 |
| masterDescribeApiName | String | 主描述API名称 |
| detectCapabilityMap | Map<String, Integer> | 业务能力映射，包括商品识别、价格识别等功能的开关 |
| fieldMap | Map<String, FieldDTO> | 字段映射，定义AI识别结果与业务字段的对应关系 |
| creator | UserInfo | 创建者信息 |
| lastModifier | UserInfo | 最后修改者信息 |
| createTime | long | 创建时间戳 |
| lastModifyTime | long | 最后修改时间戳 |

#### FieldDTO 结构
| 参数名 | 类型 | 描述 |
| ----- | ---- | ---- |
| type | String | 类型，目前仅有mapping |
| fieldKey | String | 字段apiName |
| calculateType | Integer | 计算类型：0-求和，1-最大值 |
| objectApiName | String | 对象apiName |
| filedType | String | 字段类型，如image、select_one、number等 |
| aiStoreFieldApiName | String | AI存储字段 |
| manuallyStoreFieldApiName | String | 手动存储字段 |

#### 返回示例
```json
{
  "code": 0,
  "message": "success",
  "success": true,
  "data": {
    "rule": {
      "id": "rule_12345",
      "tenantId": 10000,
      "modelId": "model_6789",
      "name": "商品陈列检测规则",
      "ruleDescribe": "用于检测商品陈列情况的规则",
      "masterDescribeApiName": "product_display",
      "detectCapabilityMap": {
        "isOpenProductRowNumber": 1,
        "isOpenGroupNumber": 1,
        "isOpenLayerNumber": 1,
        "openSkuUnit": 0,
        "isOpenPrices": 1,
        "isOpenSceneDetect": 1,
        "isOpenDisplayForm": 1,
        "isPOSMDetect": 0
      },
      "fieldMap": {
        "aiRowNumber": {
          "type": "mapping",
          "fieldKey": "row_number",
          "calculateType": 0,
          "objectApiName": "ProductDisplay",
          "filedType": "number",
          "aiStoreFieldApiName": "ai_row_number",
          "manuallyStoreFieldApiName": "manual_row_number"
        },
        "aiPath": {
          "type": "mapping",
          "fieldKey": "image_path",
          "objectApiName": "ProductDisplay",
          "filedType": "image",
          "aiStoreFieldApiName": "ai_image_path",
          "manuallyStoreFieldApiName": null
        }
      },
      "creator": {
        "userId": 10001,
        "userName": "张三"
      },
      "lastModifier": {
        "userId": 10001,
        "userName": "张三"
      },
      "createTime": 1613456789000,
      "lastModifyTime": 1613456789000
    }
  }
}
```

#### 错误码
| 错误码 | 描述 |
| ----- | ---- |
| 400 | 参数为空 |
| 404 | 规则不存在 |
| 500 | 系统错误 |

### 获取AI规则描述 (getAIRuleDescribe)

#### 接口描述
根据租户ID和模型ID获取AI规则的描述信息，包括分组、检测能力、字段映射等配置信息。

#### 请求URL
```
POST /ai/model/getAIRuleDescribe
```

#### 请求参数
| 参数名 | 类型 | 必填 | 描述 |
| ----- | ---- | ---- | ---- |
| tenantId | Integer | 是 | 租户ID |
| modelId | String | 是 | 模型ID |

#### 请求示例
```json
{
  "data": {
    "tenantId": 10000,
    "modelId": "model_6789"
  },
  "tenantId": 10000,
  "userId": 10001
}
```

#### 返回参数
| 参数名 | 类型 | 描述 |
| ----- | ---- | ---- |
| groupList | List<GroupItem> | 分组列表 |
| detectCapabilityList | List<DetectCapabilityItem> | 检测能力列表 |
| fieldsMap | Map<String, FieldMapItem> | 字段映射 |
| groupFieldRalationList | List<GroupFieldRelation> | 分组字段关系列表 |
| detectCapabilityFieldRalationMap | Map<String, List<String>> | 检测能力字段关系映射 |
| calculateTypesMap | Map<String, CalculateTypeItem> | 计算类型映射 |
| fieldTypeMap | Map<String, FieldTypeItem> | 字段类型映射 |

#### GroupItem 结构
| 参数名 | 类型 | 描述 |
| ----- | ---- | ---- |
| label | String | 分组标签 |
| value | String | 分组值 |
| i18nKey | String | 国际化键 |

#### DetectCapabilityItem 结构
| 参数名 | 类型 | 描述 |
| ----- | ---- | ---- |
| label | String | 检测能力标签 |
| value | String | 检测能力值 |
| i18nKey | String | 国际化键 |

#### FieldMapItem 结构
| 参数名 | 类型 | 描述 |
| ----- | ---- | ---- |
| type | String | 类型 |
| fieldApiname | String | 字段API名称 |
| label | String | 标签 |
| supportCalculateTypes | List<Integer> | 支持的计算类型 |
| supportFieldType | List<String> | 支持的字段类型 |
| enableManualInput | boolean | 是否启用手动输入 |
| enableCalculateType | boolean | 是否启用计算类型 |
| i18nKey | String | 国际化键 |

#### GroupFieldRelation 结构
| 参数名 | 类型 | 描述 |
| ----- | ---- | ---- |
| group | String | 分组 |
| fields | List<String> | 字段列表 |
| order | int | 排序 |

#### CalculateTypeItem 结构
| 参数名 | 类型 | 描述 |
| ----- | ---- | ---- |
| label | String | 计算类型标签 |
| value | int | 计算类型值 |
| i18nKey | String | 国际化键 |

#### FieldTypeItem 结构
| 参数名 | 类型 | 描述 |
| ----- | ---- | ---- |
| label | String | 字段类型标签 |
| value | String | 字段类型值 |
| i18nKey | String | 国际化键 |

#### 返回示例
```json
{
  "code": 0,
  "message": "success",
  "success": true,
  "data": {
    "groupList": [
      {
        "label": "商品陈列识别",
        "value": "goodsDisplay",
        "i18nKey": "fmcg.ai.rule.groupList.label.goodsDisplay"
      },
      {
        "label": "商品陈列形式识别",
        "value": "displayForm",
        "i18nKey": "fmcg.ai.rule.groupList.label.displayForm"
      },
      {
        "label": "物料识别",
        "value": "posm",
        "i18nKey": "fmcg.ai.rule.groupList.label.posm"
      }
    ],
    "detectCapabilityList": [
      {
        "label": "商品陈列识别",
        "value": "isOpenProductRowNumber",
        "i18nKey": "fmcg.ai.rule.detectCapabilityList.label.isOpenProductRowNumber"
      },
      {
        "label": "商品陈列组数",
        "value": "isOpenGroupNumber",
        "i18nKey": "fmcg.ai.rule.detectCapabilityList.label.isOpenGroupNumber"
      }
    ],
    "fieldsMap": {
      "aiPath": {
        "type": "mapping",
        "fieldApiname": "aiPath",
        "label": "AI图片",
        "supportCalculateTypes": [],
        "supportFieldType": ["image"],
        "enableManualInput": false,
        "enableCalculateType": false,
        "i18nKey": "fmcg.ai.rule.fieldsMap.label.aiPath"
      },
      "productName": {
        "type": "mapping",
        "fieldApiname": "productName",
        "label": "产品名称",
        "supportCalculateTypes": [],
        "supportFieldType": ["object_reference"],
        "enableManualInput": false,
        "enableCalculateType": false,
        "i18nKey": "fmcg.ai.rule.fieldsMap.label.productName"
      }
    },
    "groupFieldRalationList": [
      {
        "group": "goodsDisplay",
        "fields": ["aiPath", "productScene", "productName", "aiRowNumber", "aiGroupNumber", "aiUnitField", "aiPrices"],
        "order": 1
      }
    ],
    "detectCapabilityFieldRalationMap": {
      "isOpenProductRowNumber": ["aiPath", "productName", "aiRowNumber"],
      "isOpenGroupNumber": ["aiGroupNumber"]
    },
    "calculateTypesMap": {
      "0": {
        "label": "所有照片排面数求和",
        "value": 0,
        "i18nKey": "fmcg.ai.rule.calculateTypesMap.label.0"
      },
      "1": {
        "label": "所有照片排面数求最大值",
        "value": 1,
        "i18nKey": "fmcg.ai.rule.calculateTypesMap.label.1"
      }
    },
    "fieldTypeMap": {
      "text": {
        "label": "单行文本",
        "value": "text",
        "i18nKey": "fmcg.ai.rule.fieldTypeMap.label.text"
      },
      "image": {
        "label": "图片",
        "value": "image",
        "i18nKey": "fmcg.ai.rule.fieldTypeMap.label.image"
      }
    }
  }
}
```

#### 错误码
| 错误码 | 描述 |
| ----- | ---- |
| 400 | 参数为空 |
| 404 | 规则不存在 |
| 500 | 系统错误 |

## 异常处理机制

项目采用统一的异常处理机制，主要包括：

1. **错误码定义**：在`ErrorCode`枚举类中定义了所有可能的错误码，包括错误码、错误消息和国际化键。

2. **异常类型**：
   - `FmcgException`: 自定义业务异常，包含错误码和错误消息

3. **异常处理流程**：
   - 在服务实现类中捕获异常
   - 对于业务异常(`FmcgException`)，直接使用其错误码和错误消息
   - 对于其他异常，使用预定义的错误码
   - 通过`setFailure`方法返回统一格式的错误响应

4. **异常处理示例**：
   ```java
   try {
       // 业务逻辑
       return setSuccess(result);
   } catch (FmcgException fmcgException) {
       log.error("操作失败", fmcgException);
       return setFailure(fmcgException.getErrCode(), fmcgException.getErrMsg());
   } catch (Exception e) {
       log.error("操作发生未知异常", e);
       return setFailure(ErrorCode.SYSTEM_ERROR);
   }
   ```

## 国际化支持

系统支持国际化，通过`I18N`工具类实现，错误消息可以根据不同的语言环境显示不同的文本。

## 配置管理

系统使用`ConfigFactory`进行配置管理，支持动态配置更新。

## 日志记录

系统使用SLF4J进行日志记录，在关键操作和异常处理时记录详细日志，便于问题排查。

### 门头识别接口 (storeFrontDetect)

#### 接口描述
对图片进行门头识别，支持批量处理，返回识别到的门头信息包括文字、位置、属性等。

#### 请求URL
```
POST /ai/detect/storefront (外部接口)
POST /ai/detect/storefront (内部接口)
```

#### 请求参数
```json
{
  "nPath": ["图片路径1", "图片路径2"],
  "modelId": "模型ID",
  "ruleId": "规则ID",
  "confidence": 0.7,
  "returnDetail": true,
  "extraParams": {
    "detectType": "comprehensive",
    "language": "zh-CN",
    "maxResults": 10
  }
}
```

#### 参数说明
- `nPath`: 图片路径列表，必填
- `modelId`: AI模型ID，必填
- `ruleId`: 检测规则ID，必填
- `confidence`: 置信度阈值，可选，默认0.5
- `returnDetail`: 是否返回详细信息，可选
- `extraParams`: 额外参数，支持动态扩展，可选

#### 响应结果
```json
{
  "data": {
    "totalImages": 2,
    "averageConfidence": 0.85,
    "extraParams": {...}
  },
  "results": [
    {
      "originalPath": "图片路径1",
      "processedPath": "处理后图片路径",
      "status": "completed",
      "confidence": 0.85,
      "storeFronts": [
        {
          "name": "门头名称",
          "type": "门头类型",
          "text": "识别文字",
          "confidence": 0.85,
          "color": "颜色",
          "size": "尺寸",
          "boundingBox": {
            "x": 100,
            "y": 50,
            "width": 300,
            "height": 80,
            "centerX": 250,
            "centerY": 90
          },
          "attributes": {
            "lighting": "良好",
            "clarity": "清晰"
          }
        }
      ],
      "additionalData": {
        "modelId": "模型ID",
        "ruleId": "规则ID"
      }
    }
  ],
  "status": "completed",
  "message": "门头识别完成",
  "processingTime": 1500
}
```

#### 响应字段说明
- `data`: 动态数据结构，包含统计信息和额外参数
- `results`: 检测结果列表，每张图片对应一个结果
- `storeFronts`: 识别到的门头信息列表
- `boundingBox`: 门头位置信息（边界框）
- `attributes`: 门头其他属性信息
- `status`: 处理状态（processing/completed/failed）
- `processingTime`: 总处理时间（毫秒）

## 改进建议

1. 完善异常处理机制，确保所有方法都有适当的异常处理
2. 增加单元测试，提高代码质量和可靠性
3. 优化日志记录，添加更多上下文信息
4. 考虑添加性能监控和指标收集
5. 完善文档，包括API文档和开发指南
6. 为所有接口添加详细的注释和接口文档，确保开发人员能够清晰理解接口的用途和参数

