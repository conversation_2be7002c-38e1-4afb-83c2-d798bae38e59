<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>fs-fmcg</groupId>
    <artifactId>fs-fmcg</artifactId>
    <packaging>pom</packaging>
    <version>1.0-SNAPSHOT</version>
    <modules>
        <module>fs-fmcg-provider</module>
        <module>fs-fmcg-service</module>
        <module>fs-fmcg-adapter</module>
        <module>fs-fmcg-api</module>
    </modules>

    <parent>
        <groupId>com.fxiaoke.common</groupId>
        <artifactId>fxiaoke-parent-pom</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <properties>
        <httpclient.version>4.5.2</httpclient.version>
        <appserver-common-tools.version>1.2-SNAPSHOT</appserver-common-tools.version>
        <poi.version>3.14</poi.version>
        <poi-ooxml.version>3.14</poi-ooxml.version>
        <protostuff-api.version>1.4.0</protostuff-api.version>
        <protostuff-core.version>1.4.0</protostuff-core.version>
        <protostuff-runtime.version>1.4.0</protostuff-runtime.version>
        <fs-open-app-center-api.version>1.0.0-SNAPSHOT</fs-open-app-center-api.version>
        <fs-open-common-result.version>0.0.5</fs-open-common-result.version>
        <commons-fileupload.version>1.3.2</commons-fileupload.version>
        <ibss-service-eps-api.version>1.0.7-SNAPSHOT</ibss-service-eps-api.version>
        <fs-common-mds-event.version>1.0.4-SNAPSHOT</fs-common-mds-event.version>
        <qixin.version>0.0.2-SNAPSHOT</qixin.version>
    </properties>


    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>4.1.63.Final</version>
            </dependency>
            <dependency>
                <groupId>com.github.colin-lee</groupId>
                <artifactId>mongo-spring-support</artifactId>
                <version>3.0.0-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <artifactId>cglib-nodep</artifactId>
                        <groupId>cglib</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>fs-circuit-breaker</artifactId>
                        <groupId>com.fxiaoke.circuit.breaker</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>