package com.facishare.fmcg.provider.dao.po;

import lombok.Data;
import lombok.ToString;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Transient;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/25 下午5:15
 */
@Data
@ToString
@Entity(value = "fmcg_test_master",noClassnameStored = true)
public class MasterPO {

    @Id
    private ObjectId id;

    private String name;


}
