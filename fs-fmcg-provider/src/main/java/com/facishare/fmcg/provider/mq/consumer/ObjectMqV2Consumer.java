package com.facishare.fmcg.provider.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.ai.detector.api.exception.AiProviderException;
import com.facishare.common.rocketmq.AutoConfRocketMQProcessor;
import com.facishare.fmcg.api.error.FmcgException;
import com.facishare.fmcg.api.service.rebate.IRebateService;
import com.facishare.fmcg.provider.co.service.CustomerOpeningService;
import com.facishare.fmcg.provider.dao.abstraction.LicenseDAO;
import com.facishare.fmcg.provider.license.AppCodeEnum;
import com.facishare.fmcg.provider.mq.model.TriggerMessageObj;
import com.facishare.fmcg.provider.util.QueryUtil;
import com.fmcg.framework.http.PaasDataProxy;
import com.fmcg.framework.http.PaasDescribeProxy;
import com.fmcg.framework.http.TPMProxy;
import com.fmcg.framework.http.contract.abstraction.PaasRestResult;
import com.fmcg.framework.http.contract.fund.AddIncomeAuthorizationDetails;
import com.fmcg.framework.http.contract.paas.data.PaasDataGet;
import com.fmcg.framework.http.contract.paas.data.PaasDataQueryWithFields;
import com.fmcg.framework.http.contract.paas.describe.PaasDescribeGetOriginalDescribe;
import com.fmcg.framework.http.contract.tpm.ResetEnableCacheByAccount;
import com.fmcg.framework.http.contract.tpm.SelfDefineReward;
import com.fmcg.framework.http.contract.tpm.StockCheckReward;
import com.fxiaoke.common.release.GrayRelease;
import com.github.trace.TraceContext;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.elasticsearch.common.Strings;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@SuppressWarnings("Duplicates")
@Component
public class ObjectMqV2Consumer {

    private static final Logger logger = LoggerFactory.getLogger(ObjectMqV2Consumer.class);
    private static final String CONFIG_NAME = "fs-fmcg-object-mq";
    private static final String NAME_SERVER_KEY = "OBJECT_V2_NAMESERVER";
    private static final String TOPIC_KEY = "OBJECT_V2_TOPICS";
    private static final String GROUP_KEY = "OBJECT_V2_GROUP_CONSUMER";
    private static final String FUND_ACCOUNT_AUTHORIZE_KEY = "fmcg_tpm_dealer_cost_fund_account_%s";
    private AutoConfRocketMQProcessor processor;
    @Resource
    private CustomerOpeningService customerOpeningService;

    @Resource
    private TPMProxy tpmProxy;
    @Resource
    private PaasDataProxy paasDataProxy;

    @Resource
    private LicenseDAO licenseDAO;
    @Resource
    private RedissonClient redissonCmd;
    @Resource
    private IRebateService rebateService;

    @Resource
    private PaasDescribeProxy paasDescribeProxy;

    private final static Cache<String, String> UPPER_TENANT_CACHE = CacheBuilder.newBuilder().expireAfterWrite(30, TimeUnit.MILLISECONDS).maximumSize(5000).build();

    private static final Set<String> FIELD_SYSTEM_FIELDS = Sets.newHashSet("_id", "config", "create_time", "index_name", "lock_rule", "field_num", "is_index_field", "version", "last_modified_by", "data_auth_code", "last_modified_time", "out_data_own_organization", "out_data_own_department", "out_data_auth_code", "sys_modified_time");

    private static Cache<Integer, Boolean> tpm2Cache = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(20, TimeUnit.MINUTES)
            .build();

    private static final Cache<Integer, Set<String>> CONDITION_CACHE = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(20, TimeUnit.MINUTES)
            .build();

    @PostConstruct
    public void init() {
        logger.info("ObjectMqV2Consumer start init.");
        MessageListenerConcurrently listener = (messages, context) -> {
            for (MessageExt messageExt : messages) {
                try {
                    processMessage(messageExt);
                } catch (FmcgException e) {
                    logger.info("业务异常：", e);
                    return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                } catch (Exception ex) {
                    logger.error("[ObjectMqV2Consumer consumer error :" + messages + "]", ex);
                    return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        };
        //112不生效
        try {
            processor = new AutoConfRocketMQProcessor(CONFIG_NAME, NAME_SERVER_KEY, GROUP_KEY, TOPIC_KEY, listener);
            processor.init();
        } catch (Exception e) {
            logger.error("init object mq consumer failed.", e);
        }

    }

    private void processMessage(MessageExt messageExt) throws IOException, AiProviderException {
        try {
            if (messageExt.getReconsumeTimes() > 8)
                return;
            String dataJsonStr = new String(messageExt.getBody());
            TriggerMessageObj msg = JSON.parseObject(dataJsonStr, TriggerMessageObj.class);
            TraceContext.get().setTraceId("objectMq:" + UUID.randomUUID().toString());
            dealTPMStoreCheck(msg);
            dealSelfDefineReward(msg, messageExt.getMsgId());
            dealPayOrder(msg);
            dealBDM(messageExt, msg);
            dealGF(msg);
            dealResetEnableCache(msg);
            dealRebateAccount(msg);
        }catch (FmcgException e){
            throw e;
        } catch (Exception ex) {
            logger.error("[notice consumer error]", ex);
            throw ex;
        }
    }

    private void dealPayOrder(TriggerMessageObj msg) {
        if (GrayRelease.isAllow("fmcg", "allowPayOrderMq", msg.getTenantId())) {
            for (JSONObject object : msg.getBody()) {
                if (object.getString("entityId").equals("EnterprisePayOrderObj")) {
                    if (msg.getOp().equals("i")) {
                        JSONObject data = getData(msg.getTenantId(), "EnterprisePayOrderObj", object.getString("objectId"));
                        if ("paid".equals(data.getString("order_status"))) {
                            callBack(msg.getTenantId(), data);
                        }
                    } else if (msg.getOp().equals("u") && object.containsKey("afterTriggerData")) {
                        String status = object.getJSONObject("afterTriggerData").getString("order_status");
                        if ("paid".equals(status)) {
                            JSONObject data = getData(msg.getTenantId(), "EnterprisePayOrderObj", object.getString("objectId"));
                            callBack(msg.getTenantId(), data);
                        }
                    }
                }
            }
        }
    }

    private void callBack(String tenantId, JSONObject data) {
        if ("kxtpm".equals(data.getString("resource"))) {
            String bizId = data.getString("merchant_order_no");
            PaasRestResult<String> result = tpmProxy.payOrderCallBack(Integer.parseInt(tenantId), -10000, bizId);
            if (!"success".equals(result.getData())) {
                logger.info("回调失败:arg:{},result:{}", bizId, result);
                throw new FmcgException("回调失败", 60001030);
            }
        }
    }


    private JSONObject getData(String tenantId, String apiName, String dataId) {
        PaasDataGet.Result result = paasDataProxy.get(Integer.parseInt(tenantId), -10000, apiName, dataId);
        if (result.getCode() != 0) {
            throw new FmcgException("get data error", 60001000);
        } else {
            return result.getData().getObjectData();
        }
    }

    private void dealSelfDefineReward(TriggerMessageObj msg, String businessId) {
        if (GrayRelease.isAllow("fmcg", "allowSelfDefineReward", msg.getTenantId())) {
            if (msg.getOp().equals("i")) {
                for (JSONObject object : msg.getBody()) {
                    switch (object.getString("entityId")) {
                        case "FMCGSerialNumberStatusObj":
                            logger.info("status :{}", object.getString("objectId"));
                            String upperTenantId = getSerialNumberStatusUpperTenantId(msg.getTenantId());
                            if (!upperTenantId.equals(msg.getTenantId())) {
                                return;
                            }
                            SelfDefineReward.Arg arg = new SelfDefineReward.Arg();
                            arg.setSerialNumberStatusId(object.getString("objectId"));
                            arg.setBusinessId(businessId);
                            arg.setTenantId(upperTenantId);
                            SelfDefineReward.Result result = tpmProxy.selfDefineReward(Integer.parseInt(msg.getTenantId()), -10000, arg);

                            if (result.getCode() != 0) {
                                logger.info("dealSelfDefineReward arg:{},dataTenantId:{},error:{}", arg, msg.getTenantId(), result.getMessage());
                                if ("数据已作废或已删除".equals(result.getMessage())) {
                                    return;
                                }
                                if (result.getMessage() != null && result.getMessage().contains("lock fail")) {
                                    throw new FmcgException("锁超时。", -1);
                                }
                                throw new RuntimeException("码引擎自定义激励出现问题。");
                            }
                        case "FMCGSerialNumberObj":
                            //logger.info("sn :{}",object);
                    }
                }
            }
        }
    }

    private String getSerialNumberStatusUpperTenantId(String tenantId) {
        String upperTenantId = UPPER_TENANT_CACHE.getIfPresent(tenantId);
        if (Strings.isNullOrEmpty(upperTenantId)) {
            synchronized (tenantId.intern()) {
                upperTenantId = UPPER_TENANT_CACHE.getIfPresent(tenantId);
                if (Strings.isNullOrEmpty(upperTenantId)) {
                    PaasDescribeGetOriginalDescribe.Result result = paasDescribeProxy.getOriginalDescribe(Integer.parseInt(tenantId), -10000, "FMCGSerialNumberStatusObj");
                    JSONObject describe = result.getData().getDescribe();
                    upperTenantId = describe.getString("upstream_tenant_id");
                    if (Strings.isNullOrEmpty(upperTenantId)) {
                        describe.remove("fields");
                        logger.info("get itself tenantId:{},describe:{}", tenantId, describe);
                        upperTenantId = describe.getString("tenant_id");
                    }
                }
            }
        }
        return upperTenantId;
    }

    private void dealTPMStoreCheck(TriggerMessageObj msg) {
        if (GrayRelease.isAllow("fmcg", "allowStoreCheckReward", msg.getTenantId())) {
            if (msg.getOp().equals("i")) {
                for (JSONObject object : msg.getBody()) {
                    String upperTenantId = null;
                    switch (object.getString("entityId")) {
                        case "FMCGSerialNumberStatusObj":
                            upperTenantId = getSerialNumberStatusUpperTenantId(msg.getTenantId());
                            if (!upperTenantId.equals(msg.getTenantId())) {
                                return;
                            }
                            stockReward(upperTenantId, upperTenantId, object.getString("objectId"), null);
                            break;
                        case "StoreStockCheck__c":
                            upperTenantId = getSerialNumberStatusUpperTenantId(msg.getTenantId());
                            stockReward(upperTenantId, msg.getTenantId(), null, object.getString("objectId"));
                            break;
                    }
                }
            }
        }
    }

    private void stockReward(String upperTenantId, String dataTenantId, String serialNumberStatusId, String dataId) {
        StockCheckReward.Arg arg = new StockCheckReward.Arg();
        arg.setUpperTenantId(upperTenantId);
        arg.setSerialNumberStatusId(serialNumberStatusId);
        arg.setDataTenantId(dataTenantId);
        arg.setStoreCheckId(dataId);
        StockCheckReward.Result result = tpmProxy.stockCheckReward(Integer.parseInt(dataTenantId), -10000, arg);
        if (result.getCode() != 0) {
            logger.info("dealTPMStoreCheck arg:{},error:{}", arg, result.getMessage());
            if ("数据已作废或已删除".equals(result.getMessage())) {
                return;
            }
            throw new RuntimeException("码引擎动销激励出现问题。");
        }
    }

    private void dealBDM(MessageExt messageExt, TriggerMessageObj msg) throws IOException {
        if ((msg.getOp().equals("i") || msg.getOp().equals("u")) && GrayRelease.isAllow("fmcg", "BDM", msg.getTenantId())) {
            logger.info("obj mq msg：{}", msg);
            if (messageExt.getReconsumeTimes() > 5) {
                logger.info("再次消费次数达到5.msg:{}", msg);
                return;
            }
            for (JSONObject object : msg.getBody()) {
                switch (object.getString("entityId")) {
                    case "object_qq1Dy__c":
                        if (msg.getOp().equals("i"))
                            customerOpeningService.bdmIdCardDetect(Integer.parseInt(msg.getTenantId()), object.getString("objectId"));
                        break;
                    case "object_F5aqI__c":
                        if (msg.getOp().equals("i"))
                            customerOpeningService.bdmVatInvoiceDetect(Integer.parseInt(msg.getTenantId()), object.getString("objectId"), true);
                        else if (msg.getOp().equals("u") && object.containsKey("afterTriggerData") && object.getJSONObject("afterTriggerData").containsKey("field_XbGyq__c"))
                            customerOpeningService.bdmVatInvoiceDetect(Integer.parseInt(msg.getTenantId()), object.getString("objectId"), false);
                        break;
                }
            }
        }
    }

    private void dealGF(TriggerMessageObj msg) throws IOException {
        if (GrayRelease.isAllow("fmcg", "AI.PILE_ANALYSIS", msg.getTenantId())) {
            for (JSONObject object : msg.getBody()) {
                switch (object.getString("entityId")) {
                    case "CheckinsObj":
                        logger.info("start pile detect,ei:{},objId:{}", msg.getTenantId(), object.getString("entityId"));
                        if ((object.containsKey("afterTriggerData") && object.getJSONObject("afterTriggerData").containsKey("field_t00R9__c") && msg.getOp().equals("u")) || msg.getOp().equals("i")) {
                            customerOpeningService.pileExpenseReview(Integer.parseInt(msg.getTenantId()), object.getString("objectId"));
                            break;
                        }
                }
            }
        }
        if (GrayRelease.isAllow("fmcg", "AI.TPM_PILE_ANALYSIS_AUDIT", msg.getTenantId())) {
            for (JSONObject object : msg.getBody()) {
                switch (object.getString("entityId")) {
                    case "TPMActivityProofObj":
                        logger.info("start tpm ai  detect,ei:{},objId:{}", msg.getTenantId(), object.getString("entityId"));
                        if ((msg.getOp().equals("i"))) {
                            customerOpeningService.tpmPileAudit(Integer.parseInt(msg.getTenantId()), object.getString("objectId"));
                            break;
                        }
                }
            }
        }
    }

    private void dealResetEnableCache(TriggerMessageObj msg) {
        try {
            if (!GrayRelease.isAllow("fmcg", "TPM_USE_NEW_STORE_RANGE_LOGIC", msg.getTenantId())) {
                return;
            }
            for (JSONObject object : msg.getBody()) {
                switch (object.getString("entityId")) {
                    case "AccountObj":
                        //todo:invalid enable cache
                        if (msg.getOp().equals("u") && object.containsKey("afterTriggerData")) {
                            List<String> changeList = new ArrayList<>(object.getJSONObject("afterTriggerData").keySet());
                            changeList.removeIf(FIELD_SYSTEM_FIELDS::contains);
                            if (changeList.isEmpty()) {
                                return;
                            }
                            if (!isTpm2(Integer.valueOf(msg.getTenantId()))) {
                                return;
                            }
                            Set<String> conditionSet = getConditionFieldList(Integer.valueOf(msg.getTenantId()));
                            if (changeList.stream().anyMatch(conditionSet::contains)) {
                                resetByStoreId(msg.getTenantId(), object.getString("objectId"), changeList);
                            }
                        }
                        break;
                    case "TPMActivityStoreObj":
                        if (msg.getOp().equals("i") || msg.getOp().equals("invalid")) {
                            int ei = Integer.parseInt(msg.getTenantId());
                            String dataId = object.getString("objectId");
                            JSONObject data = paasDataProxy.get(ei, -10000, "TPMActivityStoreObj", dataId).getData().getObjectData();
                            resetByStoreId(msg.getTenantId(), data.getString("store_id"), new ArrayList<>());
                        }
                        break;
                    case "TPMActivityDealerScopeObj":
                        if (msg.getOp().equals("i") || msg.getOp().equals("invalid")) {
                            int ei = Integer.parseInt(msg.getTenantId());
                            String dataId = object.getString("objectId");
                            JSONObject data = paasDataProxy.get(ei, -10000, "TPMActivityDealerScopeObj", dataId).getData().getObjectData();
                            resetByStoreId(msg.getTenantId(), data.getString("dealer_id"), new ArrayList<>());
                        }
                        break;
                }
            }
        } catch (Exception e) {
            logger.info("deal reset err.", e);
        }
    }

    private void resetByStoreId(String tenantId, String accountId, List<String> changeList) {
        if (Strings.isNullOrEmpty(accountId)) {
            logger.info("accountId is empty");
            return;
        }
        int ei = Integer.parseInt(tenantId);


        ResetEnableCacheByAccount.Arg arg = new ResetEnableCacheByAccount.Arg();
        arg.setAccountId(accountId);
        arg.setChangeType("Edit");
        arg.setChangeFields(changeList);
        ResetEnableCacheByAccount.Result result = tpmProxy.resetEnableCacheByAccount(ei, -10000, arg);
        if (result.getCode() != 0) {
            logger.info("reset err:{}", result);
        }
    }


    private boolean isTpm2(Integer ei) {
        Boolean cache = tpm2Cache.getIfPresent(ei);
        if (cache == null) {
            cache = licenseDAO.get(ei, AppCodeEnum.TPM2.code()) != null;
            tpm2Cache.put(ei, licenseDAO.get(ei, AppCodeEnum.TPM2.code()) != null);
            if (Boolean.FALSE.equals(cache)) {
                //logger.info("非2.0企业" + ei);
            }
        }
        return Boolean.TRUE.equals(cache);
    }

    private Set<String> getConditionFieldList(Integer ei) {
        Set<String> fields = CONDITION_CACHE.getIfPresent(ei);
        if (fields == null) {
            synchronized (CONDITION_CACHE) {
                fields = CONDITION_CACHE.getIfPresent(ei);
                if (fields != null) {
                    return fields;
                } else {
                    PaasDataQueryWithFields.FilterDTO filterDTO = new PaasDataQueryWithFields.FilterDTO("store_range", "LIKE", "CONDITION");
                    List<JSONObject> data = QueryUtil.queryAllDataWithFields(ei, -10000, "TPMActivityObj", Lists.newArrayList(filterDTO), Lists.newArrayList("_id", "name", "store_range"));
                    Set<String> set = new HashSet<>();
                    data.forEach(activity -> {
                        String storeRange = activity.getString("store_range");
                        JSONObject object = JSON.parseObject(storeRange);
                        if ("CONDITION".equals(object.getString("type"))) {
                            JSONArray conditions = JSON.parseArray(object.getString("value"));
                            conditions.stream().map(v -> (JSONObject) v).forEach(condition -> {
                                List<JSONObject> filters = condition.getObject("filters", new TypeReference<List<JSONObject>>() {
                                });
                                filters.forEach(filter -> set.add(filter.getString("field_name")));
                            });
                        }
                    });
                    CONDITION_CACHE.put(ei, set);
                    return set;
                }
            }
        }
        return fields;
    }

    private void dealRebateAccount(TriggerMessageObj msg) {
        Integer tenantId = Integer.valueOf(msg.getTenantId());
        for (JSONObject object : msg.getBody()) {
            switch (object.getString("entityId")) {
                case "FundAccountObj":
                    if (msg.getOp().equals("i")) {
                        String dataId = object.getString("objectId");
                        JSONObject data = paasDataProxy.get(tenantId, -10000, "FundAccountObj", dataId).getData().getObjectData();
                        String type = data.getString("type");
                        String accessModel = data.getString("access_module");
                        if ("1".equals(type) && "2".equals(accessModel)) {
                            RLock lock = redissonCmd.getLock(FUND_ACCOUNT_AUTHORIZE_KEY);
                            try {
                                if (lock.tryLock(60, 120, TimeUnit.SECONDS)) {
                                    AddIncomeAuthorizationDetails.Arg arg = new AddIncomeAuthorizationDetails.Arg();
                                    arg.setAuthorizedObjectApiName("TPMDealerActivityCostObj");

                                    AddIncomeAuthorizationDetails.NewAuthorizeAccountsArg authorizeAccountsArg = new AddIncomeAuthorizationDetails.NewAuthorizeAccountsArg();
                                    authorizeAccountsArg.setAuthorizeAccountId(dataId);
                                    authorizeAccountsArg.setDefaultEntryAccount(false);
                                    arg.setNewAuthorizeAccounts(Lists.newArrayList(authorizeAccountsArg));

                                    AddIncomeAuthorizationDetails.Result result = rebateService.addFundAccountToObj(tenantId, arg);
                                    if (result.getErrCode() != 0) {
                                        logger.error("add fund account  to TPMDealerActivityCostObj error,tenantId:{},accountId:{},error message:{}", tenantId, dataId, result.getErrMessage());
                                    }
                                }

                            } catch (Exception ex) {
                                logger.error("get lock error,tenantId:{},dataId:{},", tenantId, dataId, ex);
                            } finally {
                                if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                                    lock.unlock();
                                }
                            }

                        }
                    }
                    break;
                default:
                    break;
            }
        }
    }

    @PreDestroy
    public void shutDown() {
        processor.shutDown();
    }
}
