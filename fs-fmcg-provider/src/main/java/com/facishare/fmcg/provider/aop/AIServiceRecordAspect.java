package com.facishare.fmcg.provider.aop;

import com.alibaba.fastjson.JSON;
import com.facishare.fmcg.api.annotation.AIServiceRecord;
import com.facishare.fmcg.provider.dao.po.AIServiceRecordPO;
import com.facishare.fmcg.provider.impl.record.AIServiceRecordService;

import java.lang.reflect.Method;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

/**
 * AI服务记录AOP切面
 * 用于自动记录AI服务调用信息
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-20
 */
@Aspect
@Component
@Slf4j
public class AIServiceRecordAspect {

    @Autowired
    private AIServiceRecordService aiServiceRecordService;

    private final ExpressionParser parser = new SpelExpressionParser();
    private final LocalVariableTableParameterNameDiscoverer discoverer = new LocalVariableTableParameterNameDiscoverer();

    @Around("@annotation(aiServiceRecord)")
    public Object around(ProceedingJoinPoint point, AIServiceRecord aiServiceRecord) throws Throwable {
        long startTime = System.currentTimeMillis();
        AIServiceRecordPO record = null;
        Object result = null;

        try {
            MethodSignature signature = (MethodSignature) point.getSignature();
            Method method = signature.getMethod();
            
            // 解析业务ID（用于缓存查找和记录创建）
            String businessId = parseBusinessId(aiServiceRecord.businessId(), method, point.getArgs());
            
            // 检查是否存在成功的相同业务ID记录（仅在启用缓存时）
            AIServiceRecordPO existingRecord = null;
            if (aiServiceRecord.enableCache()) {
                existingRecord = findExistingSuccessRecord(businessId, aiServiceRecord.serviceType(), aiServiceRecord.cacheExpireTime());
                if (existingRecord != null) {
                    log.info("找到已存在的成功记录，直接返回缓存结果: businessId={}, returnType={}", 
                            existingRecord.getBusinessId(), method.getReturnType().getSimpleName());
                    return parseCachedResult(existingRecord.getOutputResult(), method.getReturnType());
                }
            }
            
            // 如果没有找到缓存记录，才创建新的服务记录
            record = createServiceRecord(point, aiServiceRecord, startTime, businessId);

            // 检查调用限制
            if (aiServiceRecord.enableLimit()) {
                if (!checkServiceLimit(record, aiServiceRecord)) {
                    throw new RuntimeException("服务调用次数超限");
                }
            }

            // 执行原方法
            result = point.proceed();

            // 更新记录为成功
            updateServiceRecord(record, result, System.currentTimeMillis() - startTime, 0, null);

            return result;
        } catch (Exception e) {
            // 更新记录为失败
            if (record != null) {
                updateServiceRecord(record, null, System.currentTimeMillis() - startTime, 1, e.getMessage());
            }
            throw e;
        }
    }

    /**
     * 创建服务记录
     */
    private AIServiceRecordPO createServiceRecord(ProceedingJoinPoint point, AIServiceRecord aiServiceRecord, long startTime, String businessId) {
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        Object[] args = point.getArgs();

        AIServiceRecordPO record = new AIServiceRecordPO();
        record.setServiceType(aiServiceRecord.serviceType());
        record.setBusinessId(businessId);
        record.setMethodName(method.getName());
        record.setClassName(method.getDeclaringClass().getName());
        record.setInputParams(JSON.toJSONString(args));
        record.setStatus(-1); // 初始状态为-1，表示处理中
        record.setCreateTime(startTime);
        record.setUpdateTime(startTime);
        record.setIsDeleted("0");

        // 从注解配置中提取字段值
        extractFieldsFromAnnotation(record, aiServiceRecord, method, args);

        // 异步保存记录
        if (aiServiceRecord.async()) {
            aiServiceRecordService.saveRecordAsync(record);
        } else {
            aiServiceRecordService.saveRecord(record);
        }

        return record;
    }

    /**
     * 解析业务ID
     */
    private String parseBusinessId(String businessIdExpression, Method method, Object[] args) {
        try {
            String[] paramNames = discoverer.getParameterNames(method);
            EvaluationContext context = new StandardEvaluationContext();

            if (paramNames != null) {
                for (int i = 0; i < paramNames.length && i < args.length; i++) {
                    context.setVariable(paramNames[i], args[i]);
                }
            }

            Expression expression = parser.parseExpression(businessIdExpression);
            Object value = expression.getValue(context);
            return value != null ? value.toString() : "";
        } catch (Exception e) {
            log.warn("解析业务ID失败: {}", businessIdExpression, e);
            return "";
        }
    }

    /**
     * 从注解配置中提取字段值
     */
    private void extractFieldsFromAnnotation(AIServiceRecordPO record, AIServiceRecord aiServiceRecord, Method method, Object[] args) {
        try {
            String[] paramNames = discoverer.getParameterNames(method);
            EvaluationContext context = new StandardEvaluationContext();

            if (paramNames != null) {
                for (int i = 0; i < paramNames.length && i < args.length; i++) {
                    context.setVariable(paramNames[i], args[i]);
                }
            }

            // 提取租户ID
            if (!aiServiceRecord.tenantId().isEmpty()) {
                try {
                    Expression expression = parser.parseExpression(aiServiceRecord.tenantId());
                    Object value = expression.getValue(context);
                    if (value != null) {
                        record.setTenantId((Integer) value);
                    }
                } catch (Exception e) {
                    log.warn("提取租户ID失败: {}", aiServiceRecord.tenantId(), e);
                }
            }

            // 提取用户ID
            if (!aiServiceRecord.userId().isEmpty()) {
                try {
                    Expression expression = parser.parseExpression(aiServiceRecord.userId());
                    Object value = expression.getValue(context);
                    if (value != null) {
                        record.setUserId((Integer) value);
                    }
                } catch (Exception e) {
                    log.warn("提取用户ID失败: {}", aiServiceRecord.userId(), e);
                }
            }

            // 提取模型ID
            if (!aiServiceRecord.modelId().isEmpty()) {
                try {
                    Expression expression = parser.parseExpression(aiServiceRecord.modelId());
                    Object value = expression.getValue(context);
                    if (value != null) {
                        record.setModelId(value.toString());
                    }
                } catch (Exception e) {
                    log.warn("提取模型ID失败: {}", aiServiceRecord.modelId(), e);
                }
            }

            // 提取规则ID
            if (!aiServiceRecord.ruleId().isEmpty()) {
                try {
                    Expression expression = parser.parseExpression(aiServiceRecord.ruleId());
                    Object value = expression.getValue(context);
                    if (value != null) {
                        record.setRuleId(value.toString());
                    }
                } catch (Exception e) {
                    log.warn("提取规则ID失败: {}", aiServiceRecord.ruleId(), e);
                }
            }

            // 提取场景
            if (!aiServiceRecord.scene().isEmpty()) {
                try {
                    Expression expression = parser.parseExpression(aiServiceRecord.scene());
                    Object value = expression.getValue(context);
                    if (value != null) {
                        record.setScene(value.toString());
                    }
                } catch (Exception e) {
                    log.warn("提取场景失败: {}", aiServiceRecord.scene(), e);
                }
            }

        } catch (Exception e) {
            log.error("从注解配置提取字段失败", e);
        }
    }

    /**
     * 更新服务记录
     */
    private void updateServiceRecord(AIServiceRecordPO record, Object result, long processTime, int status, String errorMsg) {
        record.setOutputResult(result != null ? JSON.toJSONString(result) : null);
        record.setProcessTime(processTime);
        record.setStatus(status);
        record.setErrorMsg(errorMsg);
        record.setUpdateTime(System.currentTimeMillis());

        // 异步更新记录
        aiServiceRecordService.updateRecordAsync(record);
    }

    /**
     * 查找已存在的成功记录
     */
    private AIServiceRecordPO findExistingSuccessRecord(String businessId, String serviceType, long cacheExpireTime) {
        try {
            List<AIServiceRecordPO> records = aiServiceRecordService.getByBusinessId(businessId);
            if (records != null && !records.isEmpty()) {
                long currentTime = System.currentTimeMillis();
                
                // 查找最新的成功记录（按创建时间倒序，第一个成功的记录就是最新的）
                for (AIServiceRecordPO record : records) {
                    if (record.getStatus() == 0 && serviceType.equals(record.getServiceType())) {
                        // 检查缓存是否过期
                        if (currentTime - record.getCreateTime() < cacheExpireTime || cacheExpireTime == -1) {
                            log.debug("找到有效的缓存记录: businessId={}, recordId={}, createTime={}", 
                                businessId, record.getId(), record.getCreateTime());
                            return record;
                        } else {
                            log.debug("缓存记录已过期: businessId={}, recordId={}, createTime={}", 
                                businessId, record.getId(), record.getCreateTime());
                            break; // 如果最新的记录都过期了，后面的记录肯定也过期
                        }
                    }
                }
            }
            return null;
        } catch (Exception e) {
            log.error("查找已存在记录失败: businessId={}, serviceType={}", businessId, serviceType, e);
            return null;
        }
    }

    /**
     * 解析缓存的返回结果
     */
    private Object parseCachedResult(String outputResult, Class<?> returnType) {
        try {
            if (outputResult == null || outputResult.isEmpty()) {
                log.debug("缓存结果为空，返回null");
                return null;
            }
            
            log.debug("解析缓存结果: returnType={}, outputResult={}", returnType.getSimpleName(), outputResult);
            
            // 根据返回类型进行解析
            if (returnType == String.class) {
                return outputResult;
            } else if (returnType == Integer.class || returnType == int.class) {
                return Integer.parseInt(outputResult);
            } else if (returnType == Long.class || returnType == long.class) {
                return Long.parseLong(outputResult);
            } else if (returnType == Boolean.class || returnType == boolean.class) {
                return Boolean.parseBoolean(outputResult);
            } else if (returnType == Double.class || returnType == double.class) {
                return Double.parseDouble(outputResult);
            } else if (returnType == Float.class || returnType == float.class) {
                return Float.parseFloat(outputResult);
            } else if (returnType == void.class || returnType == Void.class) {
                return null;
            } else {
                // 对于复杂对象，使用JSON解析
                Object result = JSON.parseObject(outputResult, returnType);
                log.debug("JSON解析成功: returnType={}, result={}", returnType.getSimpleName(), result);
                return result;
            }
        } catch (Exception e) {
            log.error("解析缓存结果失败: outputResult={}, returnType={}", outputResult, returnType.getName(), e);
            return null;
        }
    }

    /**
     * 检查服务限制
     */
    private boolean checkServiceLimit(AIServiceRecordPO record, AIServiceRecord aiServiceRecord) {
        try {
            return aiServiceRecordService.checkServiceLimit(record, aiServiceRecord);
        } catch (Exception e) {
            log.error("检查服务限制失败", e);
            return true; // 检查失败时允许调用
        }
    }
} 