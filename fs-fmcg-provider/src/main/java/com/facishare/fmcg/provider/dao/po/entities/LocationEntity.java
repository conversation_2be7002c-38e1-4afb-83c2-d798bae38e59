package com.facishare.fmcg.provider.dao.po.entities;

import lombok.Data;
import lombok.ToString;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Description
 * @date 2022-03-16 14:40
 **/
@Data
@ToString
public class LocationEntity implements Serializable {
    public static final String F_TOP_X = "TX";
    public static final String F_TOP_Y = "TY";
    public static final String F_WIDTH = "W";
    public static final String F_HEIGHT = "H";
    public static final String F_SCORE = "SC";


    @Property(F_SCORE)
    private Double score;

    @Property(F_TOP_X)
    private Long topX;

    @Property(F_TOP_Y)
    private Long topY;

    @Property(F_WIDTH)
    private Long width;

    @Property(F_HEIGHT)
    private Long height;
}
