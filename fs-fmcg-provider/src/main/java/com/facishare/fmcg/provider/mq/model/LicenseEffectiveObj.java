package com.facishare.fmcg.provider.mq.model;

import lombok.Data;
import lombok.ToString;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021/2/4 下午3:50
 */
@Data
@ToString
public class LicenseEffectiveObj {

    private String tenantId;
    private String productId;
    private String productVersion;
    private Long startTime;
    private Long expiredTime;
    private Boolean effective; //true -> 生效提醒   false -> 到期提醒
    private Integer dueDays;//临到期天数 (到期前15，7，3，0)
    private Integer maxHeadCount;//员工数
    private Set<String> moduleCodes; //对应moduleCode
    private List<MqModulePara> moduleParas; // 对应配额信息

    @Data
    @ToString
   public static class MqModulePara{
        private String moduleCode; // 对应的模块编码
        private String paraKey; // 此模块下的配额编码
        private String paraValue; // 配额数
    }
}
