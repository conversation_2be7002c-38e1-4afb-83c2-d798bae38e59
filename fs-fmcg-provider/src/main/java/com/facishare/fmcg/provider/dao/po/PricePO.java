package com.facishare.fmcg.provider.dao.po;

import lombok.Data;
import lombok.ToString;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Property;

/**
 * <AUTHOR>
 * @date 2020/5/29 下午2:20
 */
@Entity(value = "ai_price",noClassnameStored = true)
@Data
@ToString
public class PricePO {

    public static final String F_TENANT_ID ="TI";
    public static final String F_UNIT_PRICE="UP";
    public static final String F_MODEL_ID="MI";
    public static final String F_CREATE_TIME="CT";
    public static final String F_UPDATE_TIME="UT";
    public static final String F_CREATOR="CTR";
    public static final String F_UPDATER="UD";
    public static final String F_IS_DELETED="ID";

    @Id
    private ObjectId id;

    @Property(F_TENANT_ID)
    private Integer tenantId;

    @Property(F_UNIT_PRICE)
    private Double unitPrice;

    @Property(F_MODEL_ID)
    private String modelId;

    @Property(F_CREATE_TIME)
    private Long createTime;

    @Property(F_UPDATE_TIME)
    private Long updateTime;

    @Property(F_CREATOR)
    private Integer creator;

    @Property(F_UPDATER)
    private Integer updater;

    @Property(F_IS_DELETED)
    private Boolean isDeleted = false;
}
