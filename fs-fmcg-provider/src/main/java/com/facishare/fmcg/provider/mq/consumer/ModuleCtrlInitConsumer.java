package com.facishare.fmcg.provider.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.facishare.fmcg.api.error.FmcgException;
import com.facishare.fmcg.api.service.rebate.IRebateService;
import com.facishare.fmcg.provider.dao.abstraction.LicenseDAO;
import com.facishare.fmcg.provider.dao.po.LicensePo;
import com.facishare.fmcg.provider.mq.model.ModuleCtrlMessage;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.UUID;


@Component
public class ModuleCtrlInitConsumer {

    private static final Logger logger = LoggerFactory.getLogger(ModuleCtrlInitConsumer.class);
    private static final String CONFIG_NAME = "rocketmq-consumer.ini";
    private static final String SECTION_NAME = "common,name_server_08,module_ctrl_init_fmcg";
    private AutoConfMQPushConsumer processor;
    @Resource
    private LicenseDAO licenseDAO;
    @Resource
    private IRebateService rebateService;

    @PostConstruct
    public void init() {
        logger.info("ModuleCtrlInitConsumer start init.");
        MessageListenerConcurrently listener = (messages, context) -> {
            for (MessageExt messageExt : messages) {
                try {
                    TraceContext.get().setTraceId(UUID.randomUUID().toString());
                    process(messageExt);
                } catch (Exception ex) {
                    logger.error("[ModuleCtrlInitConsumer consumer error :" + messages + "]", ex);
                    return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                } finally {
                    TraceContext.remove();
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        };
        try {
            processor = new AutoConfMQPushConsumer(CONFIG_NAME, SECTION_NAME, listener);
            processor.start();
        } catch (Exception e) {
            logger.error("init ModuleCtrlInitConsumer mq consumer failed.", e);
        }
    }

    private void process(MessageExt messageExt) {
        ModuleCtrlMessage message = JSON.parseObject(messageExt.getBody(), ModuleCtrlMessage.class);
        if (messageExt.getReconsumeTimes() > 5) {
            logger.info("reach max err time.message。msg:{}", message);
            return;
        }
        logger.info("module ctrl customer mq:{}", message);
        if (!Objects.equals(message.getModuleCode(), "rebate")) {
            return;
        }
        int tenantId = Integer.parseInt(message.getTenantId());

        List<LicensePo> licensePos = licenseDAO.query(tenantId, Lists.newArrayList("FMCG.TPM", "FMCG.TPM.2"));
        if (CollectionUtils.isEmpty(licensePos)) {
            logger.info("tenant:{} has not open tpm switch.", tenantId);
            return;
        }
        try {
            rebateService.addField(tenantId, "RebateObj", "tpm_activity_id", true, false);
            rebateService.addField(tenantId, "RebateObj", "tpm_dealer_activity_cost_id", true, false);
        } catch (Exception ex) {
            logger.error("init rebate field error,", ex);
            throw new FmcgException("init rebate field error", -1);
        }

    }


    @PreDestroy
    public void shutDown() {
        processor.close();
    }
}
