package com.facishare.fmcg.provider.mq.consumer;

import com.facishare.converter.EIEAConverter;
import com.facishare.fmcg.api.dto.abstraction.ApiArg;
import com.facishare.fmcg.api.dto.common.organization.AppGray;
import com.facishare.fmcg.api.service.common.AppGraService;
import com.facishare.fmcg.provider.dao.abstraction.LicenseDAO;
import com.facishare.fmcg.provider.mq.model.LicenseMqObj;
import com.facishare.sandbox.listener.SandboxEvent;
import com.facishare.sandbox.module.CreateEvent;
import com.facishare.sandbox.module.DestroyEvent;
import com.facishare.sandbox.module.Module;
import com.fmcg.framework.common.TraceContextUtils;
import com.fmcg.framework.http.TPMProxy;
import com.fmcg.framework.http.contract.tpm.CopyTPMMongoData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2023/2/8 下午7:52
 */
@Slf4j
@Component
public class TPMSandBoxConsumer extends SandboxEvent {

    @Resource
    private TPMProxy tpmProxy;

    @Resource
    private LicenseDAO licenseDAO;

    @Resource
    private EIEAConverter eieaConverter;

    @Resource
    private AppGraService appGraService;

    public TPMSandBoxConsumer() {
        super("sandbox-copy-fmcg-tpm-consumer", Lists.newArrayList(Module.TPM));
        log.info("init sand box consumer.");
    }

    @Override
    protected boolean onSandboxCreated(String s, CreateEvent createEvent) {
        TraceContextUtils.init(createEvent.getTo().getEnterpriseAccount(), "sandbox-copy-fmcg-tpm-consumer", UUID.randomUUID().toString());
        log.info("model:{},event:{}", s, createEvent);
        try {
            int fromTenantId = createEvent.getFrom().getEnterpriseId();
            int toTenantId = createEvent.getTo().getEnterpriseId();
            CopyTPMMongoData.Arg copyArg = new CopyTPMMongoData.Arg();
            copyArg.setFromTenantId(String.valueOf(fromTenantId));
            copyArg.setToTenantId(String.valueOf(toTenantId));
            CopyTPMMongoData.Result result = tpmProxy.copyTPMMongoData(fromTenantId, -10000, copyArg);
            log.info("copy tpm data.{}", result);
            licenseDAO.copy(fromTenantId, toTenantId);

            addGrayAndInitializationTPM(toTenantId);
        } catch (Exception e) {
            log.error("sandbox copy err.", e);
        }
        return true;
    }

    private void addGrayAndInitializationTPM(int tenantId) {
        log.info("init sandbox addGrayAndInitializationTPM");
        try {
            //沙盒复制初始化应用，兼容深研bug
            String tenantAccount = eieaConverter.enterpriseIdToAccount(tenantId);
            ApiArg<AppGray.Arg> arg = new ApiArg<>();
            arg.setTenantId(tenantId);
            arg.setTenantAccount(tenantAccount);
            arg.setUserId(1000);
            arg.setData(new AppGray.Arg());
            appGraService.addGrayAndInitialization(arg);
            log.info("success sandbox addGrayAndInitializationTPM");
        } catch (Exception e) {
            log.info("addGrayAndInitialization err, tenantId : {},e : {}", tenantId, e.getMessage());
        }
        log.info("end sandbox addGrayAndInitializationTPM");
    }

    @Override
    protected boolean onSandboxDestroy(String s, DestroyEvent destroyEvent) {
        return true;
    }
}
