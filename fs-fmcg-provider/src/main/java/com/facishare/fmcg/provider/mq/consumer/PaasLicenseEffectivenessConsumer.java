package com.facishare.fmcg.provider.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.fmcg.adapter.checkins.CheckInsAdapter;
import com.facishare.fmcg.adapter.message.MessageService;
import com.facishare.fmcg.api.dto.abstraction.ApiArg;
import com.facishare.fmcg.api.dto.abstraction.ArgBase;
import com.facishare.fmcg.api.dto.common.license.Active;
import com.facishare.fmcg.api.dto.common.license.Invalid;
import com.facishare.fmcg.api.service.common.LicenseService;
import com.facishare.fmcg.provider.dao.abstraction.LicenseDAO;
import com.facishare.fmcg.provider.dao.po.LicensePo;
import com.facishare.fmcg.provider.impl.auto.AutoInitService;
import com.facishare.fmcg.provider.impl.dev.TenantDevService;
import com.facishare.fmcg.provider.license.AppCodeEnum;
import com.facishare.fmcg.provider.license.LicenseCodeAppIdEnum;
import com.facishare.fmcg.provider.mq.model.LicenseEffectiveObj;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2021/2/4 下午3:33
 */
@Component
public class PaasLicenseEffectivenessConsumer {

    private static final Logger logger = LoggerFactory.getLogger(PaasLicenseEffectivenessConsumer.class);
    //todo:config change
    private static final String CONFIG_NAME = "rocketmq-consumer.ini";
    private static final String SECTION_NAME = "common,name_server_02,consumer_license_effective_fmcg_service";
    private AutoConfMQPushConsumer processor;
    @Resource
    private LicenseService licenseService;
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private MessageService messageService;
    @Resource
    private CheckInsAdapter checkInsAdapter;
    @Resource
    private AutoInitService autoInitService;
    @Resource
    private LicenseDAO licenseDAO;
    @Resource
    private TenantDevService tenantDevService;

    @PostConstruct
    public void init() {
        logger.info("PaasLicenseEffectivenessConsumer start init.");
        MessageListenerConcurrently listener = (messages, context) -> {
            for (MessageExt messageExt : messages) {
                try {
                    TraceContext.get().setTraceId("PaasLicenseEffectivenessConsumer:" + UUID.randomUUID().toString());
                    process(messageExt);
                } catch (Exception ex) {
                    logger.error("[PaasLicenseEffectivenessConsumer consumer error :" + messages + "]", ex);
                    return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                } finally {
                    TraceContext.remove();
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        };
        try {
            processor = new AutoConfMQPushConsumer(CONFIG_NAME, SECTION_NAME, listener);
            processor.start();
        } catch (Exception e) {
            logger.error("init PaasLicenseEffectiveness mq consumer failed.", e);
        }
    }

    private void process(MessageExt messageExt) {
        if (messageExt.getReconsumeTimes() > 5) {
            logger.error("超过最大重试次数。{}", messageExt);
            return;
        }
        LicenseEffectiveObj license = JSON.parseObject(messageExt.getBody(), LicenseEffectiveObj.class);
        switch (license.getProductVersion()) {
            case "fmcg_ai_face_recognition_app":
                logger.info("license effective msg:{}", license);
                if (license.getEffective()) {
                    Active.Arg arg = new Active.Arg();
                    arg.setAppCode("FMCG.BAIDU_FACE_BASE_DETECT");
                    licenseService.active(fromApiArg(license, arg));
                } else if (!license.getEffective()) {
                    if (license.getDueDays() == 0) {
                        Invalid.Arg arg = new Invalid.Arg();
                        arg.setAppCode("FMCG.BAIDU_FACE_BASE_DETECT");
                        licenseService.invalid(fromApiArg(license, arg));
                    }
                    sendFaceExpiredMessage(eieaConverter.enterpriseIdToAccount(Integer.parseInt(license.getTenantId())), license.getDueDays());
                }
                break;
            default:
        }

        boolean isTPMLimitCode = !org.springframework.util.CollectionUtils.isEmpty(license.getModuleParas())
                && license.getModuleParas().stream().anyMatch(v -> LicenseCodeAppIdEnum.TPM_LIMIT.getCode().equals(v.getParaKey()));
        if (!CollectionUtils.isEmpty(license.getModuleCodes()) && license.getModuleCodes().contains("trade_promotion_management_app")
                || license.getProductVersion().equals("trade_promotion_management_app") || isTPMLimitCode) {

            if (license.getEffective()) {

                int tenantId = Integer.parseInt(license.getTenantId());
                List<LicensePo> pos = licenseDAO.query(tenantId, Lists.newArrayList(AppCodeEnum.TPM2.code(), AppCodeEnum.TPM.code()));

                logger.info("补刷开箱即用");
                if (tenantDevService.isCloudTenant(tenantId)) {
                    logger.info("tenantId is not belong foneshare dev, tenantId : {} ", tenantId);
                    if (CollectionUtils.isEmpty(pos)) {
                        activePreTPM(tenantId, isTPMLimitCode);
                    }
                } else {
                    logger.info("PaasLicenseEffectivenessConsumer autoTPM, tenantId : {} ", tenantId);
                    autoInitService.autoTPM(tenantId, isTPMLimitCode);
                }
            }

        }

        dealExpenseInit(license);

    }

    private void dealExpenseInit(LicenseEffectiveObj license) {
        if(!CollectionUtils.isEmpty(license.getModuleCodes()) && license.getModuleCodes().contains("custom_object")) {
            if(license.getEffective()){
                logger.info("license EXPENSE_LICENSE effective msg:{}", license);
                Active.Arg arg = new Active.Arg();
                arg.setAppCode("FMCG.EXPENSE_LICENSE");
                licenseService.active(fromApiArg(license, arg));
            }
        }
    }

    private void activePreTPM(int tenantId, boolean isTPMLimitCode) {
        ApiArg<Active.Arg> arg = new ApiArg<>();
        Active.Arg activeArg = new Active.Arg();
        activeArg.setAppCode(isTPMLimitCode ? AppCodeEnum.TPM_LIMIT.code() : AppCodeEnum.TPM2.code());
        arg.setData(activeArg);
        arg.setUserId(-10000);
        arg.setTenantId(tenantId);
        arg.setTenantAccount(eieaConverter.enterpriseIdToAccount(arg.getTenantId()));
        licenseService.active(arg);
    }


    private <T extends ArgBase> ApiArg<T> fromApiArg(LicenseEffectiveObj license, T data) {
        int tenantId = Integer.parseInt(license.getTenantId());
        return fromApiArg(tenantId, eieaConverter.enterpriseIdToAccount(tenantId), data);
    }

    private void sendFaceExpiredMessage(String tenantAccount, int leftDay) {
        List<Integer> admins = checkInsAdapter.getAppAdminList(tenantAccount);
        String title = "AI自拍识别到期提醒";
        String subTile = "贵公司的外勤AI自拍识别，今天已到期";
        String content = "现在起将自动关闭自拍时的AI智能识别功能，若需要继续使用，请联系客服（400-1122-778）续费。";
        if (leftDay > 0) {
            subTile = String.format("贵公司的外勤AI自拍识别，%s天后即将到期", leftDay);
            content = "若需要继续使用，请及时联系客服（400-1122-778）续费。";
        }
        messageService.sendLimitedNotifyAsync(tenantAccount, admins, "WQZS", title, subTile, content);
    }

    private <T extends ArgBase> ApiArg<T> fromApiArg(Integer tenantId, String tenantAccount, T data) {
        ApiArg<T> apiArg = new ApiArg<>();
        apiArg.setTenantAccount(tenantAccount);
        apiArg.setTenantId(tenantId);
        apiArg.setUserId(-10000);
        apiArg.setData(data);
        return apiArg;
    }

    @PreDestroy
    public void shutDown() {
        processor.close();
    }
}
