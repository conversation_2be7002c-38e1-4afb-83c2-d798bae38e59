package com.facishare.fmcg.provider.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.common.rocketmq.AutoConfRocketMQProcessor;
import com.facishare.converter.EIEAConverter;
import com.facishare.fmcg.adapter.metadata.DataAdapter;
import com.facishare.fmcg.adapter.metadata.dto.data.QueryLayout;
import com.facishare.fmcg.api.dto.abstraction.ApiArg;
import com.facishare.fmcg.api.dto.abstraction.ArgBase;
import com.facishare.fmcg.api.dto.common.license.Active;
import com.facishare.fmcg.api.dto.common.license.Upgrade;
import com.facishare.fmcg.api.service.common.LicenseService;
import com.facishare.fmcg.provider.business.abstraction.TPMActivityMaterialBusiness;
import com.facishare.fmcg.provider.license.handler.BaiduFaceBaseDetectLicenseHandler;
import com.facishare.fmcg.provider.mq.model.LicenseMqObj;
import com.facishare.fmcg.provider.mq.model.TriggerMessageObj;
import com.fmcg.framework.http.PaasDescribeProxy;
import com.fmcg.framework.http.contract.paas.describe.PaasDescribeCreateField;
import com.fmcg.framework.http.contract.paas.describe.PaasDescribeFieldDelete;
import com.fmcg.framework.http.contract.paas.describe.PaasDescribeGet;
import com.google.common.collect.Lists;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * author: wuyx
 * description:
 * createTime: 2022/8/5 18:44
 */
@Component
public class ObjectDescribeMQConsumer {
    private static final Logger logger = LoggerFactory.getLogger(ObjectDescribeMQConsumer.class);

    private static final String CONFIG_NAME = "fs-fmcg-object-mq";
    private static final String NAME_SERVER_KEY = "OBJECT_DESCRIBE_NAMESERVER";
    private static final String TOPIC_KEY = "OBJECT_DESCRIBE_TOPICS";
    private static final String GROUP_KEY = "OBJECT_DESCRIBE_GROUP_CONSUMER";

    private AutoConfRocketMQProcessor processor;
    @Resource
    private PaasDescribeProxy paasDescribeProxy;
    @Resource
    private DataAdapter dataAdapter;
    @Resource
    private TPMActivityMaterialBusiness tpmActivityMaterialBusiness;

    @Resource
    private LicenseService licenseService;

    @Resource
    private EIEAConverter eieaConverter;


    @PostConstruct
    public void init() {
        logger.info("ObjectDescribeConsumer start init.");
        MessageListenerConcurrently listener = (messages, context) -> {
            for (MessageExt messageExt : messages) {
                try {
                    processMessage(messageExt);
                } catch (Exception ex) {
                    logger.error("[ObjectDescribeConsumer consumer error :" + messages + "]", ex);
                    return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        };
        try {
            processor = new AutoConfRocketMQProcessor(CONFIG_NAME, NAME_SERVER_KEY, GROUP_KEY, TOPIC_KEY, listener);
            processor.init();
        } catch (Exception e) {
            logger.error("init object mq consumer failed.", e);
        }

    }

    private void processMessage(MessageExt messageExt) {
        logger.info("init obj describe processMessage");
        try {
            String dataJsonStr = new String(messageExt.getBody());
            TriggerMessageObj msg = JSON.parseObject(dataJsonStr, TriggerMessageObj.class);
            logger.info("obj describe mq msg：{}", msg);
            if (messageExt.getReconsumeTimes() > 5) {
                logger.info("再次消费次数达到5.msg:{}", msg);
                return;
            }

            List<JSONObject> body = msg.getBody();

            String name = msg.getName();
            if ("object_field".equals(name)) {
                List<String> objectDescribeApiNames = body.stream().map(json -> json.getString("object_describe_api_name")).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(objectDescribeApiNames)) {
                    return;
                }

                for (String objectDescribeApiName : objectDescribeApiNames) {
                    if ("TPMBudgetAccountObj".equals(objectDescribeApiName)) {
                        doTPMBudgetAccountObjMQ(msg, body);
                    }

                    if ("MaterialObj".equals(objectDescribeApiName)) {
                        doMaterialObjMQ(msg, body);
                    }
                }
            }

            if ("object_describe".equals(name)) {
                List<String> objectDescribeApiNames = body.stream().map(json -> json.getString("describe_api_name")).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(objectDescribeApiNames)) {
                    return;
                }

                for (String objectDescribeApiName : objectDescribeApiNames) {
                    if ("MaterialObj".equals(objectDescribeApiName)) {
                        doMaterialObjMQ(msg, body);

                    }
                    if ("PublicEmployeeObj".equals(objectDescribeApiName)) {
                        initFaceFiled(msg);
                    }
                }
            }

        } catch (Exception ex) {
            logger.error("[notice consumer error]", ex);
            throw ex;
        }
    }

    private void doMaterialObjMQ(TriggerMessageObj msg, List<JSONObject> body) {
        int tenantId = Integer.parseInt(msg.getTenantId());
        String name = msg.getName();

        if ("object_describe".equals(name)) {
            JSONObject materialObj = body.stream().filter(json -> "MaterialObj".equals(json.getString("describe_api_name"))).findFirst().orElse(new JSONObject());
            if (materialObj.isEmpty()) {
                return;
            }

            if (!"describe_create".equals(msg.getOp())) {
                return;
            }

            tpmActivityMaterialBusiness.addTPMActivityMaterialObjAndField(tenantId);
        }

        if ("object_field".equals(name)) {
            JSONObject tpmBudgetAccountObj = body.stream().filter(json -> "MaterialObj".equals(json.getString("object_describe_api_name"))).findFirst().orElse(new JSONObject());
            if (tpmBudgetAccountObj.isEmpty()) {
                return;
            }

            JSONArray fields = tpmBudgetAccountObj.getJSONArray("fields");
            if (fields.isEmpty()) {
                return;
            }

            List<String> objectReferenceFields = fields.stream().filter(o -> {
                JSONObject field = JSON.parseObject(o.toString());
                return "material_unit".equals(field.getString("field_api_name"));
            }).map(o -> JSON.parseObject(o.toString()).getString("field_api_name")).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(objectReferenceFields)) {
                return;
            }

            if (!"field_add".equals(msg.getOp())) {
                return;
            }
            tpmActivityMaterialBusiness.addTPMActivityMaterialObjField(tenantId);
        }
    }

    private void initFaceFiled(TriggerMessageObj msg) {

        if (!"describe_create".equals(msg.getOp())) {
            return;
        }

        Integer tenantId = Integer.valueOf(msg.getTenantId());
        Upgrade.Arg arg = new Upgrade.Arg();
        arg.setAppCode("FMCG.BAIDU_FACE_BASE_DETECT");
        licenseService.upgrade(fromApiArg(tenantId, eieaConverter.enterpriseIdToAccount(tenantId), -10000, arg));
    }

    private <T extends ArgBase> ApiArg<T> fromApiArg(Integer tenantId, String tenantAccount, Integer userId, T data) {
        ApiArg<T> apiArg = new ApiArg<>();
        apiArg.setTenantAccount(tenantAccount);
        apiArg.setTenantId(tenantId);
        apiArg.setUserId(userId);
        apiArg.setData(data);
        return apiArg;
    }

    private void doTPMBudgetAccountObjMQ(TriggerMessageObj msg, List<JSONObject> body) {
        JSONObject tpmBudgetAccountObj = body.stream().filter(json -> "TPMBudgetAccountObj".equals(json.getString("object_describe_api_name"))).findFirst().orElse(new JSONObject());
        if (tpmBudgetAccountObj.isEmpty()) {
            return;
        }

        JSONArray fields = tpmBudgetAccountObj.getJSONArray("fields");
        if (fields.isEmpty()) {
            return;
        }

        List<String> objectReferenceFields = fields.stream().filter(o -> {
            JSONObject field = JSON.parseObject(o.toString());
            return "object_reference".equals(field.getString("field_type"));
        }).map(o -> JSON.parseObject(o.toString()).getString("field_api_name")).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(objectReferenceFields)) {
            return;
        }

        int tenantId = Integer.parseInt(msg.getTenantId());
        PaasDescribeGet.Result describeRst = paasDescribeProxy.get(tenantId, -10000, "TPMBudgetAccountObj");
        if (describeRst.getCode() != 0) {
            logger.info("get TPMBudgetAccountObj error ,errorMsg={}", describeRst.getMessage());
            return;
        }

        Map<String, JSONObject> budgetAccountFiles = describeRst.getData().getDescribe().getFields();
        Map<String, JSONObject> fieldDescribeMap = budgetAccountFiles.keySet().stream().filter(objectReferenceFields::contains).collect(Collectors.toMap(k -> k, budgetAccountFiles::get));

        List<String> objectApiNames = Lists.newArrayList("TPMBudgetStatisticTableObj", "TPMBudgetDisassemblyNewDetailObj");
        for (String objectApiName : objectApiNames) {
            syncBudgetAccountFilesToTargetDescribeObj(msg, objectReferenceFields, tenantId, fieldDescribeMap, objectApiName);
        }
    }

    private void syncBudgetAccountFilesToTargetDescribeObj(TriggerMessageObj msg, List<String> objectReferenceFields, int tenantId, Map<String, JSONObject> fieldDescribeMap, String objectApiName) {
        if (!fieldDescribeMap.isEmpty() && !"field_delete".equals(msg.getOp())) {
            for (Map.Entry<String, JSONObject> entry : fieldDescribeMap.entrySet()) {
                JSONObject value = entry.getValue();
                String fieldApiName = entry.getKey();
                value.put("describe_api_name", objectApiName);
                value.remove("_id");
                switch (msg.getOp()) {
                    case "field_add":
                        PaasDescribeCreateField.Arg arg = new PaasDescribeCreateField.Arg();
                        arg.setDescribeAPIName(objectApiName);
                        arg.setGroupFields("[]");
                        arg.setFieldDescribe(value.toJSONString());
                        arg.setLayoutList(queryLayOut(tenantId, objectApiName));
                        paasDescribeProxy.createField(tenantId, -10000, arg);
                        break;
                    case "field_update":
                        paasDescribeProxy.updateField(tenantId, -10000, objectApiName, fieldApiName, value);
                        break;
                    case "field_enable":
                        value.put("is_active", true);
                        paasDescribeProxy.updateField(tenantId, -10000, objectApiName, fieldApiName, value);
                        break;
                    case "field_disable":
                        PaasDescribeFieldDelete.Arg disableArg = new PaasDescribeFieldDelete.Arg();
                        disableArg.setDescribeAPIName(objectApiName);
                        disableArg.setName(fieldApiName);
                        paasDescribeProxy.disableField(tenantId, -10000, disableArg);
                        break;
                    default:
                        break;
                }
            }
        } else if ("field_delete".equals(msg.getOp())) {
            for (String fieldApiName : objectReferenceFields) {
                PaasDescribeFieldDelete.Arg deleteArg = new PaasDescribeFieldDelete.Arg();
                deleteArg.setDescribeAPIName(objectApiName);
                deleteArg.setName(fieldApiName);
                paasDescribeProxy.deleteField(tenantId, -10000, deleteArg);
            }
        }
    }

    private String queryLayOut(Integer tenantId, String describeApiName) {

        List<JSONObject> layoutList = queryLayoutList(tenantId, describeApiName);
        List<JSONObject> fieldLayout = Lists.newArrayList();
        for (JSONObject completeLayout : layoutList) {
            if (!"detail".equals(completeLayout.getString("layout_type")))
                continue;
            JSONObject tempLayout = new JSONObject();
            fieldLayout.add(tempLayout);
            String label = completeLayout.getString("display_name");
            tempLayout.put("api_name", completeLayout.getString("api_name"));
            tempLayout.put("label", label);
            tempLayout.put("layout_type", "detail");
            tempLayout.put("render_type", "object_reference");
            tempLayout.put("is_show", true);
            tempLayout.put("is_required", false);
            tempLayout.put("is_readonly", false);
        }

        return fieldLayout.toString();
    }

    private List<JSONObject> queryLayoutList(Integer tenantId, String apiName) {
        QueryLayout.Arg arg = new QueryLayout.Arg();
        arg.setObjectDescribeApiName(apiName);
        try {
            QueryLayout.Result r = dataAdapter.queryLayout(tenantId, arg);
            if (r.getLayouts() != null && !r.getLayouts().isEmpty()) {

                return r.getLayouts();
            }
        } catch (Exception e) {
            logger.error("get layout from {}.{} err", tenantId, apiName);
        }
        return new ArrayList<>();
    }

    @PreDestroy
    public void shutDown() {
        processor.shutDown();
    }
}
