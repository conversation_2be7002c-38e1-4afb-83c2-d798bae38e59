package com.facishare.fmcg.provider.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.facishare.converter.EIEAConverter;
import com.facishare.fmcg.adapter.config.ConfigService;
import com.facishare.fmcg.adapter.interconnection.EnterpriseRelationAdapter;
import com.facishare.fmcg.adapter.license.LicenseAdapter;
import com.facishare.fmcg.adapter.redisCache.TPMLocalCache;
import com.facishare.fmcg.adapter.route.DBRouterAdapter;
import com.facishare.fmcg.api.dto.abstraction.ApiArg;
import com.facishare.fmcg.api.dto.abstraction.ArgBase;
import com.facishare.fmcg.api.dto.ai.charge.SynLicenseToChargeDetail;
import com.facishare.fmcg.api.dto.common.license.Active;
import com.facishare.fmcg.api.dto.common.license.Upgrade;
import com.facishare.fmcg.api.dto.common.organization.AppGray;
import com.facishare.fmcg.api.error.FmcgException;
import com.facishare.fmcg.api.service.ai.charge.AIChargeDetailService;
import com.facishare.fmcg.api.service.common.AppGraService;
import com.facishare.fmcg.api.service.common.LicenseService;
import com.facishare.fmcg.provider.business.abstraction.DescribeBusiness;
import com.facishare.fmcg.provider.concurrent.ParallelTaskUtil;
import com.facishare.fmcg.provider.dao.abstraction.LicenseDAO;
import com.facishare.fmcg.provider.dao.enumeration.AIChargeDetailType;
import com.facishare.fmcg.provider.dao.po.LicensePo;
import com.facishare.fmcg.provider.impl.auto.AutoInitService;
import com.facishare.fmcg.provider.impl.dev.TenantDevService;
import com.facishare.fmcg.provider.license.AppCodeEnum;
import com.facishare.fmcg.provider.license.LicenseCodeAppIdEnum;
import com.facishare.fmcg.provider.mq.model.LicenseMqObj;
import com.facishare.uc.api.model.enterprise.arg.BatchGetSimpleEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.BatchGetSimpleEnterpriseDataResult;
import com.facishare.uc.api.model.fscore.SimpleEnterpriseData;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fmcg.framework.common.TraceContextUtils;
import com.fmcg.framework.http.WeexConsoleProxy;
import com.fmcg.framework.http.contract.weex.UpdateGray;
import com.fxiaoke.common.MapUtils;
import com.fxiaoke.common.release.GrayRelease;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.elasticsearch.common.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2021/2/4 下午3:32
 */
@Component
public class PaasLicenseConsumer {

    private static final Logger logger = LoggerFactory.getLogger(PaasLicenseConsumer.class);
    //todo:config change
    private static final String CONFIG_NAME = "rocketmq-consumer.ini";
    private static final String SECTION_NAME = "common,name_server_09,consumer_license_create_fmcg_service";

    private static final String PROCESS_PROFILE = System.getProperty("process.profile");

    private static final String ENVIRONMENT_TYPE = System.getenv("ENVIRONMENT_TYPE");

    private AutoConfMQPushConsumer processor;
    @Resource
    private LicenseService licenseService;
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private AIChargeDetailService aiChargeDetailService;
    @Resource
    private LicenseDAO licenseDAO;
    @Resource
    private ConfigService configService;
    @Resource
    private WeexConsoleProxy weexConsoleProxy;
    @Resource
    private AutoInitService autoInitService;
    @Resource
    private AppGraService appGraService;
    @Resource
    private TenantDevService tenantDevService;
    @Resource
    private DBRouterAdapter dbRouterAdapter;

    @Resource
    private EnterpriseEditionService enterpriseEditionService;
    @Resource
    private DescribeBusiness describeBusiness;
    @Resource
    private LicenseAdapter licenseAdapter;
    @Resource
    private TPMLocalCache tpmLocalCache;
    private static final ScheduledExecutorService scheduledExecutorService = Executors.newScheduledThreadPool(5);


    @Resource
    private EnterpriseRelationAdapter enterpriseRelationAdapter;

    @PostConstruct
    public void init() {
        logger.info("PaasLicenseConsumer start init.");
        MessageListenerConcurrently listener = (messages, context) -> {
            for (MessageExt messageExt : messages) {
                try {
                    TraceContext.get().setTraceId(UUID.randomUUID().toString());
                    process(messageExt);
                } catch (Exception ex) {
                    logger.error("[PaasLicenseConsumer consumer error :" + messages + "]", ex);
                    return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                } finally {
                    TraceContext.remove();
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        };
        try {
            processor = new AutoConfMQPushConsumer(CONFIG_NAME, SECTION_NAME, listener);
            processor.start();
        } catch (Exception e) {
            logger.error("init PaasLicenseConsumer mq consumer failed.", e);
        }
    }

    private void process(MessageExt messageExt) {
        LicenseMqObj licenseMqObj = JSON.parseObject(messageExt.getBody(), LicenseMqObj.class);
        TraceContextUtils.init(licenseMqObj.getTenantId(), "FMCG-LICENSE-MQ", UUID.randomUUID().toString());
        if (messageExt.getReconsumeTimes() > 5) {
            logger.error("超过最大重试次数。{}", messageExt);
            return;
        }

        logger.info("license:{}", licenseMqObj);
        int tenantId = Integer.parseInt(licenseMqObj.getTenantId());

        boolean isCurrentCloudTenant;
        try {

            isCurrentCloudTenant = tenantDevService.isCurrentCloudTenant(tenantId);
            if (!isCurrentCloudTenant) {
                logger.info("tenantId is not belong current cloud dev,tenantId:{}", tenantId);
                return;
            }

            if (!dbRouterAdapter.existsPGDBRouter(String.valueOf(tenantId))) {
                logger.info("企业没有路由。ei:{}", tenantId);
                return;
            }

        } catch (Exception e) {
            logger.info("tenantId get current dev err,tenantId:{}", tenantId);
            return;
        }

        ArrayList<String> traceId = Lists.newArrayList(TraceContext.get().getTraceId());
        String key = tpmLocalCache.getKey(String.valueOf(tenantId), TPMLocalCache.KEY_TPM_LICENSE, traceId.toArray(new String[0]));
        tpmLocalCache.save(key, JSON.toJSONString(licenseMqObj));

        if (!CollectionUtils.isEmpty(licenseMqObj.getMqModuleParas()) && licenseMqObj.getMqModuleParas().stream().anyMatch(v -> "photo_identify_limit".equals(v.getParaKey()))) {
            logger.info("license create msg:{}", licenseMqObj);
            synAIChargeRecord(licenseMqObj);
        }


        if (!CollectionUtils.isEmpty(licenseMqObj.getModuleCodes()) &&
                (licenseMqObj.getModuleCodes().contains("points_incentive_app") || licenseMqObj.getModuleCodes().contains("user_license_points_incentive_by_user_limit")
                        || licenseMqObj.getModuleCodes().contains("points_incentive_by_user_app"))) {
            logger.info("license create msg:{}", licenseMqObj);
            Active.Arg arg = new Active.Arg();
            arg.setAppCode(AppCodeEnum.INTEGRAL_LIMIT.code());
            licenseService.active(fromApiArg(licenseMqObj, arg));
        }


        String tags = messageExt.getTags();
        if (!Strings.isNullOrEmpty(tags) && tags.contains("sandbox")) {
            logger.info("sandbox copy reject open box");
            return;
        }

        if (!CollectionUtils.isEmpty(licenseMqObj.getModuleCodes()) && licenseMqObj.getModuleCodes().contains("fmcg_ai_face_recognition_app")) {
            logger.info("license create msg:{}", licenseMqObj);
            Upgrade.Arg arg = new Upgrade.Arg();
            arg.setAppCode("FMCG.BAIDU_FACE_BASE_DETECT");
            licenseService.upgrade(fromApiArg(licenseMqObj, arg));
        }

        if (!CollectionUtils.isEmpty(licenseMqObj.getModuleCodes()) && licenseMqObj.getModuleCodes().contains("fmcg_ai_store_sign_recognition_app")) {
            logger.info("license create msg:{}", licenseMqObj);
            Upgrade.Arg arg = new Upgrade.Arg();
            arg.setAppCode("FMCG.STORE_FRONT_DETECT");
            licenseService.upgrade(fromApiArg(licenseMqObj, arg));
        }



        boolean isTPMLimitCode = !CollectionUtils.isEmpty(licenseMqObj.getMqModuleParas())
                && licenseMqObj.getMqModuleParas().stream().anyMatch(v -> LicenseCodeAppIdEnum.TPM_LIMIT.getCode().equals(v.getParaKey()));
        if (!CollectionUtils.isEmpty(licenseMqObj.getModuleCodes()) && licenseMqObj.getModuleCodes().contains("trade_promotion_management_app")
                || licenseMqObj.getLicenseVersion().equals("trade_promotion_management_app") || isTPMLimitCode) {
            if (enterpriseRelationAdapter.isConnectTemplateCopyTenant(tenantId)) {
                logger.info("template copy reject preset TPM, tenantId : {} ", tenantId);
                addGrayAndInitializationTPM(tenantId);
                return;
            }

            if (!licenseAdapter.validate(tenantId, "trade_promotion_management_app")) {
                logger.info("tpm2 license未生效");
                throw new FmcgException("tpm2 license未生效", 10000);
            }

            //判断新企业
            if (!existTPMLimitTenant(tenantId)) {
                if (!existTPM1Tenant(tenantId)) {
                    if (GrayRelease.isAllow("fmcg", "SKIP_PRESET_TPM_FROM_SCRIPT", tenantId)) {
                        logger.info("skip preset script TPM ,tenantId : {}", tenantId);
                        return;
                    }
                    boolean cloudTenant = tenantDevService.isCloudTenant(tenantId);

                    if (cloudTenant) {
                        logger.info("cloud preset from script ");
                        activePreTPM(tenantId, isTPMLimitCode);
                    } else {
                        if (GrayRelease.isAllow("fmcg", "AUTO_TPM_COPY", tenantId)) {
                            logger.info("copy from module tenant");
                            autoInitService.autoTPM(tenantId, isTPMLimitCode);
                        } else {
                            logger.info("preset from script ");
                            activePreTPM(tenantId, isTPMLimitCode);
                        }
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(licenseMqObj.getModuleCodes()) && licenseMqObj.getModuleCodes().contains("code_marketing_campaign_app")
                || licenseMqObj.getLicenseVersion().equals("code_marketing_campaign_app")) {
            if (enterpriseRelationAdapter.isConnectTemplateCopyTenant(tenantId)) {
                logger.info("template copy reject preset TPM code, tenantId : {} ", tenantId);
                addGrayAndInitializationTPM(tenantId);
                return;
            }

            //判断新企业
            if (isNewTPMCodeTenant(tenantId)) {
                logger.info("cloud preset tpm code from script ");
                activePreTPMCode(tenantId);
            }
        }


        boolean isBudgetLimitCode = !CollectionUtils.isEmpty(licenseMqObj.getMqModuleParas())
                && licenseMqObj.getMqModuleParas().stream().anyMatch(v -> LicenseCodeAppIdEnum.BUDGET_LIMIT.getCode().equals(v.getParaKey()));
        if ((!CollectionUtils.isEmpty(licenseMqObj.getModuleCodes()) && licenseMqObj.getModuleCodes().contains("budget_management_app")) || isBudgetLimitCode) {
            logger.info("license create budget msg:{}", licenseMqObj);
            Active.Arg arg = new Active.Arg();
            arg.setAppCode(isBudgetLimitCode ? AppCodeEnum.BUDGET_LIMIT.code() : AppCodeEnum.TPM_BUDGET_ACCOUNT.code());
            licenseService.active(fromApiArg(licenseMqObj, arg));
        }

        if (!CollectionUtils.isEmpty(licenseMqObj.getModuleCodes()) && licenseMqObj.getModuleCodes().contains("kx_industry")) {
            describeBusiness.addFieldAndCheckFieldIsExist(tenantId, "ProductObj", "quality_guarantee_period", false, true);
        }

        if(!CollectionUtils.isEmpty(licenseMqObj.getModuleCodes()) && licenseMqObj.getModuleCodes().contains("custom_object")) {
            logger.info("EXPENSE_LICENSE license create msg:{}", licenseMqObj);
            Active.Arg arg = new Active.Arg();
            arg.setAppCode("FMCG.EXPENSE_LICENSE");

            scheduledExecutorService.schedule(() -> licenseService.active(fromApiArg(licenseMqObj, arg)), 10, TimeUnit.SECONDS);
        }

        tpmLocalCache.del(key);
    }

    private void activePreTPM(int tenantId, boolean isTPMLimitCode) {
        ApiArg<Active.Arg> arg = new ApiArg<>();
        Active.Arg activeArg = new Active.Arg();
        activeArg.setAppCode(isTPMLimitCode ? AppCodeEnum.TPM_LIMIT.code() : AppCodeEnum.TPM2.code());
        arg.setData(activeArg);
        arg.setUserId(-10000);
        arg.setTenantId(tenantId);
        arg.setTenantAccount(eieaConverter.enterpriseIdToAccount(arg.getTenantId()));
        licenseService.active(arg);
    }

    private void activePreTPMCode(int tenantId) {
        ApiArg<Active.Arg> arg = new ApiArg<>();
        Active.Arg activeArg = new Active.Arg();
        activeArg.setAppCode(AppCodeEnum.TPM_CODE.code());
        arg.setData(activeArg);
        arg.setUserId(-10000);
        arg.setTenantId(tenantId);
        arg.setTenantAccount(eieaConverter.enterpriseIdToAccount(arg.getTenantId()));
        licenseService.active(arg);
    }

    private void addGrayAndInitializationTPM(int tenantId) {
        logger.info("init sandbox addGrayAndInitializationTPM");
        try {
            //沙盒复制初始化应用，兼容深研bug
            String tenantAccount = eieaConverter.enterpriseIdToAccount(tenantId);
            ApiArg<AppGray.Arg> arg = new ApiArg<>();
            arg.setTenantId(tenantId);
            arg.setTenantAccount(tenantAccount);
            arg.setUserId(1000);
            arg.setData(new AppGray.Arg());
            appGraService.addGrayAndInitialization(arg);
            logger.info("success sandbox addGrayAndInitializationTPM");
        } catch (Exception e) {
            logger.info("addGrayAndInitialization err, tenantId : {},e : {}", tenantId, e.getMessage());
        }
        logger.info("end sandbox addGrayAndInitializationTPM");
    }

    private boolean isFoneShareEnv(int tenantId) {
        BatchGetSimpleEnterpriseDataArg arg = new BatchGetSimpleEnterpriseDataArg();
        arg.setEnterpriseIds(Lists.newArrayList(tenantId));

        BatchGetSimpleEnterpriseDataResult batchGetSimpleEnterpriseDataResult = enterpriseEditionService.batchGetSimpleEnterpriseData(arg);
        List<SimpleEnterpriseData> enterpriseData = batchGetSimpleEnterpriseDataResult.getSimpleEnterpriseList();

        logger.info("isFoneShareEnv tenantId:{} , simpleEnterpriseData:{}", tenantId, JSON.toJSONString(enterpriseData));
        if (!CollectionUtils.isEmpty(enterpriseData)) {
            Integer env = enterpriseData.stream().filter(simpleEnterpriseData -> tenantId == simpleEnterpriseData.getEnterpriseId()).map(SimpleEnterpriseData::getEnv).findAny().orElse(0);
            return env == 1;
        }
        return false;
    }

    private String getProperty() {
        String candidates = System.getProperty("process.profile.candidates");
        if (!Strings.isNullOrEmpty(candidates)) {
            String[] properties = candidates.split(",");
            return properties[0];
        } else {
            String env = System.getenv("ENVIRONMENT_TYPE");
            if ("firstshare".equals(env)) {
                return "fstest";
            } else if ("foneshare".equals(env)) {
                return "foneshare";
            } else if (!Strings.isNullOrEmpty(env)) {
                return "cloud";
            }
        }
        return System.getProperty("process.profile");
    }

    private Boolean isNotCloudStage() {
        if (GrayRelease.isAllow("fmcg", "license_mq_foneshare_judge", -1)) {
            return false;
        }

        if (Strings.isNullOrEmpty(ENVIRONMENT_TYPE) || ENVIRONMENT_TYPE.contains("foneshare")) {
            boolean result = !Strings.isNullOrEmpty(PROCESS_PROFILE) || PROCESS_PROFILE.contains("foneshare");
            if (!result) {
                logger.info("env:{},profile:{}", ENVIRONMENT_TYPE, PROCESS_PROFILE);
            }
            return result;
        }
        return true;
    }

    private <T extends ArgBase> ApiArg<T> fromApiArg(LicenseMqObj license, T data) {
        int tenantId = Integer.parseInt(license.getTenantId());
        return fromApiArg(tenantId, eieaConverter.enterpriseIdToAccount(tenantId), data);
    }

    private void synAIChargeRecord(LicenseMqObj licenseMqObj) {
        ApiArg<SynLicenseToChargeDetail.Arg> apiArg = new ApiArg<>();
        SynLicenseToChargeDetail.Arg arg = new SynLicenseToChargeDetail.Arg();
        arg.setType(AIChargeDetailType.OBJECT_DETECT.value());
        apiArg.setData(arg);
        apiArg.setTenantId(Integer.parseInt(licenseMqObj.getTenantId()));
        apiArg.setUserId(-10000);
        apiArg.setTenantAccount(eieaConverter.enterpriseIdToAccount(apiArg.getTenantId()));
        aiChargeDetailService.synLicenseToChargeDetail(apiArg);
    }

    private boolean existTPM1Tenant(Integer tenantId) {
        LicensePo licensePo = licenseDAO.get(tenantId, AppCodeEnum.TPM.code());
        return Objects.nonNull(licensePo);
    }

    private boolean existTPMLimitTenant(Integer tenantId) {
        LicensePo licensePo = licenseDAO.get(tenantId, AppCodeEnum.TPM_LIMIT.code());
        return Objects.nonNull(licensePo);
    }

    private boolean isNewTPMCodeTenant(Integer tenantId) {
        LicensePo license = licenseDAO.get(tenantId, AppCodeEnum.TPM_CODE.code());
        logger.info("license tpm_code : {}", license);
        return license == null;
    }

    private void addGrayTenantId(Integer tenantId) {
        Map<String, String> configMap = configService.get("variables_fmcg_gray");
        String eaGrayList = configMap.getOrDefault("ea_list_fmcg_crm_gray", "");
        String eiGrayList = configMap.getOrDefault("ei_list_fmcg_tpm_gray", "");
        String describeGrayList = configMap.getOrDefault("ei_list_fmcg_tpm_gray_describe", "");
        String tenantAccount = eieaConverter.enterpriseIdToAccount(tenantId);
        logger.info("gray ea list:{}", eaGrayList);
        logger.info("gray ei list:{}", eiGrayList);
        logger.info("gray describe list:{}", describeGrayList);
        if (Strings.isNullOrEmpty(eaGrayList) || Strings.isNullOrEmpty(eiGrayList)) {
            throw new RuntimeException("获取路由灰度列表失败");
        }
        Map<String, String> updateMap = new HashMap<>();
        if (!eaGrayList.contains(tenantAccount)) {
            if (eaGrayList.endsWith(",") || Strings.isNullOrEmpty(eaGrayList)) {
                eaGrayList += tenantAccount;
            } else {
                eaGrayList += ',' + tenantAccount;
            }
            updateMap.put("ea_list_fmcg_crm_gray", eaGrayList);
        }
        if (!eiGrayList.contains(String.valueOf(tenantId))) {
            if (eiGrayList.endsWith(",") || Strings.isNullOrEmpty(eiGrayList)) {
                eiGrayList += tenantId;
            } else {
                eiGrayList += "," + tenantId;
            }
            updateMap.put("ei_list_fmcg_tpm_gray", eiGrayList);
        }
        if (!describeGrayList.contains(String.valueOf(tenantId))) {
            if (describeGrayList.endsWith(",") || Strings.isNullOrEmpty(describeGrayList)) {
                describeGrayList += ("\"" + tenantId + "\"");
            } else {
                describeGrayList += "," + ("\"" + tenantId + "\"");
            }
            updateMap.put("ei_list_fmcg_tpm_gray_describe", describeGrayList);
        }
        if (!MapUtils.isNullOrEmpty(updateMap)) {
            configService.updateMulti("variables_fmcg_gray", updateMap);
        }
    }

    private void addGrayWebTenantId(Integer tenantId) {
        String ea = eieaConverter.enterpriseIdToAccount(tenantId);
        Map<String, String> configMap = configService.get("variables_fmcg_gray");
        String eaGrayList = configMap.getOrDefault("ea_list_tpm2_gray", "");
        logger.info("web gray ea:{}", eaGrayList);
        if (Strings.isNullOrEmpty(eaGrayList)) {
            throw new RuntimeException("获取路由灰度列表失败");
        }
        if (!eaGrayList.contains(ea)) {
            if (eaGrayList.endsWith(",") || Strings.isNullOrEmpty(eaGrayList)) {
                eaGrayList += "'" + ea + "'";
            } else {
                eaGrayList += ",'" + ea + "'";
            }
            configService.update("variables_fmcg_gray", "ea_list_tpm2_gray", eaGrayList);
        }

        String env = System.getenv("ENVIRONMENT_TYPE");
        if ("foneshare".equals(env)) {
            logger.info("start gray foneshare");
            appendWeex(ea);
        } else if ("firstshare".equals(env) || "fstest".equals(env)) {
            logger.info("start gray 112");
            appendWeex(ea);
        }
    }

    private <T extends ArgBase> ApiArg<T> fromApiArg(Integer tenantId, String tenantAccount, T data) {
        ApiArg<T> apiArg = new ApiArg<>();
        apiArg.setTenantAccount(tenantAccount);
        apiArg.setTenantId(tenantId);
        apiArg.setUserId(-10000);
        apiArg.setData(data);
        return apiArg;
    }

    private void appendWeex(String ea) {
        UpdateGray.Arg arg = new UpdateGray.Arg();
        arg.setUpdateBy("TPM2");
        arg.setName("TPM2.0");
        arg.setEaList(Lists.newArrayList(ea));
        arg.setAppend(true);
        UpdateGray.Result result = weexConsoleProxy.updateGray(arg);
        if (!result.getSuccess()) {
            logger.info("update fail.arg:{},rst:{}", arg, result);
        }
    }

    @PreDestroy
    public void shutDown() {
        processor.close();
    }
}
