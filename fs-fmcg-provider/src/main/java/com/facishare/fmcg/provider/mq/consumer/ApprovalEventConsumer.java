package com.facishare.fmcg.provider.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.common.rocketmq.AutoConfRocketMQProcessor;
import com.facishare.fmcg.provider.dao.abstraction.DevicePhotoDetailDAO;
import com.facishare.fmcg.provider.dao.po.DevicePhotoDetailPO;
import com.facishare.fmcg.provider.distributed.RedisDistributedLock;
import com.facishare.fmcg.provider.impl.custom.YuanQiService;
import com.facishare.fmcg.provider.mq.model.ApprovalEventOBJ;
import com.fmcg.framework.http.PaasDataProxy;
import com.fmcg.framework.http.contract.paas.data.PaasDataIncrementUpdate;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;


@SuppressWarnings("Duplicates")
@Component
@Slf4j
public class ApprovalEventConsumer {

    public static final String APPEAL_KEY = "appeal__c";
    private static final Logger logger = LoggerFactory.getLogger(ApprovalEventConsumer.class);
    private static final String CONFIG_NAME = "fs-fmcg-framework-config";
    private static final String NAME_SERVER_KEY = "FS_FMCG_APPROVAL_EVENT_NAMESERVER";
    private static final String TOPIC_KEY = "FS_FMCG_APPROVAL_EVENT_TOPICS";
    private static final String GROUP_KEY = "FS_FMCG_APPROVAL_EVENT_GROUP";
    private AutoConfRocketMQProcessor processor;
    @Resource
    private DevicePhotoDetailDAO devicePhotoDetailDAO;
    @Resource
    private PaasDataProxy paasDataProxy;
    @Resource
    private RedisDistributedLock redisDistributedLock;

    @PostConstruct
    public void init() {
        logger.info("ApprovalEventConsumer start init.");
        MessageListenerConcurrently listener = (messages, context) -> {
            for (MessageExt messageExt : messages) {
                try {
                    processMessage(messageExt);
                } catch (Exception ex) {
                    logger.error("[ApprovalEventConsumer consumer error :" + messages + "]", ex);
                    return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        };
        try {
            processor = new AutoConfRocketMQProcessor(CONFIG_NAME, NAME_SERVER_KEY, GROUP_KEY, TOPIC_KEY, listener);
            processor.init();
        } catch (Exception e) {
            logger.error("init a mq consumer failed.", e);
        }

    }

    private void processMessage(MessageExt messageExt) {
        try {

            String dataJsonStr = new String(messageExt.getBody());
            ApprovalEventOBJ message = JSON.parseObject(dataJsonStr, ApprovalEventOBJ.class);
            if (YuanQiService.DEVICE_PHOTO_OBJ_NAME.equals(message.getTag())) {
                handle(message);
            }

        } catch (Exception ex) {
            logger.error("[notice consumer error]", ex);
            throw ex;
        }
    }

    public void handle(ApprovalEventOBJ message) {
        String eventType = message.getEventType();
        if (!"instance_complete".equals(eventType)) {
            return;
        }
        log.info("approval message:{}", message);
        String dataId = message.getEventData().getString("dataId");
        int tenantId = message.getEventData().getIntValue("tenantId");
        String status = message.getEventData().getString("status");
        int userId = message.getEventData().getIntValue("userId");
        JSONObject callbackData = message.getEventData().getJSONObject("callbackData");

        switch (status) {
            case "pass": {
                handlePaas(tenantId, dataId, callbackData);
                break;
            }
            case "in_progress": {
                break;
            }
            case "reject":
            case "cancel": {
                handleReject(tenantId, dataId, userId, callbackData);
                break;
            }
            default:
        }
    }

    private void handlePaas(Integer tenantId, String id, JSONObject callbackData) {
        //判断是不是申诉触发的审批MQ，后台审批触发MQ的时候会带上 modify_number__c字段
        if (!CollectionUtils.isEmpty(callbackData) && callbackData.containsKey("modify_number__c")) {
            //修改主对象排面数、SKU数
            String key = String.format("DEVICE_PHOTO_LOCK_%s_%s", tenantId, id);
            String uuid = UUID.randomUUID().toString();
            if (redisDistributedLock.tryLock(key, uuid)) {
                try {
                    Map<String, Object> update = Maps.newHashMap();
                    update.put("_id", id);

                    update.put(APPEAL_KEY, true);

                    PaasDataIncrementUpdate.Arg updateArg = new PaasDataIncrementUpdate.Arg();
                    updateArg.setData(update);
                    PaasDataIncrementUpdate.Result updateResult = paasDataProxy.incrementUpdate(tenantId, -10000, YuanQiService.DEVICE_PHOTO_OBJ_NAME, updateArg);
                    log.info("update master data's appeal result:{},arg:{}", JSON.toJSONString(updateResult), JSON.toJSONString(updateArg));
                } finally {
                    redisDistributedLock.unlock(key, uuid);
                }
            }
        }
    }

    private void handleReject(Integer tenantId, String id, Integer userId, JSONObject callbackData) {
        if (!CollectionUtils.isEmpty(callbackData) && callbackData.containsKey("modify_number__c")) {

            List<DevicePhotoDetailPO> devicePhotoDetails = devicePhotoDetailDAO.getAndIgnoreDelete(tenantId, id);
            if (CollectionUtils.isEmpty(devicePhotoDetails)) {
                return;
            }
            Map<String, DevicePhotoDetailPO> finalDataInDb = devicePhotoDetails.stream().collect(Collectors.toMap(key -> key.getId().toString(), v -> v));
            for (DevicePhotoDetailPO devicePhotoDetail : devicePhotoDetails) {
                Long count = devicePhotoDetail.getCount();
                Long appealCount = devicePhotoDetail.getAppealCount();
                Boolean deleted = devicePhotoDetail.getDeleted();
                boolean flag = (count != null && appealCount != null) || (count != null && deleted != null && deleted);
                if (flag) {
                    devicePhotoDetailDAO.update(devicePhotoDetail.getId().toString(), userId);
                } else if (count == null && appealCount != null) {
                    devicePhotoDetailDAO.delete(devicePhotoDetail.getId().toString());
                    finalDataInDb.remove(devicePhotoDetail.getId().toString());
                }
            }


            //修改主对象排面数、SKU数
            Map<String, Object> update = Maps.newHashMap();
            update.put("_id", id);
            update.put("total_sku__c", finalDataInDb.size());


            Long totalCount = finalDataInDb.values().stream().map(DevicePhotoDetailPO::getCount).reduce(0L, Long::sum);
            update.put("total_num__c", totalCount);

            //申诉被拒绝之后申诉的数量置为空
            update.put("modify_number__c", "");

            PaasDataIncrementUpdate.Arg updateArg = new PaasDataIncrementUpdate.Arg();
            updateArg.setData(update);
            PaasDataIncrementUpdate.Result updateResult = paasDataProxy.incrementUpdate(tenantId, userId, YuanQiService.DEVICE_PHOTO_OBJ_NAME, updateArg);
            log.info("update master data's skuCount and totalNum result:{}", JSON.toJSONString(updateResult));
        }
    }

    @PreDestroy
    public void shutDown() {
        processor.shutDown();
    }
}
