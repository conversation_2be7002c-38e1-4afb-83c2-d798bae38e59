package com.facishare.fmcg.provider.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.fmcg.adapter.metadata.FieldDescribeAdapter;
import com.facishare.fmcg.adapter.metadata.dto.field.Add;
import com.facishare.fmcg.api.error.FmcgException;
import com.facishare.fmcg.provider.business.abstraction.FieldBusiness;
import com.facishare.fmcg.provider.dao.abstraction.LicenseDAO;
import com.facishare.fmcg.provider.dao.po.LicensePo;
import com.facishare.fmcg.provider.mq.model.FundAccountMessage;
import com.fmcg.framework.http.PaasDataProxy;
import com.fmcg.framework.http.PaasDescribeProxy;
import com.fmcg.framework.http.contract.paas.data.PaasFundAccountInit;
import com.fmcg.framework.http.contract.paas.describe.PaasDescribeGet;
import com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2021/2/4 下午3:32
 */
@Component
public class PaasFundAccountConsumer {

    private static final Logger logger = LoggerFactory.getLogger(PaasFundAccountConsumer.class);
    //todo:config change
    private static final String CONFIG_NAME = "rocketmq-consumer.ini";
    private static final String SECTION_NAME = "common,name_server_02,consumer_customer_account_fmcg_service";
    private AutoConfMQPushConsumer processor;
    @Resource
    private LicenseDAO licenseDAO;
    @Resource
    private PaasDataProxy paasDataProxy;
    @Resource
    private PaasDescribeProxy paasDescribeProxy;
    @Resource
    private FieldDescribeAdapter fieldDescribeAdapter;
    @Resource
    private FieldBusiness fieldBusiness;

    @PostConstruct
    public void init() {
        logger.info("PaasFundAccountConsumer start init.");
        MessageListenerConcurrently listener = (messages, context) -> {
            for (MessageExt messageExt : messages) {
                try {
                    TraceContext.get().setTraceId(UUID.randomUUID().toString());
                    process(messageExt);
                } catch (Exception ex) {
                    logger.error("[PaasFundAccountConsumer consumer error :" + messages + "]", ex);
                    return ConsumeConcurrentlyStatus.RECONSUME_LATER;
                } finally {
                    TraceContext.remove();
                }
            }
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        };
        try {
            processor = new AutoConfMQPushConsumer(CONFIG_NAME, SECTION_NAME, listener);
            processor.start();
        } catch (Exception e) {
            logger.error("init PaasFundAccountConsumer mq consumer failed.", e);
        }
    }

    private void process(MessageExt messageExt) {
        FundAccountMessage message = JSON.parseObject(messageExt.getBody(), FundAccountMessage.class);
        if (messageExt.getReconsumeTimes() > 5) {
            logger.info("reach max err time.message。msg:{}", message);
            return;
        }
        logger.info("customer mq:{}", message);
        int tenantId = Integer.parseInt(message.getTenantId());
        List<LicensePo> licensePos = licenseDAO.query(tenantId, Lists.newArrayList("FMCG.TPM", "FMCG.TPM.2"));
        if (CollectionUtils.isEmpty(licensePos)) {
            logger.info("tenant:{} has not open tpm switch.", tenantId);
            return;
        }
        PaasDescribeGet.Result describeRst = paasDescribeProxy.get(tenantId, -10000, "TPMDealerActivityCostObj");
        if (describeRst.getCode() != 0) {
            logger.info("get TPMDealerActivityCostObj rst:{}", describeRst);
        }
        if (describeRst.getCode() == 0) {
            logger.info("fund account message:{}", message);
            PaasFundAccountInit.Arg arg = new PaasFundAccountInit.Arg();
            arg.setObjectApiNames(Lists.newArrayList("TPMDealerActivityCostObj"));
            PaasFundAccountInit.Result result = paasDataProxy.initFoundAccount(tenantId, -10000, arg);
            if (result.getErrCode() != 0) {
                logger.info("open fund account fail,arg:{} result:{}", arg, result);
                throw new FmcgException("open fund account fail." + result.getErrMessage(), result.getErrCode());
            }
        }

        Map<String, JSONObject> fields = describeRst.getData().getDescribe().getFields();
        JSONObject cashingFundAccountFiled = fields.get("cashing_fund_account_id");
        if (cashingFundAccountFiled == null) {
            try {
                Add.Result createFieldResult = fieldDescribeAdapter.create(tenantId, fieldBusiness.buildFieldArg(tenantId, "TPMDealerActivityCostObj", "cashing_fund_account_id", true, false));
                if (createFieldResult.getErrCode() != 0) {
                    logger.error("init cashing_fund_account_id field error,tenantId:{},msg:{}", tenantId, createFieldResult.getErrCode());
                }
            } catch (Exception ex) {
                logger.error("init cashing_fund_account_id field error,tenantId:{}", tenantId, ex);
            }
        }
    }


    @PreDestroy
    public void shutDown() {
        processor.close();
    }
}
